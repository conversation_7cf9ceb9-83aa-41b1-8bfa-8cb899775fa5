<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Rinwriter - 研发报告生成器</title>
    <!-- 引入Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加网站图标 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📊</text></svg>">
    <style>
        :root {
            /* 主色调 - 现代绿色系 */
            --primary-color: #10b981; /* 更鲜明的绿色 */
            --primary-hover: #059669; /* 深绿色 */
            --primary-light: #d1fae5; /* 浅绿色 */
            --primary-dark: #047857; /* 暗绿色 */

            /* 辅助色 */
            --secondary-color: #3b82f6; /* 蓝色 */
            --secondary-hover: #2563eb; /* 深蓝色 */
            --accent-color: #8b5cf6; /* 紫色强调色 */

            /* 文本和背景色 */
            --text-color: #f3f4f6; /* 亮文本颜色，适合深色背景 */
            --text-color-dark: #1f2937; /* 深色文本，用于浅色背景 */
            --text-color-muted: #9ca3af; /* 次要文本颜色 */
            --bg-dark: #111827; /* 深色背景 */
            --bg-card: rgba(255, 255, 255, 0.08); /* 卡片背景 */

            /* 功能色 */
            --success-color: #10b981; /* 成功绿色 */
            --warning-color: #f59e0b; /* 警告黄色 */
            --error-color: #ef4444; /* 错误红色 */
            --info-color: #3b82f6; /* 信息蓝色 */

            /* 进度条 */
            --progress-color: #10b981;
            --progress-background: rgba(255, 255, 255, 0.1);

            /* 边框和阴影 */
            --border-color: rgba(255, 255, 255, 0.1);
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            --box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.2);
            --glass-border: 1px solid rgba(255, 255, 255, 0.1);

            /* 圆角和过渡 */
            --border-radius-sm: 6px;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 24px;
            --border-radius-full: 9999px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            /* 间距 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-md);
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;

            /* 现代渐变背景 */
            background-color: #0f172a; /* 深蓝黑色背景 */
            background-image:
                radial-gradient(at 40% 20%, hsla(167, 85%, 40%, 0.15) 0px, transparent 50%),
                radial-gradient(at 80% 0%, hsla(189, 85%, 40%, 0.1) 0px, transparent 50%),
                radial-gradient(at 0% 50%, hsla(355, 85%, 40%, 0.05) 0px, transparent 50%),
                radial-gradient(at 80% 50%, hsla(340, 85%, 40%, 0.05) 0px, transparent 50%),
                radial-gradient(at 0% 100%, hsla(22, 85%, 40%, 0.1) 0px, transparent 50%),
                radial-gradient(at 80% 100%, hsla(242, 85%, 40%, 0.1) 0px, transparent 50%),
                radial-gradient(at 0% 0%, hsla(343, 85%, 40%, 0.1) 0px, transparent 50%);
            background-attachment: fixed;
            background-size: cover;
        }

        /* 添加网格线背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
            opacity: 0.5;
        }

        /* 添加动态背景元素 - 优化版本 */
        .floating-element {
            position: fixed;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0) 70%);
            z-index: -1;
            animation: floatElement 30s ease-in-out infinite;
            pointer-events: none;
            filter: blur(3px);
            opacity: 0.5;
            will-change: transform;
            transform: translateZ(0);
        }

        .floating-element:nth-child(1) {
            top: 15%;
            left: 10%;
            width: 250px;
            height: 250px;
            animation-delay: 0s;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0) 70%);
        }

        .floating-element:nth-child(2) {
            top: 60%;
            left: 85%;
            width: 200px;
            height: 200px;
            animation-delay: -5s;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0) 70%);
        }

        .floating-element:nth-child(3) {
            top: 80%;
            left: 30%;
            width: 180px;
            height: 180px;
            animation-delay: -10s;
            background: radial-gradient(circle, rgba(139, 92, 246, 0.05) 0%, rgba(139, 92, 246, 0) 70%);
        }

        /* 减少浮动元素数量和动画复杂度 */
        @media (max-width: 768px) {
            .floating-element {
                opacity: 0.3;
                animation: floatElementMobile 40s ease-in-out infinite;
            }

            .floating-element:nth-child(3) {
                display: none; /* 在小屏幕上隐藏第三个元素 */
            }
        }

        @keyframes floatElement {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(15px, -15px) rotate(60deg); }
            50% { transform: translate(30px, 0px) rotate(120deg); }
            75% { transform: translate(15px, 15px) rotate(180deg); }
            100% { transform: translate(0, 0) rotate(0deg); }
        }

        /* 移动设备上使用更简单的动画 */
        @keyframes floatElementMobile {
            0% { transform: translate(0, 0); }
            50% { transform: translate(15px, -15px); }
            100% { transform: translate(0, 0); }
        }

        .header {
            text-align: center;
            padding: 25px 0;
            background: transparent; /* 透明背景 */
            position: relative;
            z-index: 1000;
            margin: 0 auto;
            transition: var(--transition);
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: auto; /* 自适应高度 */
        }

        .logout-button {
            position: fixed;
            right: 20px;
            top: 20px;
            padding: 8px 16px;
            background: rgba(29, 209, 161, 0.15); /* 半透明绿色背景 */
            color: #1dd1a1; /* 绿色文字 */
            border: 1px solid rgba(29, 209, 161, 0.3);
            border-radius: var(--border-radius-full);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            z-index: 1001; /* 确保在标题上方 */
        }

        .logout-button:hover {
            background: rgba(29, 209, 161, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(29, 209, 161, 0.5);
        }

        .logout-button i {
            font-size: 14px;
        }

        /* 添加一个占位元素，为标题和内容提供间距 */
        .header-placeholder {
            height: 20px; /* 减小高度，因为header不再是固定定位 */
            margin-bottom: 10px;
        }

        .header:hover {
            box-shadow: var(--box-shadow-hover);
            /* 移除transform效果，因为header现在是固定的 */
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 0;
            margin-top: 0;
            font-weight: 700;
            letter-spacing: 2px;
            color: #1dd1a1; /* 使用亮绿色 */
            display: inline-block;
            font-family: 'Nunito', sans-serif;
            position: relative;
            padding: 0 var(--spacing-md);
            /* 静态3D文字效果 */
            text-shadow:
                0 1px 0 rgba(0, 0, 0, 0.4),
                0 2px 0 rgba(0, 0, 0, 0.3),
                0 3px 0 rgba(0, 0, 0, 0.2),
                0 4px 5px rgba(0, 0, 0, 0.5),
                0 0 15px rgba(29, 209, 161, 0.5),
                0 0 25px rgba(29, 209, 161, 0.3);
        }

        /* 移除标题前后的装饰元素，使其更简洁 */

        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.3); opacity: 0.8; }
            100% { transform: scale(1); opacity: 0.5; }
        }

        .header p {
            color: var(--text-color-muted);
            font-size: 1.1rem;
            max-width: 600px;
            margin: var(--spacing-sm) auto 0;
            line-height: 1.6;
        }

        /* 进度相关样式已移至按钮进度条样式中 */

        /* 选项卡样式 */
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: var(--spacing-md);
            background-color: var(--bg-card);
            overflow: visible;
            padding: var(--spacing-md) var(--spacing-sm);
            border: var(--glass-border);
            border-radius: var(--border-radius-full);
            position: relative;
            z-index: 100;
            box-shadow: var(--box-shadow);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            margin: 0 auto;
            max-width: 800px;
            transition: var(--transition);
        }

        .tabs:hover {
            box-shadow: var(--box-shadow-hover);
        }

        .tab-button {
            padding: var(--spacing-sm) var(--spacing-md);
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            color: var(--text-color);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            transition: var(--transition);
            flex: 1;
            text-align: center;
            position: relative;
            border-radius: var(--border-radius-full);
            margin: 0 var(--spacing-xs);
            letter-spacing: 0.5px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
            z-index: 1;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            font-family: 'Nunito', sans-serif;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            z-index: -1;
            transition: var(--transition);
            transform: translateY(100%);
            opacity: 0;
        }

        .tab-button.active {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4), 0 0 0 2px rgba(16, 185, 129, 0.2);
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: 1px solid rgba(255, 255, 255, 0.4);
            position: relative;
            z-index: 2;
        }

        /* 添加发光效果 */
        .tab-button.active::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--border-radius-full);
            z-index: -1;
            filter: blur(8px);
            opacity: 0.4;
            animation: tab-pulse 2s infinite;
        }

        @keyframes tab-pulse {
            0% { opacity: 0.3; }
            50% { opacity: 0.5; }
            100% { opacity: 0.3; }
        }

        .tab-button:hover::before {
            transform: translateY(0);
            opacity: 1;
        }

        .tab-button:hover:not(.active) {
            transform: translateY(-2px);
            background: rgba(16, 185, 129, 0.3);
            box-shadow: 0 6px 15px rgba(16, 185, 129, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .tab-button i {
            margin-right: var(--spacing-xs);
            font-size: 15px;
            vertical-align: middle;
            transition: var(--transition);
            color: white;
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        }

        .tab-button:hover i {
            transform: translateX(-2px);
        }

        .tab-button.active i {
            transform: translateX(-2px) scale(1.1);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
            color: #ffffff;
            animation: tab-icon-pulse 2s infinite;
        }

        @keyframes tab-icon-pulse {
            0% { text-shadow: 0 0 8px rgba(255, 255, 255, 0.5); }
            50% { text-shadow: 0 0 15px rgba(255, 255, 255, 0.8); }
            100% { text-shadow: 0 0 8px rgba(255, 255, 255, 0.5); }
        }

        /* 提交按钮样式 */
        .submit-container {
            text-align: center;
            margin: var(--spacing-md) 0;
            position: relative;
            z-index: 200;
            padding: var(--spacing-sm) 0;
            margin-top: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            clear: both;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .submit-button {
            padding: var(--spacing-md) var(--spacing-xl);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            border-radius: var(--border-radius-full);
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: var(--transition);
            margin: 0 auto;
            display: none;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3), 0 0 0 2px rgba(16, 185, 129, 0.1);
            position: relative;
            overflow: hidden;
            z-index: 1;
            font-family: 'Nunito', sans-serif;
            min-width: 200px;
            text-align: center;
        }

        .submit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            z-index: -1;
            transition: var(--transition);
            transform: translateY(100%);
            opacity: 0;
        }

        .submit-button.active {
            display: inline-block;
        }

        .submit-button:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 12px 25px rgba(16, 185, 129, 0.4), 0 0 0 4px rgba(16, 185, 129, 0.15);
        }

        .submit-button:hover::before {
            transform: translateY(0);
            opacity: 1;
        }

        .submit-button:active {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
        }

        .submit-button i {
            margin-right: var(--spacing-sm);
            transition: var(--transition);
            position: relative;
        }

        .submit-button:hover i {
            transform: translateX(-3px);
        }

        /* 添加按钮发光效果 */
        .submit-button::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--border-radius-full);
            z-index: -2;
            filter: blur(10px);
            opacity: 0;
            transition: var(--transition);
        }

        .submit-button:hover::after {
            opacity: 0.5;
            animation: button-pulse 2s infinite;
        }

        @keyframes button-pulse {
            0% { opacity: 0.3; filter: blur(10px); }
            50% { opacity: 0.5; filter: blur(15px); }
            100% { opacity: 0.3; filter: blur(10px); }
        }

        /* 按钮基础样式 */
        .submit-button {
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .button-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
        }

        .button-content i {
            margin-right: 5px;
        }

        /* 按钮进度条样式 */
        .submit-button.processing {
            cursor: default;
            background: linear-gradient(135deg, #035e43, #024731) !important;
            transform: none !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
            border: none !important;
            color: white !important;
        }

        .submit-button.processing:hover {
            transform: none !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
            background: linear-gradient(135deg, #035e43, #024731) !important;
        }

        .submit-button.processing::before {
            display: none !important;
        }

        .submit-button.processing::after {
            display: none !important;
        }

        .progress-indicator {
            position: absolute;
            top: 2px; /* 稍微下移，增加立体感 */
            left: 2px; /* 稍微右移，增加立体感 */
            height: calc(100% - 4px); /* 减小高度，增加内边距效果 */
            width: 0%;
            background: linear-gradient(90deg, rgba(249, 115, 22, 0.9), rgba(239, 68, 68, 0.9)); /* 橙红渐变 */
            background-image:
                linear-gradient(90deg, rgba(249, 115, 22, 0.9), rgba(239, 68, 68, 0.9)),
                linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.1) 100%); /* 添加垂直渐变增加3D效果 */
            z-index: 1;
            transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow:
                0 0 20px rgba(249, 115, 22, 0.8), /* 外发光 */
                inset 0 1px 2px rgba(255, 255, 255, 0.5), /* 顶部高光 */
                inset 0 -1px 2px rgba(0, 0, 0, 0.2); /* 底部阴影 */
            animation: progress-glow 1.5s infinite alternate, progress-pulse 2s infinite;
            pointer-events: none;
            border-radius: calc(var(--border-radius-full) - 2px);
            overflow: hidden;
            transform: translateZ(0); /* 启用硬件加速 */
        }

        /* 进度条内部3D效果 */
        .progress-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            border-radius: calc(var(--border-radius-full) - 2px) calc(var(--border-radius-full) - 2px) 0 0;
        }

        /* 进度条底部阴影效果 */
        .progress-indicator::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0));
            border-radius: 0 0 calc(var(--border-radius-full) - 2px) calc(var(--border-radius-full) - 2px);
        }

        @keyframes progress-glow {
            from { box-shadow: 0 0 15px rgba(249, 115, 22, 0.6), inset 0 1px 2px rgba(255, 255, 255, 0.5), inset 0 -1px 2px rgba(0, 0, 0, 0.2); }
            to { box-shadow: 0 0 30px rgba(239, 68, 68, 1), inset 0 1px 3px rgba(255, 255, 255, 0.7), inset 0 -1px 3px rgba(0, 0, 0, 0.3); }
        }

        @keyframes progress-pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        /* 进度条活跃状态 */
        .submit-button.processing .progress-indicator {
            background-image:
                linear-gradient(90deg, rgba(249, 115, 22, 0.9), rgba(239, 68, 68, 0.9)),
                linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.1) 100%);
            box-shadow:
                0 0 20px rgba(249, 115, 22, 0.8),
                inset 0 1px 2px rgba(255, 255, 255, 0.5),
                inset 0 -1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 进度条活跃动画 */
        .progress-indicator.progress-active {
            animation: progress-glow 1s infinite alternate, progress-pulse 1.5s infinite !important;
        }

        /* 取消按钮样式 */
        .cancel-generation {
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background-color: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            z-index: 10;
        }

        .submit-button.processing:hover .cancel-generation {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }

        .cancel-generation:hover {
            background-color: rgba(220, 38, 38, 1);
            transform: translateY(-50%) scale(1.2) !important;
        }

        /* 进度条完成状态 */
        .progress-indicator[style*="width: 100%"] {
            background-image:
                linear-gradient(90deg, rgba(251, 146, 60, 0.9), rgba(249, 115, 22, 0.9)),
                linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.15) 100%) !important;
            box-shadow:
                0 0 30px rgba(249, 115, 22, 1),
                inset 0 1px 3px rgba(255, 255, 255, 0.7),
                inset 0 -1px 3px rgba(0, 0, 0, 0.3) !important;
        }

        /* 按钮悬停时进度条效果增强 */
        .submit-button:hover .progress-indicator {
            filter: brightness(1.05);
        }

        /* 移除不再需要的progress-shine动画 */

        .progress-text {
            position: relative;
            z-index: 2;
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            color: white;
            display: block;
            width: 100%;
            text-align: center;
            pointer-events: none;
        }

        /* 确保按钮内容在进度条上方 */
        .submit-button i,
        .submit-button span {
            position: relative;
            z-index: 2;
        }

        /* 下载按钮样式 - From Uiverse.io by Na3ar-17 */
        .download-container {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            font-family: Arial, Helvetica, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .download-button {
            background-color: transparent;
            border: 2px solid rgb(91, 91, 240);
            display: flex;
            align-items: center;
            border-radius: 50px;
            width: 120px;
            cursor: pointer;
            transition: all 0.4s ease;
            padding: 4px;
            position: relative;
            margin: 0 auto;
        }

        .download-button::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            width: 8px;
            height: 8px;
            transition: all 0.4s ease;
            border-radius: 100%;
            margin: auto;
            opacity: 0;
            visibility: hidden;
        }

        .download-button .input {
            display: none;
        }

        .download-button .title {
            font-size: 14px;
            color: #fff;
            transition: all 0.4s ease;
            position: absolute;
            right: 14px;
            bottom: 11px;
            text-align: center;
        }

        .download-button .title:last-child {
            opacity: 0;
            visibility: hidden;
        }

        .download-button .circle {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            background-color: rgb(91, 91, 240);
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.4s ease;
            position: relative;
            box-shadow: 0 0 0 0 rgb(255, 255, 255);
            overflow: hidden;
        }

        .download-button .circle .icon {
            color: #fff;
            width: 22px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
        }

        .download-button .circle .square {
            aspect-ratio: 1;
            width: 12px;
            border-radius: 2px;
            background-color: #fff;
            opacity: 0;
            visibility: hidden;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
        }

        .download-button .circle::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            background-color: #3333a8;
            width: 100%;
            height: 0;
            transition: all 0.4s ease;
        }

                         .download-button.downloading {
            width: 42px;
            animation: installed 0.4s ease 3.5s forwards;
        }

         .download-button.downloading::before {
             animation: rotate 3s ease-in-out 0.4s forwards;
         }

                 .download-button.downloading .circle {
             animation:
                 pulse 1s forwards,
                 circleDelete 0.2s ease 3.5s forwards;
             rotate: 180deg;
         }

         .download-button.downloading .circle::before {
             animation: installing 3s ease-in-out forwards;
         }

         .download-button.downloading .circle .icon {
             opacity: 0;
             visibility: hidden;
         }

         .download-button.downloading .circle .square {
             opacity: 1;
             visibility: visible;
         }

         .download-button.downloading .title {
             opacity: 0;
             visibility: hidden;
         }

         .download-button.downloading .title:last-child {
             animation: showInstalledMessage 0.4s ease 3.5s forwards;
         }

        @keyframes pulse {
            0% {
                scale: 0.95;
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
            }
            70% {
                scale: 1;
                box-shadow: 0 0 0 16px rgba(255, 255, 255, 0);
            }
            100% {
                scale: 0.95;
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        @keyframes installing {
            from {
                height: 0;
            }
            to {
                height: 100%;
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(-90deg) translate(20px) rotate(0);
                opacity: 1;
                visibility: visible;
            }
            99% {
                transform: rotate(270deg) translate(20px) rotate(270deg);
                opacity: 1;
                visibility: visible;
            }
            100% {
                opacity: 0;
                visibility: hidden;
            }
        }

        @keyframes installed {
            100% {
                width: 110px;
                border-color: rgb(35, 174, 35);
            }
        }

        @keyframes circleDelete {
            100% {
                opacity: 0;
                visibility: hidden;
            }
        }

        @keyframes showInstalledMessage {
            100% {
                opacity: 1;
                visibility: visible;
                right: 42px;
            }
        }

        /* 下载按钮禁用状态 */
        .download-button.disabled {
            cursor: not-allowed;
            opacity: 0.5;
            border-color: rgb(120, 120, 120);
        }

        .download-button.disabled .circle {
            background-color: rgb(120, 120, 120);
        }

        .download-button.disabled:hover {
            transform: none;
        }

        /* 下载按钮启用状态 */
        .download-button.enabled {
            cursor: pointer;
            opacity: 1;
            border-color: rgb(91, 91, 240);
        }

        .download-button.enabled .circle {
            background-color: rgb(91, 91, 240);
        }

        /* 按钮完成动画 */
        .button-completed {
            animation: button-completed-pulse 1s ease;
        }

        @keyframes button-completed-pulse {
            0% { transform: scale(1); box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3); }
            50% { transform: scale(1.05); box-shadow: 0 12px 25px rgba(16, 185, 129, 0.6); }
            100% { transform: scale(1); box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3); }
        }

        /* 表单内容样式 */
        .tab-content {
            display: none;
            background-color: var(--bg-card);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow);
            border: var(--glass-border);
            margin-bottom: var(--spacing-lg);
            counter-reset: section;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .tab-content:hover {
            box-shadow: var(--box-shadow-hover);
        }

        /* 添加装饰元素 */
        .tab-content::before {
            content: '';
            position: absolute;
            top: -80px;
            right: -80px;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.08) 0%, rgba(16, 185, 129, 0) 70%);
            border-radius: 50%;
            z-index: -1;
            opacity: 0.6;
        }

        .tab-content::after {
            content: '';
            position: absolute;
            bottom: -100px;
            left: -100px;
            width: 250px;
            height: 250px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0) 70%);
            border-radius: 50%;
            z-index: -1;
            opacity: 0.5;
        }

        /* 页脚样式 */
        .footer {
            width: 200px;
            margin: var(--spacing-lg) auto;
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: rgba(15, 23, 42, 0.6);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: var(--glass-border);
            text-align: center;
            z-index: 100;
            transition: var(--transition);
        }

        .footer:hover {
            box-shadow: var(--box-shadow-hover);
            transform: translateY(-2px);
        }

        .footer-content {
            position: relative;
            z-index: 2;
            width: fit-content;
            margin: 0 auto;
        }

        .footer p {
            color: var(--text-color);
            font-size: 11px;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            white-space: nowrap;
            letter-spacing: -0.2px;
            font-family: 'Nunito', sans-serif;
        }

        /* 简化装饰元素 */
        .tab-content::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0) 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .form-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition);
        }

        .form-group:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }

        .form-group:last-child {
            border-bottom: none;
        }

        label {
            font-weight: 600;
            min-width: 180px;
            color: var(--text-color);
            text-align: right;
            padding-right: var(--spacing-md);
            font-size: 14px;
            letter-spacing: 0.3px;
            font-family: 'Nunito', sans-serif;
            transition: var(--transition);
        }

        .form-group:hover label {
            color: white;
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        }

        input[type="file"], input[type="date"], input[type="text"], input[type="number"], select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            flex: 1;
            min-width: 300px;
            font-family: inherit;
            transition: var(--transition);
            background-color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin: var(--spacing-xs) 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        input[type="text"]:hover, input[type="date"]:hover, input[type="number"]:hover, select:hover {
            border-color: var(--primary-color);
            background-color: white;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .file-input-container {
            display: flex;
            align-items: center;
            flex: 1;
            gap: var(--spacing-sm);
            position: relative;
            width: 100%;
        }

        /* 优化文件输入框性能 */
        input[type="file"] {
            padding: var(--spacing-md);
            background-color: rgba(255, 255, 255, 0.05);
            border: 2px dashed rgba(16, 185, 129, 0.4);
            border-radius: var(--border-radius);
            flex: 1;
            cursor: pointer;
            color: var(--text-color);
            /* 简化过渡效果，只对必要属性应用过渡 */
            transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            font-size: 13px;
            /* 移动设备上禁用模糊效果，提高性能 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 100%;
            margin: var(--spacing-xs) 0;
            /* 添加硬件加速 */
            will-change: background-color, border-color;
            z-index: 1; /* 降低z-index，确保不会遮挡其他元素 */
        }

        /* 在支持的设备上启用模糊效果 */
        @supports (backdrop-filter: blur(10px)) {
            input[type="file"] {
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
            }
        }

        /* 文件输入容器样式 - 针对已上传文件的容器 - 移除背景色 */
        .file-input-container.has-file {
            position: relative;
            overflow: hidden;
        }

        /* 文件已上传状态 - 显著增强视觉反馈 - 只填充虚线框体 */
        input[type="file"].has-file {
            background-color: rgba(16, 185, 129, 0.9) !important; /* 使用!important确保样式应用，进一步增加不透明度 */
            border: 3px solid var(--primary-color) !important; /* 加粗边框并确保应用 */
            color: white !important; /* 确保文字颜色应用 */
            position: relative;
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.8) !important; /* 显著增强阴影效果 */
            transform: translateY(-2px);
            font-weight: bold !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        /* 移除发光效果 */
        input[type="file"].has-file::after {
            content: none; /* 禁用伪元素，移除绿色线条 */
        }

        @keyframes pulse-border {
            0% { opacity: 0.6; box-shadow: 0 0 5px rgba(16, 185, 129, 0.3); }
            50% { opacity: 1; box-shadow: 0 0 20px rgba(16, 185, 129, 0.6); }
            100% { opacity: 0.6; box-shadow: 0 0 5px rgba(16, 185, 129, 0.3); }
        }

        /* 添加内联动画样式 */
        .file-glow-effect {
            animation: pulse-border 2s infinite;
        }

        /* 添加已上传图标和背景效果 */
        .file-input-container {
            position: relative;
            transition: var(--transition);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        /* 移除容器背景渐变效果 */
        .file-input-container.has-file::before {
            content: none; /* 禁用伪元素 */
        }

        @keyframes glow-container {
            from { box-shadow: inset 0 0 10px rgba(16, 185, 129, 0.2); }
            to { box-shadow: inset 0 0 25px rgba(16, 185, 129, 0.4); }
        }

        .file-input-container.has-file::after {
            content: none; /* 移除绿色勾 */
        }

        @keyframes fadeInScale {
            from { opacity: 0; transform: translateY(-50%) scale(0.8); }
            to { opacity: 1; transform: translateY(-50%) scale(1); }
        }

        input[type="file"]:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
        }

        /* 无效字段样式 */
        .invalid-field {
            border-color: #e53e3e !important;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2) !important;
            background-color: rgba(254, 226, 226, 0.9) !important;
        }

        /* 抖动动画 */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake-animation {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }

        input[type="file"].has-file:hover {
            background-color: rgba(16, 185, 129, 0.2);
            border-color: var(--primary-dark);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        input[type="file"]::file-selector-button {
            background: var(--primary-color);
            color: white;
            padding: var(--spacing-xs) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius-full);
            cursor: pointer;
            margin-right: var(--spacing-sm);
            font-weight: 600;
            font-size: 12px;
            box-shadow: 0 2px 5px rgba(16, 185, 129, 0.2);
            font-family: 'Nunito', sans-serif;
            /* 简化过渡效果 */
            transition: background-color 0.2s ease, transform 0.2s ease;
        }

        input[type="file"]::file-selector-button:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
            background-color: white;
            transform: translateY(-1px);
        }

        /* 隐藏文件上传状态指示器 */
        .file-status {
            display: none;
        }

        .file-input-container.has-file .file-status {
            display: none;
        }

        .path-input-container {
            display: flex;
            align-items: center;
            flex: 1;
            flex-wrap: wrap;
        }

        .note {
            font-size: 12px;
            color: #6c757d;
            margin-top: 3px;
            width: 100%;
            margin-left: 190px; /* 调整为与输入框对齐，匹配新的标签宽度 */
            line-height: 1.4;
            font-style: italic;
        }

        .parse-rdps-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
            width: fit-content;
            min-width: 60px;
            height: 36px;
        }

        .parse-rdps-btn:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        }

        .parse-rdps-btn:active {
            transform: translateY(0);
        }

        .parse-rdps-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 报告部分样式 */
        .report-section {
            border: 1px solid #e9ecef;
            padding: 28px;
            margin-bottom: 35px;
            border-radius: 12px;
            background-color: #f9fafb;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .report-section:hover {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
            border-color: #d1d9e6;
        }

        .report-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), #3a56d4);
            opacity: 0.8;
        }

        .report-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.25rem;
            margin-bottom: 22px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .report-section h3 i {
            margin-right: 10px;
            color: #27ae60; /* 改为绿色 */
            font-size: 1.1em;
        }

        /* 折叠表单部分样式 */
        .form-section {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px; /* 减小圆角 */
            margin-bottom: 15px; /* 增加底部边距，使布局更均匀 */
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.15); /* 降低不透明度 */
            backdrop-filter: blur(10px); /* 减少模糊程度 */
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.15) inset; /* 减小阴影 */
            transition: all 0.2s ease; /* 加快过渡效果 */
            position: relative;
        }

        /* 添加深度效果 */
        .form-section::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 5%;
            width: 90%;
            height: 10px;
            background-color: rgba(0, 0, 0, 0.03);
            filter: blur(4px);
            border-radius: 50%;
            z-index: -1;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .form-section:hover::after {
            opacity: 0.9;
            bottom: -6px;
            filter: blur(5px);
        }

        .form-section:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }

        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@600;700&display=swap');

        .form-section summary {
            padding: 12px 15px; /* 减少内边距 */
            background: linear-gradient(135deg, rgba(225, 235, 245, 0.8), rgba(215, 225, 240, 0.8)); /* 降低不透明度 */
            backdrop-filter: blur(10px); /* 减少模糊程度 */
            -webkit-backdrop-filter: blur(10px);
            cursor: pointer;
            font-weight: 600; /* 减小字体粗细 */
            color: #0a1f38;
            position: relative;
            transition: all 0.2s ease; /* 加快过渡效果 */
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.3) inset; /* 减小阴影 */
            letter-spacing: 0.3px; /* 减小字母间距 */
            counter-increment: section;
            font-family: 'Nunito', sans-serif;
            border-radius: 8px; /* 减小圆角 */
            margin-bottom: 0;
            border: 1px solid rgba(255, 255, 255, 0.3); /* 降低边框不透明度 */
            z-index: 3;
            font-size: 14px; /* 减小字体大小 */
        }

        /* 添加标题悬停效果 */
        .form-section summary:hover {
            background: linear-gradient(135deg, rgba(220, 230, 245, 0.98), rgba(210, 220, 235, 0.98)); /* 保持深色 */
            color: #0a1f38;
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.18), 0 0 0 1px rgba(255, 255, 255, 0.5) inset;
        }

        /* 添加装饰元素 */
        .form-section {
            position: relative;
            margin-bottom: 15px; /* 增加底部边距，使布局更均匀 */
        }

        .form-section summary::before {
            content: "";
            margin-right: 0;
        }

        /* 删除旧的悬停效果 */

        .form-section summary i {
            margin-right: 12px;
            margin-left: 5px;
            font-size: 18px;
            color: #27ae60; /* 改为绿色 */
            transition: all 0.3s ease;
        }

        .form-section summary:hover i {
            transform: scale(1.1) rotate(5deg);
        }

        .form-section summary::marker,
        .form-section summary::-webkit-details-marker {
            display: none;
        }

        .form-section summary::after {
            content: '\f078'; /* Font Awesome 的向下箭头 */
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 15px;
            transition: transform 0.3s ease;
        }

        .form-section[open] summary::after {
            transform: rotate(180deg);
        }

        .form-section-content {
            padding: 15px !important; /* 增加内边距，使内容更均匀分布 */
            border-top: 1px solid rgba(255, 0, 0, 0.5) !important; /* 使用明显的红色边框 */
            background-color: rgba(0, 0, 0, 0.3) !important; /* 使用黑色背景，透明度更低 */
            backdrop-filter: blur(10px) !important; /* 适中的模糊效果 */
            -webkit-backdrop-filter: blur(10px) !important;
            transition: all 0.2s ease !important; /* 加快过渡效果 */
            border-radius: 0 0 8px 8px !important; /* 减小圆角 */
            position: relative !important;
            overflow: hidden !important;
            box-shadow: 0 4px 10px rgba(255, 0, 0, 0.5) !important; /* 使用明显的红色阴影 */
            border: 1px solid rgba(255, 0, 0, 0.5) !important; /* 使用明显的红色边框 */
            border-top: none !important;
            margin-top: -1px !important;
            text-align: left !important;
        }

        /* 增强层次感 */
        .form-section[open] {
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            transform: translateY(0);
            z-index: 2;
        }

        .form-section[open] summary {
            border-radius: 10px 10px 0 0;
            border-bottom: none;
            background: linear-gradient(135deg, rgba(210, 225, 245, 1), rgba(200, 215, 235, 1)); /* 完全不透明的背景 */
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.15);
            color: #061525; /* 极深的文字颜色 */
            font-weight: 700; /* 更粗的字体 */
            border: 1px solid rgba(255, 255, 255, 0.6);
            letter-spacing: 0.8px; /* 增加字母间距 */
        }

        /* 添加动态装饰元素 */
        .form-section-content::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0) 70%);
            border-radius: 50%;
            z-index: -1;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            50% { transform: translate(-20px, 20px) rotate(180deg); }
            100% { transform: translate(0, 0) rotate(360deg); }
        }

        .form-section[open] .form-section-content {
            animation: slideDown 0.3s ease forwards;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-15px);
                box-shadow: 0 0 0 rgba(0, 0, 0, 0);
            }
            to {
                opacity: 1;
                transform: translateY(0);
                /* 移除可能覆盖我们样式的阴影 */
            }
        }

        /* 改进表单内容对齐 */
        .form-section-content .form-group {
            display: grid;
            grid-template-columns: 180px 1fr auto; /* 减小标签宽度 */
            align-items: center;
            gap: 10px; /* 适当的元素间距 */
            padding: 8px 0; /* 适当的上下内边距 */
            border-bottom: 1px dashed rgba(222, 226, 230, 0.1); /* 进一步降低边框不透明度 */
            margin-bottom: 5px; /* 增加底部间距，使布局更均匀 */
            position: relative;
            transition: all 0.2s ease; /* 加快过渡效果 */
            background-color: rgba(255, 255, 255, 0.005); /* 进一步降低背景不透明度 */
            border-radius: 4px; /* 减小圆角 */
            border: 1px solid rgba(255, 255, 255, 0.01); /* 进一步降低边框不透明度 */
            width: 100%;
        }

        .form-section-content .form-group:hover {
            background-color: rgba(248, 250, 252, 0.02); /* 更加透明的悬停背景 */
            border-radius: 8px;
            padding-left: 10px;
            padding-right: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.005); /* 减小阴影 */
            border: 1px solid rgba(255, 255, 255, 0.03); /* 降低边框不透明度 */
            backdrop-filter: blur(20px); /* 增加模糊程度，增强毛玻璃效果 */
            -webkit-backdrop-filter: blur(20px);
        }

        .form-section-content .form-group:last-child {
            border-bottom: none;
        }

        /* 多项报告部分样式 */
        .multi-reports-section > summary {
            font-weight: 700;
            background: linear-gradient(135deg, rgba(210, 225, 245, 1), rgba(200, 215, 235, 1)); /* 完全不透明的背景 */
            padding-left: 20px;
            color: #061525; /* 极深的文字颜色 */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.6);
        }

        .multi-reports-section > summary i {
            color: #27ae60;
            font-size: 20px;
            margin-right: 15px;
        }

        .multi-reports-section > .form-section-content {
            padding: 35px;
            background-color: rgba(250, 250, 250, 0.0005); /* 极大降低不透明度，几乎完全透明 */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.0005) inset; /* 极大减小阴影 */
            backdrop-filter: blur(35px); /* 大幅增加模糊程度，极大增强毛玻璃效果 */
            -webkit-backdrop-filter: blur(35px);
            border: 1px solid rgba(255, 255, 255, 0.002); /* 极大降低边框不透明度 */
            border-top: none;
        }

        .report-section {
            margin-bottom: 15px; /* 减少底部边距 */
            padding: 15px; /* 减少内边距 */
            padding-bottom: 20px; /* 减少底部内边距 */
            border-bottom: 1px dashed rgba(200, 200, 200, 0.005); /* 极大降低边框不透明度 */
            background-color: rgba(255, 255, 255, 0.0005); /* 极大降低不透明度，几乎完全透明 */
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.0005); /* 极大减小阴影 */
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(35px); /* 大幅增加模糊程度，极大增强毛玻璃效果 */
            -webkit-backdrop-filter: blur(35px);
            border: 1px solid rgba(255, 255, 255, 0.002); /* 极大降低边框不透明度 */
        }

        .report-section:hover {
            background-color: rgba(255, 255, 255, 0.001); /* 极大降低不透明度，几乎完全透明 */
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.001); /* 极大减小阴影 */
            transform: translateY(-2px);
            border: 1px solid rgba(255, 255, 255, 0.005); /* 极大降低边框不透明度 */
            backdrop-filter: blur(40px); /* 大幅增加模糊程度，极大增强毛玻璃效果 */
            -webkit-backdrop-filter: blur(40px);
        }

        .report-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .report-section h3 {
            margin: 0 0 20px 0;
            padding: 12px 18px;
            font-size: 16px;
            color: #061525; /* 极深的文字颜色 */
            background: linear-gradient(135deg, rgba(210, 225, 245, 1), rgba(200, 215, 235, 1)); /* 完全不透明的背景 */
            border-radius: 8px;
            display: flex;
            align-items: center;
            font-weight: 700; /* 更粗的字体 */
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border-left: 3px solid #27ae60;
            letter-spacing: 0.8px; /* 增加字母间距 */
        }

        .report-section h3 i {
            margin-right: 12px;
            color: #27ae60;
            font-size: 18px;
        }

        .form-section-content label {
            width: 180px;
            min-width: 180px;
            text-align: right;
            padding-right: 15px;
            color: #e0e0e0; /* 使用浅灰色，不那么刺眼 */
            font-weight: 600;
            position: relative;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-family: 'Nunito', sans-serif;
            letter-spacing: 0.3px;
            text-shadow: 0 0 2px rgba(0, 150, 0, 0.4); /* 减弱阴影效果 */
        }

        /* 添加标签文本容器，确保文本对齐 */
        .form-section-content label::after {
            content: attr(for);
            display: none;
        }

        .form-section-content label span {
            white-space: normal;
            text-align: right;
            line-height: 1.4;
        }

        /* 特定标签样式调整 */
        label[for="revenue_2024"],
        label[for="percentage_2024"] {
            width: 220px;
            min-width: 220px;
        }

        .form-section-content label i {
            margin-right: 10px;
            color: #4ade80; /* 使用柔和的绿色 */
            font-size: 16px; /* 恢复适中的图标大小 */
            width: 20px; /* 恢复适中的宽度 */
            text-align: center;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            text-shadow: 0 0 2px rgba(0, 150, 0, 0.5); /* 减弱阴影效果 */
            filter: none; /* 移除额外的阴影效果 */
        }

        .form-section-content input[type="file"],
        .form-section-content input[type="date"],
        .form-section-content input[type="text"],
        .form-section-content input[type="number"],
        .form-section-content select,
        .form-section-content .file-input-container,
        .form-section-content .path-input-container {
            flex: 1;
            min-width: 250px;
        }

        .error-message {
            color: var(--error-color);
            font-size: 14px;
            margin-top: 5px;
            display: block;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 18px 22px;
            border-radius: 16px;
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
            display: flex;
            align-items: center;
            gap: 14px;
            z-index: 1000;
            max-width: 400px;
            animation: slideIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-left: 5px solid #3498db;
            transform-origin: top right;
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .notification.success {
            border-left: 5px solid var(--success-color);
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(46, 204, 113, 0.05));
        }

        .notification.error {
            border-left: 5px solid var(--error-color);
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
        }

        .notification.warning {
            border-left: 5px solid var(--warning-color);
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(243, 156, 18, 0.05));
        }

        .notification.info {
            border-left: 5px solid #3498db;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.05));
        }

        .notification i {
            font-size: 22px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.25);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .notification.success i {
            color: var(--success-color);
            background-color: rgba(46, 204, 113, 0.15);
        }

        .notification.error i {
            color: var(--error-color);
            background-color: rgba(231, 76, 60, 0.15);
        }

        .notification.warning i {
            color: var(--warning-color);
            background-color: rgba(243, 156, 18, 0.15);
        }

        .notification.info i {
            color: #3498db;
            background-color: rgba(52, 152, 219, 0.15);
        }

        .notification:hover i {
            transform: scale(1.1) rotate(5deg);
        }

        .notification span {
            flex: 1;
            font-size: 14px;
            line-height: 1.6;
            font-weight: 500;
            letter-spacing: 0.2px;
        }

        .notification button {
            background: rgba(0, 0, 0, 0.05);
            border: none;
            cursor: pointer;
            color: #777;
            padding: 0;
            font-size: 14px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .notification button:hover {
            color: #333;
            background-color: rgba(0, 0, 0, 0.1);
            transform: rotate(90deg);
        }

        .notification.fade-out {
            opacity: 0;
            transform: translateX(30px) scale(0.9);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* 清除按钮样式 - 更加明显 */
        .clear-file-btn {
            background-color: rgba(239, 68, 68, 0.8);
            color: #fff;
            border: 1px solid rgba(239, 68, 68, 0.9);
            border-radius: var(--border-radius-full);
            padding: var(--spacing-xs) var(--spacing-sm);
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            display: inline-flex !important; /* 强制显示，移除默认隐藏 */
            align-items: center;
            justify-content: center;
            min-width: 70px;
            margin-left: var(--spacing-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            font-family: 'Nunito', sans-serif;
            z-index: 30 !important; /* 确保清除按钮始终在最上层 */
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .clear-file-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            opacity: 0;
            transition: var(--transition);
        }

        .clear-file-btn:hover {
            background-color: rgba(239, 68, 68, 0.8);
            color: white;
            border-color: rgba(239, 68, 68, 0.9);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
            transform: translateY(-2px);
        }

        .clear-file-btn:hover::before {
            opacity: 1;
        }

        .clear-file-btn:active {
            background-color: rgba(239, 68, 68, 0.2);
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) inset;
        }

        .clear-file-btn i {
            margin-right: var(--spacing-xs);
            transition: var(--transition);
            color: white;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .clear-file-btn:hover i {
            transform: rotate(90deg);
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-group,
            .form-section-content .form-group {
                flex-direction: column;
                align-items: flex-start;
            }

            label,
            .form-section-content label {
                width: 100%;
                min-width: 100%;
                text-align: left;
                margin-bottom: 10px;
                padding-right: 0;
                justify-content: flex-start;
            }

            .form-section-content label span {
                text-align: left;
                white-space: normal;
            }

            .form-section-content label i {
                margin-right: 10px;
            }

            input[type="file"], input[type="date"], input[type="text"], input[type="number"], select,
            .form-section-content input[type="file"],
            .form-section-content input[type="date"],
            .form-section-content input[type="text"],
            .form-section-content input[type="number"],
            .form-section-content select,
            .form-section-content .file-input-container,
            .form-section-content .path-input-container {
                width: 100%;
                min-width: 100%;
            }

            .note {
                margin-left: 0;
                margin-top: 8px;
            }

            .tab-button {
                padding: 10px 15px;
                font-size: 14px;
            }

            .tab-button i {
                margin-right: 5px;
            }

            .file-input-container {
                width: 100%;
            }
        }

        /* 页脚样式 */
        footer.footer {
            text-align: center;
            margin-top: 20px; /* 增加顶部边距 */
            margin-bottom: 20px; /* 增加底部边距 */
            padding: 8px 0; /* 适当的上下内边距 */
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            position: relative; /* 相对定位 */
            border-radius: 12px;
            overflow: hidden;
            z-index: 10;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
            width: auto;
            max-width: 800px; /* 与导航栏宽度一致 */
            margin-left: auto;
            margin-right: auto;
            height: auto; /* 自适应高度 */
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .footer p {
            margin: 0;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 2;
            font-weight: 500;
            line-height: 1.2;
        }

        .footer-description {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        /* 主容器布局 */
        .main-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            padding-top: 5px; /* 减少顶部内边距 */
            padding-bottom: 5px; /* 减少底部内边距 */
            padding-left: 10px; /* 保持左侧内边距 */
            padding-right: 10px; /* 保持右侧内边距 */
            box-sizing: border-box;
            overflow-x: hidden;
        }

        @media (max-width: 768px) {
            .main-container {
                padding-left: 5px;
                padding-right: 5px;
            }
        }

        /* 侧边栏样式 */
        .sidebar {
            display: none; /* 隐藏侧边栏 */
        }

        /* 内容区域样式 */
        .content-area {
            width: 100%;
            padding-top: 5px; /* 减少顶部内边距 */
            padding-bottom: 5px; /* 减少底部内边距 */
            padding-left: 30px; /* 保持左侧内边距 */
            padding-right: 30px; /* 保持右侧内边距 */
            box-sizing: border-box;
        }

        @media (max-width: 768px) {
            .content-area {
                padding-left: 10px;
                padding-right: 10px;
            }
        }

        @media (max-width: 480px) {
            .content-area {
                padding-left: 5px;
                padding-right: 5px;
            }
        }

        /* 帮助部分样式已删除 */

        /* 响应式设计 */
        @media (max-width: 992px) {
            body {
                padding: 10px;
                max-width: 100%;
            }

            .sidebar {
                position: relative;
                top: 0;
                left: 0;
                width: 100%;
                margin-bottom: 20px; /* 增加底部外边距 */
            }

            .content-area {
                padding-top: 10px;
                padding-left: 10px;
                padding-right: 10px;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .tab-button {
                padding: 12px 20px;
                font-size: 14px;
            }

            .tab-button i {
                font-size: 16px;
                margin-right: 6px;
            }

            .submit-button {
                padding: 14px 28px;
                font-size: 15px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 5px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content-area {
                padding-left: 5px; /* 减小左侧内边距 */
                padding-right: 5px; /* 减小右侧内边距 */
            }

            .form-section-content {
                padding: 10px; /* 减小内边距 */
            }

            .tab-content {
                padding: 15px;
            }

            .tabs {
                flex-direction: column;
                padding: 8px;
            }

            .tab-button {
                margin-bottom: 8px;
                width: 100%;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
            }

            .form-group label {
                width: 100%;
                margin-bottom: 5px;
            }

            .form-group input,
            .form-group select,
            .file-input-container {
                width: 100%;
            }

            .submit-container {
                margin: 20px 0;
            }

            .submit-button {
                width: 100%;
                padding: 12px 20px;
            }
        }

        /* 特小屏幕设备 */
        @media (max-width: 480px) {
            body {
                padding: 5px;
                overflow-x: hidden;
            }

            .header {
                padding: var(--spacing-sm) 0;
                border-radius: 0; /* 移动设备上去掉圆角 */
            }

            .header-placeholder {
                height: 60px; /* 移动设备上调整占位高度 */
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .header h1::before {
                display: none; /* 隐藏装饰元素 */
            }

            .tab-content {
                padding: 10px;
            }

            .form-section summary {
                padding: 10px;
                font-size: 14px;
            }

            .form-group {
                padding: 8px 0;
            }

            .form-group label {
                font-size: 14px;
            }

            .clear-file-btn {
                padding: 4px 8px;
                font-size: 11px;
            }

            /* 修复表单布局 */
            .form-section-content .form-group {
                display: flex;
                flex-direction: column;
                grid-template-columns: none;
            }

            label {
                min-width: 100%;
                width: 100%;
                text-align: left;
                padding-right: 0;
                margin-bottom: 5px;
            }

            input[type="text"],
            input[type="number"],
            input[type="date"],
            select {
                min-width: 100%;
                width: 100%;
            }

            input[type="file"] {
                width: 100%;
            }

            .file-input-container {
                flex-direction: column;
                align-items: flex-start;
            }

            .file-input-container.has-file::after {
                right: 10px;
            }

            /* 调整进度条 */
            #progress-container {
                max-width: 100%;
            }

            /* 调整页脚 */
            .footer {
                padding: 10px;
                font-size: 12px;
            }
        }


    </style>
</head>
<body>
    <!-- 添加动态背景元素 -->
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>

    <!-- 添加音频元素用于完成提示音 -->
    <audio id="completion-sound" preload="auto">
        <source src="https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3" type="audio/mpeg">
        <!-- 备用音效 -->
        <source src="https://assets.mixkit.co/active_storage/sfx/1008/1008-preview.mp3" type="audio/mpeg">
        <!-- 如果浏览器不支持这些格式，将使用系统默认提示音 -->
    </audio>

    <div class="header">
        <h1>Rinwriter</h1>
        <a href="/logout" class="logout-button">
            <i class="fas fa-sign-out-alt"></i>退出登录
        </a>
    </div>

    <!-- 添加占位元素，防止内容被固定header遮挡，但减小高度 -->
    <div class="header-placeholder" style="height: 60px;"></div>

    <div class="main-content" style="margin-top: -10px; display: flex; flex-direction: column; justify-content: space-between; min-height: calc(100vh - 60px);">
        <div class="main-container" style="padding-top: 0; flex: 1;">
            <div class="sidebar">
            </div>
            <div class="content-area" style="padding-top: 0;">
            <div class="tabs">
                <button type="button" class="tab-button active" onclick="showTab('single')"><i class="fas fa-file-alt"></i>单个报告</button>
                <button type="button" class="tab-button" onclick="showTab('multiple')"><i class="fas fa-copy"></i>批量报告</button>
                <button type="button" class="tab-button" onclick="showTab('achievements')"><i class="fas fa-award"></i>成果材料</button>
            </div>

    <div class="submit-container" style="margin-top: 15px; margin-bottom: 15px;">
        <button id="generate-submit" type="button" class="submit-button active" onclick="submitActiveForm()">
            <span class="button-content"><i class="fas fa-paper-plane"></i>生成报告</span>
            <div class="progress-indicator" style="width: 0%;"></div>
        </button>
    </div>

    <!-- 下载按钮容器 -->
    <div class="download-container" style="text-align: center; margin: 10px 0;">
        <button id="download-report" type="button" class="download-button disabled" onclick="downloadReport()" disabled>
            <input class="input" type="checkbox">
            <div class="circle">
                <svg class="icon" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7,10 12,15 17,10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                <div class="square"></div>
            </div>
            <div class="title">下载</div>
            <div class="title">完成</div>
        </button>
    </div>

    <!-- 单个报告表单 -->
    <div id="single" class="tab-content active" style="margin-top: 15px; margin-bottom: 20px;">
        <form id="single-upload-form" action="/upload" method="post" enctype="multipart/form-data">
            <details class="form-section">
                <summary><i class="fas fa-info-circle"></i> 项目基本信息</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="company_name"><i class="fas fa-building"></i> 公司名称：</label>
                        <input type="text" name="company_name" id="company_name" required placeholder="请输入公司名称">
                    </div>
                    <div class="form-group">
                        <label for="project_name"><i class="fas fa-project-diagram"></i> 项目名称：</label>
                        <input type="text" name="project_name" id="project_name" required placeholder="请输入项目名称">
                    </div>
                    <div class="form-group">
                        <label for="rd_number"><i class="fas fa-hashtag"></i> RD序号：</label>
                        <select name="rd_number" id="rd_number" required>
                            <option value="">请选择RD序号</option>
                            <option value="RD01">RD01</option>
                            <option value="RD02">RD02</option>
                            <option value="RD03">RD03</option>
                            <option value="RD04">RD04</option>
                            <option value="RD05">RD05</option>
                            <option value="RD06">RD06</option>
                            <option value="RD07">RD07</option>
                            <option value="RD08">RD08</option>
                            <option value="RD09">RD09</option>
                            <option value="RD10">RD10</option>
                            <option value="RD11">RD11</option>
                            <option value="RD12">RD12</option>
                            <option value="RD13">RD13</option>
                            <option value="RD14">RD14</option>
                            <option value="RD15">RD15</option>
                            <option value="RD16">RD16</option>
                            <option value="RD17">RD17</option>
                            <option value="RD18">RD18</option>
                            <option value="RD19">RD19</option>
                            <option value="RD20">RD20</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="project_number"><i class="fas fa-id-card"></i> 项目编号：</label>
                        <input type="text" name="project_number" id="project_number" placeholder="不填则使用RD序号">
                    </div>
                    <div class="form-group">
                        <label for="acceptance_number"><i class="fas fa-check-circle"></i> 验收编号：</label>
                        <input type="text" name="acceptance_number" id="acceptance_number" placeholder="留空则使用项目编号">
                    </div>
                </div>
            </details>
            <details class="form-section">
                <summary><i class="fas fa-file-upload"></i> 技术文件上传</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="file1"><i class="fas fa-file-upload"></i> 技术文件1：</label>
                        <div class="file-input-container">
                            <input type="file" name="file1" id="file1">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('file1')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="file2"><i class="fas fa-file-upload"></i> 技术文件2：</label>
                        <div class="file-input-container">
                            <input type="file" name="file2" id="file2">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('file2')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="file3"><i class="fas fa-file-upload"></i> 技术文件3：</label>
                        <div class="file-input-container">
                            <input type="file" name="file3" id="file3">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('file3')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="file4"><i class="fas fa-file-upload"></i> 技术文件4：</label>
                        <div class="file-input-container">
                            <input type="file" name="file4" id="file4">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('file4')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="file5"><i class="fas fa-file-upload"></i> 技术文件5：</label>
                        <div class="file-input-container">
                            <input type="file" name="file5" id="file5">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('file5')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                </div>
            </details>

            <details class="form-section">
                <summary><i class="fas fa-file-excel"></i> 辅助账与工资表</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="excel_2022"><i class="fas fa-file-excel"></i> 2022辅助账（Excel）：</label>
                        <div class="file-input-container">
                            <input type="file" name="excel_2022" id="excel_2022" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2022')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="excel_2023"><i class="fas fa-file-excel"></i> 2023辅助账（Excel）：</label>
                        <div class="file-input-container">
                            <input type="file" name="excel_2023" id="excel_2023" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2023')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="excel_2024"><i class="fas fa-file-excel"></i> 2024辅助账（Excel）：</label>
                        <div class="file-input-container">
                            <input type="file" name="excel_2024" id="excel_2024" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2024')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="staff_excel"><i class="fas fa-users"></i> 研发人员工资表（Excel）：</label>
                        <div class="file-input-container">
                            <input type="file" name="staff_excel" id="staff_excel" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('staff_excel')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="rdps_file"><i class="fas fa-table"></i> RDPS表（可选）：</label>
                        <div class="file-input-container">
                            <input type="file" name="rdps_file" id="rdps_file" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('rdps_file')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                </div>
            </details>
            <details class="form-section">
                <summary><i class="fas fa-calendar-alt"></i> 项目时间与保存路径</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="start_date"><i class="fas fa-calendar-alt"></i> 项目开始时间：</label>
                        <input type="date" name="start_date" id="start_date" required>
                    </div>
                    <div class="form-group">
                        <label for="end_date"><i class="fas fa-calendar-check"></i> 项目结束时间：</label>
                        <input type="date" name="end_date" id="end_date" required>
                    </div>
                    <div class="form-group">
                        <label for="output_path"><i class="fas fa-folder-open"></i> 报告保存路径：</label>
                        <div class="path-input-container">
                            <input type="text" name="output_path" id="output_path" placeholder="输入保存路径（如 C:\Reports 或 ./reports）">
                        </div>
                    </div>
                </div>
            </details>
        </form>
    </div>

    <!-- 批量报告表单 -->
    <div id="multiple" class="tab-content" style="margin-top: 15px; margin-bottom: 20px;">
        <form id="multiple-upload-form" action="/upload_multiple" method="post" enctype="multipart/form-data">
            <details class="form-section">
                <summary><i class="fas fa-info-circle"></i> 共享信息</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="company_name_multi"><i class="fas fa-building"></i> <span>公司名称：</span></label>
                        <input type="text" name="company_name" id="company_name_multi" required placeholder="请输入公司名称">
                    </div>
                    <div class="form-group">
                        <label for="project_number_multi"><i class="fas fa-id-card"></i> <span>项目编号：</span></label>
                        <input type="text" name="project_number" id="project_number_multi" placeholder="不填则使用RD序号">
                    </div>
                    <div class="form-group">
                        <label for="acceptance_number_multi"><i class="fas fa-check-circle"></i> <span>验收编号：</span></label>
                        <input type="text" name="acceptance_number" id="acceptance_number_multi" placeholder="留空则使用项目编号">
                    </div>
                </div>
            </details>

            <details class="form-section">
                <summary><i class="fas fa-file-upload"></i> 共享文件</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="staff_excel_multi"><i class="fas fa-users"></i> <span>研发人员工资表：</span></label>
                        <div class="file-input-container">
                            <input type="file" name="staff_excel" id="staff_excel_multi" accept=".xlsx, .xls">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('staff_excel_multi')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="rdps_file_multi"><i class="fas fa-table"></i> <span>RDPS表（可选）：</span></label>
                        <div class="file-input-container">
                            <input type="file" name="rdps_file" id="rdps_file_multi" accept=".xlsx, .xls">
                            <button type="button" id="parse-rdps-btn" class="parse-rdps-btn" style="display: none;" onclick="parseRdpsFile()">
                                <i class="fas fa-magic"></i>填充
                            </button>
                        </div>
                    </div>
                </div>
            </details>

            <details class="form-section">
                <summary><i class="fas fa-cog"></i> 输出设置</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="output_path_multi"><i class="fas fa-folder-open"></i> <span>报告保存路径：</span></label>
                        <div class="path-input-container">
                            <input type="text" name="output_path" id="output_path_multi" placeholder="输入保存路径（如 C:\Reports 或 ./reports）">
                        </div>
                    </div>
                </div>
            </details>
            <details class="form-section multi-reports-section">
                <summary><i class="fas fa-layer-group"></i> 多项报告</summary>
                <div class="form-section-content">
                    <div id="report-sections">
                        <!-- 报告部分将由JavaScript动态生成 -->
                    </div>
                </div>
            </details>
        </form>
    </div>

    <!-- 成果材料表单 -->
    <div id="achievements" class="tab-content" style="margin-top: 15px; margin-bottom: 20px;">
        <form id="achievements-upload-form" action="/achievements" method="post" enctype="multipart/form-data">
            <details class="form-section">
                <summary><i class="fas fa-info-circle"></i> 基本信息</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="company_name_ach"><i class="fas fa-building"></i> <span>公司名称：</span></label>
                        <input type="text" name="company_name" id="company_name_ach" required placeholder="请输入公司名称">
                    </div>
                    <div class="form-group">
                        <label for="revenue_2024"><i class="fas fa-chart-line"></i> <span>高品收入：</span></label>
                        <input type="number" step="0.01" min="0" name="revenue_2024" id="revenue_2024" placeholder="例如 693.63">
                    </div>
                    <div class="form-group">
                        <label for="percentage_2024"><i class="fas fa-percentage"></i> <span>高品占比：</span></label>
                        <input type="number" step="0.01" min="0" max="100" name="percentage_2024" id="percentage_2024" placeholder="例如 70.59">
                    </div>
                </div>
            </details>

            <details class="form-section">
                <summary><i class="fas fa-file-upload"></i> 文件上传</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="rd_excel"><i class="fas fa-table"></i> <span>RD表（Excel）：</span></label>
                        <div class="file-input-container">
                            <input type="file" name="diagnostic_file" id="rd_excel" accept=".xlsx, .xls" required>
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('rd_excel')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="tech_files_zip"><i class="fas fa-file-archive"></i> <span>技术文件（ZIP）：</span></label>
                        <div class="file-input-container">
                            <input type="file" name="tech_files_zip" id="tech_files_zip" accept=".zip">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('tech_files_zip')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="patent_cert_zip"><i class="fas fa-certificate"></i> <span>专利证书（ZIP）：</span></label>
                        <div class="file-input-container">
                            <input type="file" name="patent_cert_zip" id="patent_cert_zip" accept=".zip">
                            <button type="button" class="clear-file-btn" onclick="clearFileInput('patent_cert_zip')">
                                <i class="fas fa-times"></i>清除
                            </button>
                        </div>
                    </div>
                </div>
            </details>

            <details class="form-section">
                <summary><i class="fas fa-cog"></i> 输出设置</summary>
                <div class="form-section-content">
                    <div class="form-group">
                        <label for="output_path_ach"><i class="fas fa-folder-open"></i> <span>保存路径：</span></label>
                        <div class="path-input-container">
                            <input type="text" name="output_path" id="output_path_ach" placeholder="输入保存路径（如 C:\Reports 或 ./reports）">
                        </div>
                    </div>
                </div>
            </details>
        </form>
    </div>

        </div> <!-- 关闭 content-area -->

        <!-- 页脚部分 -->
        <footer class="footer">
            <div id="particles-container"></div>
            <div class="footer-content">
                <p>© 2024 Rinwriter -by 联，微信：v116383932 | 版本 1.0.2</p>
            </div>
        </footer>
    </div> <!-- 关闭 main-container -->
    </div> <!-- 关闭 main-content -->

    <script>
        // 帮助部分相关函数已删除

        // 添加粒子动画 - 优化版本
        function setupParticles() {
            const footer = document.querySelector('.footer');
            const container = document.getElementById('particles-container');
            if (!container) return;

            // 设置容器样式
            container.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 30px; /* 减小高度 */
                overflow: hidden;
                border-radius: 12px;
                z-index: 1;
            `;

            // 清空现有粒子
            container.innerHTML = '';
            window.particles = [];

            // 根据设备性能和屏幕大小决定粒子数量
            const isMobile = window.innerWidth < 768;
            const isLowPerfDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4;

            // 减少粒子数量
            const particleCount = isMobile || isLowPerfDevice ? 3 : 4;

            // 创建粒子
            for (let i = 0; i < particleCount; i++) {
                createParticle(container, i);
            }
        }

        function createParticle(container, index) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            // 随机大小 (3-6px) - 减小粒子大小
            const size = 3 + Math.random() * 3;

            // 随机初始位置 - 确保不会太靠近边缘
            const posX = 10 + Math.random() * 80;
            const posY = 10 + Math.random() * 80;

            // 随机速度 (-10 到 10 px/s) - 减小速度范围
            let speedX = -10 + Math.random() * 20;
            let speedY = -10 + Math.random() * 20;

            // 确保粒子有最小速度
            if (Math.abs(speedX) < 3) speedX = speedX > 0 ? 3 : -3;
            if (Math.abs(speedY) < 3) speedY = speedY > 0 ? 3 : -3;

            // 随机不透明度 (0.5-0.8) - 降低不透明度
            const opacity = 0.5 + Math.random() * 0.3;

            // 随机颜色 - 绿色色调变化
            const hue = 120 + Math.random() * 40;
            const saturation = 60 + Math.random() * 30;
            const lightness = 40 + Math.random() * 20;

            // 设置粒子样式 - 简化阴影效果
            particle.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                background-color: hsl(${hue}, ${saturation}%, ${lightness}%);
                border-radius: 50%;
                filter: blur(1px);
                box-shadow: 0 0 ${size}px hsl(${hue}, ${saturation}%, ${lightness}%);
                opacity: ${opacity};
                top: ${posY}%;
                left: ${posX}%;
                z-index: 2;
                will-change: transform, opacity;
                transform: translateZ(0);
            `;

            container.appendChild(particle);

            // 存储粒子的当前位置和速度
            const particleData = {
                element: particle,
                x: posX,
                y: posY,
                speedX: speedX,
                speedY: speedY,
                size: size,
                hue: hue,
                saturation: saturation,
                lightness: lightness,
                lastFlash: 0 // 上次闪烁的时间
            };

            // 将粒子添加到全局数组
            window.particles.push(particleData);
        }

        // 更新粒子位置 - 优化版本
        function updateParticles() {
            // 如果页面不可见，暂停动画
            if (document.hidden) {
                window.particlesAnimationId = requestAnimationFrame(updateParticles);
                return;
            }

            const container = document.getElementById('particles-container');
            if (!container || !window.particles) {
                window.particlesAnimationId = requestAnimationFrame(updateParticles);
                return;
            }

            const now = performance.now();
            const particles = window.particles;

            // 节流更新 - 每16ms (约60fps) 更新一次
            if (window.lastParticleUpdate && now - window.lastParticleUpdate < 16) {
                window.particlesAnimationId = requestAnimationFrame(updateParticles);
                return;
            }

            window.lastParticleUpdate = now;

            // 更新位置
            particles.forEach(particle => {
                particle.x += particle.speedX * 0.01;
                particle.y += particle.speedY * 0.01;

                // 碰撞检测 - 边界
                if (particle.x < 0 || particle.x > 100) {
                    particle.speedX *= -1;
                    particle.x = Math.max(0, Math.min(100, particle.x));

                    // 限制闪烁频率 - 至少500ms一次
                    if (now - particle.lastFlash > 500) {
                        simpleFlashParticle(particle);
                        particle.lastFlash = now;
                    }
                }

                if (particle.y < 0 || particle.y > 100) {
                    particle.speedY *= -1;
                    particle.y = Math.max(0, Math.min(100, particle.y));

                    // 限制闪烁频率
                    if (now - particle.lastFlash > 500) {
                        simpleFlashParticle(particle);
                        particle.lastFlash = now;
                    }
                }

                // 应用新位置
                particle.element.style.left = `${particle.x}%`;
                particle.element.style.top = `${particle.y}%`;
            });

            // 简化的粒子碰撞检测 - 只在必要时执行
            // 每10帧检测一次碰撞，减少计算量
            if (Math.random() < 0.1 && particles.length > 1) {
                simpleCollisionDetection(particles, now);
            }

            // 继续动画循环
            window.particlesAnimationId = requestAnimationFrame(updateParticles);
        }

        // 简化的碰撞检测
        function simpleCollisionDetection(particles, now) {
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const p1 = particles[i];
                    const p2 = particles[j];

                    // 使用曼哈顿距离进行初步筛选 - 更高效
                    const dx = Math.abs(p1.x - p2.x);
                    const dy = Math.abs(p1.y - p2.y);

                    // 如果粒子足够近，则交换速度
                    if (dx < 10 && dy < 10) {
                        // 简单交换速度，不计算复杂的物理效果
                        const tempSpeedX = p1.speedX;
                        const tempSpeedY = p1.speedY;
                        p1.speedX = p2.speedX;
                        p1.speedY = p2.speedY;
                        p2.speedX = tempSpeedX;
                        p2.speedY = tempSpeedY;

                        // 限制闪烁频率
                        if (now - p1.lastFlash > 500) {
                            simpleFlashParticle(p1);
                            p1.lastFlash = now;
                        }

                        if (now - p2.lastFlash > 500) {
                            simpleFlashParticle(p2);
                            p2.lastFlash = now;
                        }
                    }
                }
            }
        }

        // 简化的粒子闪烁效果
        function simpleFlashParticle(particle) {
            // 只改变不透明度，不修改其他属性，减少重绘
            particle.element.style.opacity = "0.9";

            // 恢复原始状态
            setTimeout(() => {
                if (particle.element && document.body.contains(particle.element)) {
                    particle.element.style.opacity = "0.6";
                }
            }, 200);
        }

        // 页面加载完成后设置粒子 - 延迟初始化
        window.addEventListener('load', () => {
            // 延迟初始化粒子动画，优先加载页面其他内容
            setTimeout(() => {
                setupParticles();
                updateParticles();

                // 添加可见性变化监听器，在页面不可见时暂停动画
                document.addEventListener('visibilitychange', () => {
                    if (document.visibilityState === 'visible') {
                        if (!window.particlesAnimationId) {
                            updateParticles();
                        }
                    } else {
                        if (window.particlesAnimationId) {
                            cancelAnimationFrame(window.particlesAnimationId);
                            window.particlesAnimationId = null;
                        }
                    }
                });
            }, 500);
        });
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.5.1/socket.io.js"></script>
    <script>
        const socket = io.connect('http://' + document.domain + ':' + location.port);

        function showTab(tabId) {
            // 切换标签内容
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`button[onclick="showTab('${tabId}')"]`).classList.add('active');

            // 更新生成按钮的文本和图标
            const generateButton = document.getElementById('generate-submit');
            const buttonContent = generateButton.querySelector('.button-content');

            if (tabId === 'single') {
                buttonContent.innerHTML = '<i class="fas fa-paper-plane"></i>生成报告';
            } else if (tabId === 'multiple') {
                buttonContent.innerHTML = '<i class="fas fa-tasks"></i>生成所有报告';
            } else if (tabId === 'achievements') {
                buttonContent.innerHTML = '<i class="fas fa-file-export"></i>生成成果材料';
            }

            // 设置当前活动标签页
            window.activeTab = tabId;
        }

        // 初始化活动标签页
        window.activeTab = 'single';

        // 初始化下载状态
        window.downloadInProgress = false;

        // 初始化待下载数据
        window.pendingDownload = null;

        // 添加全局表单提交拦截器，防止意外提交
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有表单添加提交拦截
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(event) {
                    console.log('表单提交事件被触发，表单ID:', form.id);

                    // 检查是否是通过正确的提交按钮触发的
                    const submitButton = document.querySelector('#generate-submit');
                    const isProcessing = submitButton && submitButton.classList.contains('processing');

                    // 检查事件的来源
                    const submitter = event.submitter;
                    console.log('提交者:', submitter);

                    // 如果提交者不是正确的提交按钮，或者提交者有data-no-submit属性，则阻止提交
                    if (submitter && submitter.getAttribute('data-no-submit') === 'true') {
                        console.log('拦截了来自非提交按钮的表单提交:', submitter);
                        event.preventDefault();
                        event.stopPropagation();
                        return false;
                    }

                    // 如果不是正在处理状态且没有明确的提交者，说明可能是意外触发的提交
                    if (!isProcessing && !submitter) {
                        console.log('拦截了意外的表单提交（无明确提交者）');
                        event.preventDefault();
                        event.stopPropagation();
                        return false;
                    }
                });
            });

            // 添加键盘事件监听，防止Enter键意外触发提交
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    const target = event.target;
                    // 如果焦点在按钮上且按钮有data-no-submit属性，阻止默认行为
                    if (target.tagName === 'BUTTON' && target.getAttribute('data-no-submit') === 'true') {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('阻止了Enter键在非提交按钮上的默认行为');
                        return false;
                    }
                }
            });
        });

        // 启用下载按钮的函数
        function enableDownloadButton() {
            const downloadBtn = document.getElementById('download-report');
            if (downloadBtn) {
                downloadBtn.disabled = false;
                downloadBtn.classList.remove('disabled');
                downloadBtn.classList.add('enabled');
                console.log('下载按钮已启用');
            }
        }

        // 禁用下载按钮的函数
        function disableDownloadButton() {
            const downloadBtn = document.getElementById('download-report');
            if (downloadBtn) {
                downloadBtn.disabled = true;
                downloadBtn.classList.remove('enabled');
                downloadBtn.classList.add('disabled');
                
                // 重置动画状态
                const checkbox = downloadBtn.querySelector('.input');
                if (checkbox) {
                    checkbox.checked = false;
                    downloadBtn.classList.remove('downloading');
                }
                
                console.log('下载按钮已禁用');
            }
        }

        // 下载报告的函数
        function downloadReport() {
            if (!window.pendingDownload) {
                showNotification('没有可下载的文件，请先生成报告', 'warning');
                return;
            }

            // 防重复下载检查
            if (window.downloadInProgress) {
                showNotification('下载已在进行中，请稍候...', 'warning');
                return;
            }

            window.downloadInProgress = true;

            // 触发动画效果
            const downloadBtn = document.getElementById('download-report');
            const checkbox = downloadBtn.querySelector('.input');
            if (checkbox) {
                checkbox.checked = true;
                downloadBtn.classList.add('downloading');
            }

            try {
                // 创建下载链接
                const url = window.URL.createObjectURL(window.pendingDownload.blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = window.pendingDownload.filename;
                a.style.display = 'none';
                document.body.appendChild(a);

                // 触发下载
                a.click();

                // 清理
                setTimeout(() => {
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    window.downloadInProgress = false;
                    
                    // 动画完成后重置checkbox和class
                    setTimeout(() => {
                        if (checkbox) {
                            checkbox.checked = false;
                            downloadBtn.classList.remove('downloading');
                        }
                    }, 4000); // 等待动画完成后重置
                }, 100);

                // 显示成功消息
                showNotification('文件下载已开始...', 'success');

            } catch (error) {
                console.error('下载失败:', error);
                showNotification('下载失败: ' + error.message, 'error');
                window.downloadInProgress = false;
                
                // 失败时重置checkbox和class
                if (checkbox) {
                    checkbox.checked = false;
                    downloadBtn.classList.remove('downloading');
                }
            }
        }

        // 根据当前活动标签页提交表单
        function submitActiveForm() {
            // 开始生成新报告时，禁用下载按钮并清理之前的下载数据
            disableDownloadButton();
            if (window.pendingDownload && window.pendingDownload.blob) {
                window.URL.revokeObjectURL(window.pendingDownload.blob);
            }
            window.pendingDownload = null;

            // 根据当前活动标签页调用相应的提交函数
            if (window.activeTab === 'single') {
                submitSingleForm();
            } else if (window.activeTab === 'multiple') {
                submitMultipleForm();
            } else if (window.activeTab === 'achievements') {
                submitAchievementsForm();
            }
        }

        // 修改为按需生成报告部分
        function generateReportSections(initialCount = 3) {
            const reportSections = document.getElementById('report-sections');
            // 清空现有内容
            reportSections.innerHTML = '';

            // 使用文档片段批量添加元素
            const fragment = document.createDocumentFragment();

            // 初始只生成少量报告部分
            for (let i = 1; i <= initialCount; i++) {
                addReportSection(i, fragment);
            }

            // 一次性添加所有元素
            reportSections.appendChild(fragment);

            // 添加"添加更多报告"按钮
            const addMoreContainer = document.createElement('div');
            addMoreContainer.className = 'add-more-container';
            addMoreContainer.style.cssText = `
                text-align: center;
                margin: 20px 0;
                padding: 15px;
            `;

            const addMoreButton = document.createElement('button');
            addMoreButton.type = 'button'; // 明确设置为普通按钮，防止意外提交表单
            addMoreButton.className = 'add-more-button';

            // 添加多重防护，确保不会触发表单提交
            addMoreButton.setAttribute('type', 'button');
            addMoreButton.setAttribute('data-no-submit', 'true');
            addMoreButton.style.cssText = `
                padding: 10px 20px;
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
                color: var(--primary-color);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: var(--border-radius-full);
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                transition: var(--transition);
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto;
                box-shadow: var(--box-shadow);
            `;
            addMoreButton.innerHTML = '<i class="fas fa-plus" style="margin-right: 8px;"></i> 添加更多报告';
            addMoreButton.onclick = function(event) {
                // 阻止事件冒泡和默认行为，防止意外触发表单提交
                event.preventDefault();
                event.stopPropagation();

                console.log('点击了添加更多报告按钮'); // 调试日志

                const currentCount = reportSections.querySelectorAll('.report-section').length;
                if (currentCount < 20) {
                    // 每次添加一个新的报告部分
                    const newFragment = document.createDocumentFragment();
                    addReportSection(currentCount + 1, newFragment);

                    // 在"添加更多"按钮前插入新报告
                    reportSections.insertBefore(newFragment, addMoreContainer);

                    // 如果达到最大数量，隐藏按钮
                    if (currentCount + 1 >= 20) {
                        this.style.display = 'none';
                    }

                    // 更新计数显示
                    reportCountDisplay.textContent = `${currentCount + 1}/20 个报告`;

                    console.log(`已添加报告 ${currentCount + 1}，当前总数: ${currentCount + 1}`); // 调试日志
                } else {
                    console.log('已达到最大报告数量限制'); // 调试日志
                }

                return false; // 确保不会触发任何默认行为
            };

            // 添加报告计数显示
            const reportCountDisplay = document.createElement('div');
            reportCountDisplay.style.cssText = `
                font-size: 12px;
                color: var(--text-color-muted);
                margin-top: 8px;
            `;
            reportCountDisplay.textContent = `${initialCount}/20 个报告`;

            addMoreContainer.appendChild(addMoreButton);
            addMoreContainer.appendChild(reportCountDisplay);
            reportSections.appendChild(addMoreContainer);
        }

        // 添加单个报告部分的函数
        function addReportSection(i, container) {
            const section = document.createElement('div');
            section.className = 'report-section';

            // 生成RD序号选项
            let optionsHtml = '<option value="">请选择RD序号</option>';
            for (let j = 1; j <= 20; j++) {
                const rdValue = `RD${j < 10 ? '0' : ''}${j}`;
                optionsHtml += `<option value="${rdValue}">${rdValue}</option>`;
            }

            section.innerHTML = `
                <h3><i class="fas fa-file-alt"></i> 报告 ${i}</h3>

                <details class="form-section" closed>
                    <summary><i class="fas fa-info-circle"></i> 项目信息</summary>
                    <div class="form-section-content">
                        <div class="form-group">
                            <label for="project_name_${i}"><i class="fas fa-project-diagram"></i> <span>项目名称：</span></label>
                            <input type="text" name="project_name_${i}" id="project_name_${i}" placeholder="请输入项目名称">
                        </div>
                        <div class="form-group">
                            <label for="rd_number_${i}"><i class="fas fa-hashtag"></i> <span>RD序号：</span></label>
                            <select name="rd_number_${i}" id="rd_number_${i}">
                                ${optionsHtml}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="start_date_${i}"><i class="fas fa-calendar-alt"></i> <span>开始时间：</span></label>
                            <input type="date" name="start_date_${i}" id="start_date_${i}">
                        </div>
                        <div class="form-group">
                            <label for="end_date_${i}"><i class="fas fa-calendar-check"></i> <span>结束时间：</span></label>
                            <input type="date" name="end_date_${i}" id="end_date_${i}">
                        </div>
                    </div>
                </details>

                <details class="form-section" closed>
                    <summary><i class="fas fa-file-upload"></i> 技术文件</summary>
                    <div class="form-section-content">
                        <div class="form-group">
                            <label for="file1_${i}"><i class="fas fa-file-upload"></i> <span>技术文件1：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="file1_${i}" id="file1_${i}">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('file1_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="file2_${i}"><i class="fas fa-file-upload"></i> <span>技术文件2：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="file2_${i}" id="file2_${i}">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('file2_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="file3_${i}"><i class="fas fa-file-upload"></i> <span>技术文件3：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="file3_${i}" id="file3_${i}">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('file3_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="file4_${i}"><i class="fas fa-file-upload"></i> <span>技术文件4：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="file4_${i}" id="file4_${i}">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('file4_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="file5_${i}"><i class="fas fa-file-upload"></i> <span>技术文件5：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="file5_${i}" id="file5_${i}">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('file5_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="form-section" closed>
                    <summary><i class="fas fa-file-excel"></i> 辅助账目</summary>
                    <div class="form-section-content">
                        <div class="form-group">
                            <label for="excel_2022_${i}"><i class="fas fa-file-excel"></i> <span>2022辅助账（Excel）：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="excel_2022_${i}" id="excel_2022_${i}" accept=".xlsx, .xls">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2022_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                                <span class="file-status">文件已选择</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="excel_2023_${i}"><i class="fas fa-file-excel"></i> <span>2023辅助账（Excel）：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="excel_2023_${i}" id="excel_2023_${i}" accept=".xlsx, .xls">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2023_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                                <span class="file-status">文件已选择</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="excel_2024_${i}"><i class="fas fa-file-excel"></i> <span>2024辅助账（Excel）：</span></label>
                            <div class="file-input-container">
                                <input type="file" name="excel_2024_${i}" id="excel_2024_${i}" accept=".xlsx, .xls">
                                <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2024_${i}')">
                                    <i class="fas fa-times"></i>清除
                                </button>
                                <span class="file-status">文件已选择</span>
                            </div>
                        </div>
                    </div>
                </details>
            `;
            container.appendChild(section);
        }

        // 优化的页面加载函数 - 使用懒加载和延迟初始化
        window.onload = function() {
            // 首先执行关键初始化
            const multiReportsSection = document.querySelector('.multi-reports-section');
            if (multiReportsSection) {
                multiReportsSection.removeAttribute('open');
            }

            // 初始只生成少量报告部分
            generateReportSections(3);

            // 立即检查已有的文件输入状态
            checkExistingFileInputs();

            // 延迟加载非关键功能
            setTimeout(() => {
                // 设置文件上传状态监听
                setupFileInputListeners();

                // 添加页面可见性监听，在页面不可见时暂停耗费资源的操作
                document.addEventListener('visibilitychange', handleVisibilityChange);

                // 初始化通知系统
                initNotifications();
            }, 100);

            // 进一步延迟加载视觉效果
            setTimeout(() => {
                // 只在页面可见时初始化粒子效果
                if (document.visibilityState === 'visible') {
                    setupParticles();
                    updateParticles();
                }
            }, 500);
        };

        // 检查已有的文件输入状态 - 使用setAttribute确保样式应用
        function checkExistingFileInputs() {
            console.log("检查已有文件输入状态...");

            // 获取所有文件输入框
            const fileInputs = document.querySelectorAll('input[type="file"]');
            console.log(`找到 ${fileInputs.length} 个文件输入框`);

            // 检查每个文件输入框是否已有文件
            fileInputs.forEach((input, index) => {
                if (input.files && input.files.length > 0) {
                    console.log(`文件输入框 #${index} (${input.id}) 有文件: ${input.files[0].name}`);

                    // 获取父容器
                    const container = input.closest('.file-input-container');
                    if (container) {
                        console.log(`- 找到容器，应用样式`);

                        // 添加类名
                        input.classList.add('has-file');
                        container.classList.add('has-file');

                        // 1. 强制设置输入框样式
                        input.setAttribute('style', `
                            background-color: rgba(16, 185, 129, 0.4) !important;
                            border: 3px solid #10b981 !important;
                            color: white !important;
                            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.5) !important;
                            transform: translateY(-2px) !important;
                        `);

                        // 2. 强制设置容器样式
                        container.setAttribute('style', `
                            position: relative !important;
                            box-shadow: 0 0 15px rgba(16, 185, 129, 0.3) !important;
                            background-color: rgba(16, 185, 129, 0.1) !important;
                        `);

                        // 3. 创建一个额外的背景元素确保颜色填充
                        let bgElement = container.querySelector('.file-bg-fill');
                        if (!bgElement) {
                            bgElement = document.createElement('div');
                            bgElement.className = 'file-bg-fill';
                            // 插入到容器的最前面，确保在其他元素下面
                            container.insertBefore(bgElement, container.firstChild);
                        }

                        bgElement.setAttribute('style', `
                            position: absolute !important;
                            top: 0 !important;
                            left: 0 !important;
                            right: 0 !important;
                            bottom: 0 !important;
                            background-color: rgba(16, 185, 129, 0.15) !important;
                            border-radius: 8px !important;
                            z-index: 0 !important;
                            pointer-events: none !important;
                        `);

                        // 4. 添加或更新发光效果元素
                        let glowElement = container.querySelector('.file-glow-effect');
                        if (!glowElement) {
                            glowElement = document.createElement('div');
                            glowElement.className = 'file-glow-effect';
                            container.appendChild(glowElement);
                        }

                        glowElement.setAttribute('style', `
                            position: absolute !important;
                            top: 0 !important;
                            left: 0 !important;
                            right: 0 !important;
                            bottom: 0 !important;
                            border-radius: 8px !important;
                            border: 2px solid rgba(16, 185, 129, 0.5) !important;
                            pointer-events: none !important;
                            z-index: 1 !important;
                            box-shadow: 0 0 15px rgba(16, 185, 129, 0.5) !important;
                            animation: pulse-border 2s infinite !important;
                            background-color: rgba(16, 185, 129, 0.05) !important;
                        `);

                        // 5. 获取或创建状态指示器
                        let statusElement = container.querySelector('.file-status');
                        if (!statusElement) {
                            statusElement = document.createElement('span');
                            statusElement.className = 'file-status';
                            container.appendChild(statusElement);
                        }

                        // 更新状态指示器文本
                        const fileName = input.files[0].name;
                        const fileSize = (input.files[0].size / 1024).toFixed(1);
                        statusElement.textContent = `已选择: ${fileName} (${fileSize}KB)`;

                        statusElement.setAttribute('style', `
                            opacity: 1 !important;
                            transform: translateY(0) !important;
                            color: #10b981 !important;
                            text-shadow: 0 0 5px rgba(16, 185, 129, 0.3) !important;
                            background-color: rgba(255, 255, 255, 0.9) !important;
                            padding: 3px 8px !important;
                            border-radius: 4px !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                            border-left: 3px solid #10b981 !important;
                            font-weight: bold !important;
                            position: absolute !important;
                            left: 15px !important;
                            bottom: -22px !important;
                            font-size: 12px !important;
                            z-index: 10 !important;
                        `);
                    }
                }
            });
        }

        // 处理页面可见性变化
        function handleVisibilityChange() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时恢复动画
                if (!window.particlesAnimationId && window.particles) {
                    updateParticles();
                }
            } else {
                // 页面不可见时暂停动画
                if (window.particlesAnimationId) {
                    cancelAnimationFrame(window.particlesAnimationId);
                    window.particlesAnimationId = null;
                }
            }
        }

        // 初始化通知系统
        function initNotifications() {
            // 预加载通知图标
            const infoIcon = new Image();
            infoIcon.src = 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/svgs/solid/info-circle.svg';

            const successIcon = new Image();
            successIcon.src = 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/svgs/solid/check-circle.svg';

            const errorIcon = new Image();
            errorIcon.src = 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/svgs/solid/exclamation-circle.svg';
        }

        // 清除文件输入框的函数 - 调用优化版本
        function clearFileInput(inputId) {
            // 调用优化版本的函数
            clearFileInputOptimized(inputId);
        }

        // 添加文件上传状态检测 - 增强版
        function setupFileInputListeners() {
            console.log("设置增强版文件上传状态监听...");

            // 添加全局样式，确保动画效果
            addGlobalStyles();

            // 直接监听所有文件输入框，不依赖容器
            document.querySelectorAll('input[type="file"]').forEach(input => {
                // 移除旧的事件监听器，避免重复
                input.removeEventListener('change', fileChangeHandler);

                // 添加新的事件监听器
                input.addEventListener('change', fileChangeHandler);

                // 立即检查当前状态
                if (input.files && input.files.length > 0) {
                    console.log(`文件输入框 ${input.id} 已有文件，立即应用样式`);
                    handleFileInputChange(input);
                }
            });

            // 使用事件委托处理所有文件输入的变化 - 捕获动态添加的元素
            document.body.addEventListener('change', function(event) {
                const target = event.target;
                // 只处理文件输入框的变化
                if (target.type === 'file') {
                    handleFileInputChange(target);
                }
            });

            // 使用事件委托处理清除按钮点击
            document.body.addEventListener('click', function(event) {
                const target = event.target;
                // 检查是否点击了清除按钮或其内部的图标
                if (target.classList.contains('clear-file-btn') ||
                    (target.tagName === 'I' && target.parentElement.classList.contains('clear-file-btn'))) {

                    // 如果点击的是图标，获取父按钮
                    const clearButton = target.classList.contains('clear-file-btn') ? target : target.parentElement;

                    // 从onclick属性中提取文件输入ID
                    const onclickAttr = clearButton.getAttribute('onclick');
                    if (onclickAttr) {
                        const match = onclickAttr.match(/clearFileInput\(['"]([^'"]+)['"]\)/);
                        if (match && match[1]) {
                            const inputId = match[1];
                            // 阻止默认onclick处理
                            event.preventDefault();
                            // 调用优化的清除函数
                            clearFileInputOptimized(inputId);
                        }
                    }
                }
            });

            // 添加DOM变化监听，处理动态添加的文件输入框
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        // 检查新添加的节点中是否有文件输入框
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // 元素节点
                                // 检查节点本身
                                if (node.tagName === 'INPUT' && node.type === 'file') {
                                    node.addEventListener('change', fileChangeHandler);
                                    if (node.files && node.files.length > 0) {
                                        handleFileInputChange(node);
                                    }
                                }

                                // 检查子节点
                                const fileInputs = node.querySelectorAll('input[type="file"]');
                                fileInputs.forEach(input => {
                                    input.addEventListener('change', fileChangeHandler);
                                    if (input.files && input.files.length > 0) {
                                        handleFileInputChange(input);
                                    }
                                });
                            }
                        });
                    }
                });
            });

            // 开始观察整个文档
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        // 文件变化处理函数
        function fileChangeHandler() {
            handleFileInputChange(this);
        }

        // 添加全局样式
        function addGlobalStyles() {
            // 检查是否已添加样式
            if (document.getElementById('file-upload-styles')) {
                return;
            }

            console.log("添加全局样式...");

            // 创建样式元素
            const styleElement = document.createElement('style');
            styleElement.id = 'file-upload-styles';
            styleElement.textContent = `
                @keyframes pulse-border {
                    0% { opacity: 0.6; box-shadow: 0 0 5px rgba(16, 185, 129, 0.3); }
                    50% { opacity: 1; box-shadow: 0 0 20px rgba(16, 185, 129, 0.6); }
                    100% { opacity: 0.6; box-shadow: 0 0 5px rgba(16, 185, 129, 0.3); }
                }

                .file-input-container {
                    position: relative !important;
                }

                .file-bg-fill {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    background-color: rgba(16, 185, 129, 0.3) !important;
                    border-radius: 8px !important;
                    z-index: 0 !important;
                    pointer-events: none !important;
                }

                .file-overlay {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.3)) !important;
                    border-radius: 8px !important;
                    z-index: 0 !important;
                    pointer-events: none !important;
                    border: 1px solid rgba(16, 185, 129, 0.4) !important;
                }

                .file-glow-effect {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    border-radius: 8px !important;
                    border: 2px solid rgba(16, 185, 129, 0.5) !important;
                    pointer-events: none !important;
                    z-index: 1 !important;
                    box-shadow: 0 0 15px rgba(16, 185, 129, 0.5) !important;
                    animation: pulse-border 2s infinite !important;
                    background-color: rgba(16, 185, 129, 0.05) !important;
                }

                input[type="file"].has-file {
                    background-color: rgba(16, 185, 129, 0.4) !important;
                    border: 3px solid #10b981 !important;
                    color: white !important;
                    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.5) !important;
                    transform: translateY(-2px) !important;
                }
            `;

            // 添加到文档头部
            document.head.appendChild(styleElement);
            console.log("已添加全局样式");
        }

        // 处理文件输入变化的函数 - 完全重写以确保视觉反馈
        function handleFileInputChange(input) {
            // 使用防抖函数减少频繁调用
            if (window.fileChangeTimeout) {
                clearTimeout(window.fileChangeTimeout);
            }

            window.fileChangeTimeout = setTimeout(() => {
                // 获取父容器
                const container = input.closest('.file-input-container');
                if (!container) return;

                // 获取表单组
                const formGroup = container.closest('.form-group');
                if (!formGroup) return;

                // 获取清除按钮
                const clearBtn = formGroup.querySelector('.clear-file-btn');

                // 获取状态指示器
                let statusElement = container.querySelector('.file-status');

                // 如果状态指示器不存在，创建一个
                if (!statusElement) {
                    statusElement = document.createElement('span');
                    statusElement.className = 'file-status';
                    statusElement.textContent = '文件已选择';
                    container.appendChild(statusElement);
                }

                // 检查是否有文件被选择
                const hasFile = input.files && input.files.length > 0;

                if (hasFile) {
                    // ===== 直接应用强制样式 =====

                    // 1. 强制设置输入框样式 - 只填充虚线框体
                    input.setAttribute('style', `
                        background-color: rgba(16, 185, 129, 0.9) !important;
                        border: 3px solid #10b981 !important;
                        color: white !important;
                        box-shadow: 0 4px 20px rgba(16, 185, 129, 0.8) !important;
                        transform: translateY(-2px) !important;
                        font-weight: bold !important;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
                        z-index: 1 !important; /* 确保z-index较低 */
                    `);

                    // 2. 不再设置容器样式，只设置输入框样式
                    container.removeAttribute('style');

                    // 确保容器有正确的定位，但不添加背景色
                    container.style.position = 'relative';

                    // 3. 移除发光效果元素
                    let glowElement = container.querySelector('.file-glow-effect');
                    if (glowElement) {
                        container.removeChild(glowElement);
                    }

                    // 4. 不再显示状态指示器
                    statusElement.style.display = 'none';

                    // 5. 添加类名以便CSS选择器可以工作
                    input.classList.add('has-file');
                    container.classList.add('has-file');

                    // 移除之前可能存在的背景元素和覆盖层元素
                    const bgElement = container.querySelector('.file-bg-fill');
                    if (bgElement) {
                        container.removeChild(bgElement);
                    }

                    const overlayElement = container.querySelector('.file-overlay');
                    if (overlayElement) {
                        container.removeChild(overlayElement);
                    }

                    // 清除按钮始终显示，不需要特殊处理
                    // if (clearBtn) {
                    //     clearBtn.style.display = 'inline-flex';
                    // }

                } else {
                    // 移除所有已上传状态的样式和元素
                    input.removeAttribute('style');
                    container.removeAttribute('style');

                    // 确保容器有正确的定位
                    container.style.position = 'relative';

                    // 移除发光效果元素
                    const glowElement = container.querySelector('.file-glow-effect');
                    if (glowElement) {
                        container.removeChild(glowElement);
                    }

                    // 移除背景填充元素
                    const bgElement = container.querySelector('.file-bg-fill');
                    if (bgElement) {
                        container.removeChild(bgElement);
                    }

                    // 移除覆盖层元素
                    const overlayElement = container.querySelector('.file-overlay');
                    if (overlayElement) {
                        container.removeChild(overlayElement);
                    }

                    // 隐藏状态指示器
                    statusElement.style.display = 'none';

                    // 移除类名
                    input.classList.remove('has-file');
                    container.classList.remove('has-file');

                    // 清除按钮始终显示，不需要隐藏
                    // if (clearBtn) {
                    //     clearBtn.style.display = 'none';
                    // }
                }
            }, 10);
        }

        // 优化的清除文件输入函数 - 确保彻底清除样式
        function clearFileInputOptimized(inputId) {
            const fileInput = document.getElementById(inputId);
            if (fileInput) {
                // 重置文件输入框的值
                fileInput.value = '';

                // 获取父容器
                const container = fileInput.closest('.file-input-container');
                if (container) {
                    // 获取表单组
                    const formGroup = container.closest('.form-group');

                    // 直接移除所有样式和元素
                    fileInput.removeAttribute('style');
                    container.removeAttribute('style');

                    // 确保容器有正确的定位
                    container.style.position = 'relative';

                    // 移除发光效果元素
                    const glowElement = container.querySelector('.file-glow-effect');
                    if (glowElement) {
                        container.removeChild(glowElement);
                    }

                    // 移除背景填充元素
                    const bgElement = container.querySelector('.file-bg-fill');
                    if (bgElement) {
                        container.removeChild(bgElement);
                    }

                    // 移除覆盖层元素
                    const overlayElement = container.querySelector('.file-overlay');
                    if (overlayElement) {
                        container.removeChild(overlayElement);
                    }

                    // 隐藏状态指示器
                    const statusElement = container.querySelector('.file-status');
                    if (statusElement) {
                        statusElement.style.display = 'none';
                    }

                    // 移除类名
                    fileInput.classList.remove('has-file');
                    container.classList.remove('has-file');

                    // 清除按钮始终显示，不需要隐藏
                    // if (formGroup) {
                    //     const clearBtn = formGroup.querySelector('.clear-file-btn');
                    //     if (clearBtn) {
                    //         clearBtn.style.display = 'none';
                    //     }
                    // }
                }

                // 使用统一的处理函数更新状态
                handleFileInputChange(fileInput);

                // 显示通知
                console.log(`已清除 ${inputId} 的文件选择`);
                showNotification(`已清除文件选择`, 'info');
            } else {
                console.error(`找不到ID为 ${inputId} 的文件输入框`);
            }
        }

        // 更新按钮进度条
        function updateButtonProgress(button, progress) {
            if (!button) return;

            // 查找或创建进度指示器
            let progressIndicator = button.querySelector('.progress-indicator');
            if (!progressIndicator) {
                progressIndicator = document.createElement('div');
                progressIndicator.className = 'progress-indicator';
                button.appendChild(progressIndicator);
            }

            // 更新进度条宽度
            progressIndicator.style.width = progress + '%';
        }

        // 为动态生成的报告部分添加清除按钮和文件上传状态监听
        function addClearButtonsToReportSections() {
            const reportSections = document.getElementById('report-sections');
            if (!reportSections) return;

            // 查找所有文件输入框
            const fileInputs = reportSections.querySelectorAll('input[type="file"]');

            fileInputs.forEach(input => {
                // 获取当前输入框的父元素
                const parent = input.parentElement;

                // 如果父元素不是file-input-container，则需要创建容器并添加清除按钮
                if (!parent.classList.contains('file-input-container')) {
                    // 创建新的容器
                    const container = document.createElement('div');
                    container.className = 'file-input-container';

                    // 将输入框移动到新容器中
                    parent.insertBefore(container, input);
                    container.appendChild(input);

                    // 创建清除按钮
                    const clearBtn = document.createElement('button');
                    clearBtn.type = 'button';
                    clearBtn.className = 'clear-file-btn';
                    clearBtn.innerHTML = '<i class="fas fa-times"></i>清除';
                    clearBtn.onclick = function() { clearFileInput(input.id); };

                    // 添加清除按钮到容器
                    container.appendChild(clearBtn);
                }
            });
        }

        function validateSingleForm() {
            // 检查公司名称
            const companyName = document.getElementById('company_name').value.trim();
            if (!companyName) {
                showNotification('请填入公司名称！', 'error');
                highlightInvalidField('company_name');
                return false;
            }

            // 检查RD序号
            const rdNumber = document.getElementById('rd_number').value;
            if (!rdNumber || rdNumber === "请选择") {
                showNotification('请选择RD序号！', 'error');
                highlightInvalidField('rd_number');
                return false;
            }

            // 检查项目起止时间
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            if (!startDate || !endDate) {
                showNotification('请填入项目起止时间！', 'error');
                if (!startDate) highlightInvalidField('start_date');
                if (!endDate) highlightInvalidField('end_date');
                return false;
            }

            // 检查技术文件
            let filesUploaded = false;
            for (let i = 1; i <= 5; i++) {
                const fileInput = document.getElementById(`file${i}`);
                if (fileInput && fileInput.files.length > 0) {
                    filesUploaded = true;
                    break;
                }
            }
            if (!filesUploaded) {
                showNotification('请至少上传一个技术文件！', 'error');
                // 高亮所有文件输入框
                for (let i = 1; i <= 5; i++) {
                    highlightInvalidField(`file${i}`);
                }
                return false;
            }

            return true;
        }

        // 高亮无效字段
        function highlightInvalidField(fieldId) {
            const field = document.getElementById(fieldId);
            if (!field) return;

            // 添加错误样式
            field.classList.add('invalid-field');

            // 添加抖动动画
            field.classList.add('shake-animation');

            // 移除抖动动画
            setTimeout(() => {
                field.classList.remove('shake-animation');
            }, 500);

            // 监听输入，当用户开始输入时移除错误样式
            field.addEventListener('input', function onInput() {
                field.classList.remove('invalid-field');
                field.removeEventListener('input', onInput);
            });

            // 监听选择变化
            field.addEventListener('change', function onChange() {
                field.classList.remove('invalid-field');
                field.removeEventListener('change', onChange);
            });
        }

        function validateMultipleForm() {
            // 首先检查共享的公司名称
            const sharedCompanyName = document.getElementById('company_name_multi').value.trim();
            if (!sharedCompanyName) {
                showNotification('请填入公司名称！', 'error');
                highlightInvalidField('company_name_multi');
                return false;
            }

            let hasValidReport = false;
            for (let i = 1; i <= 20; i++) {
                const projectNameInput = document.getElementById(`project_name_${i}`);
                if (!projectNameInput) continue;

                const projectName = projectNameInput.value.trim();
                const rdNumberSelect = document.getElementById(`rd_number_${i}`);
                const rdNumber = rdNumberSelect ? rdNumberSelect.value : "";
                const startDateInput = document.getElementById(`start_date_${i}`);
                const startDate = startDateInput ? startDateInput.value : "";
                const endDateInput = document.getElementById(`end_date_${i}`);
                const endDate = endDateInput ? endDateInput.value : "";

                let filesUploaded = false;
                for (let j = 1; j <= 5; j++) {
                    const fileInput = document.getElementById(`file${j}_${i}`);
                    if (fileInput && fileInput.files.length > 0) {
                        filesUploaded = true;
                        break;
                    }
                }

                // 如果有任何一个字段填写了，则认为用户想要生成这个报告
                if (projectName || rdNumber || startDate || endDate) {
                    // 检查所有必填字段（除了公司名称，因为它是共享的）
                    let missingFields = [];

                    if (!projectName) {
                        missingFields.push("项目名称");
                        highlightInvalidField(projectNameInput.id);
                    }

                    if (!rdNumber || rdNumber === "请选择") {
                        missingFields.push("RD序号");
                        if (rdNumberSelect) highlightInvalidField(rdNumberSelect.id);
                    }

                    if (!startDate) {
                        missingFields.push("开始时间");
                        if (startDateInput) highlightInvalidField(startDateInput.id);
                    }

                    if (!endDate) {
                        missingFields.push("结束时间");
                        if (endDateInput) highlightInvalidField(endDateInput.id);
                    }

                    if (!filesUploaded) {
                        missingFields.push("技术文件");
                        // 高亮所有文件输入框
                        for (let j = 1; j <= 5; j++) {
                            const fileId = `file${j}_${i}`;
                            highlightInvalidField(fileId);
                        }
                    }

                    if (missingFields.length > 0) {
                        showNotification(`报告 ${i}: 缺少必填字段: ${missingFields.join(", ")}`, 'error');
                        return false;
                    }

                    hasValidReport = true;
                }
            }

            if (!hasValidReport) {
                showNotification('请至少填写一个完整的报告！', 'error');
                return false;
            }

            return true;
        }

        function validateAchievementsForm() {
            // 检查RD表
            const rdExcel = document.getElementById('rd_excel');
            if (!rdExcel || rdExcel.files.length === 0) {
                showNotification('请上传 RD 表！', 'error');
                highlightInvalidField('rd_excel');
                return false;
            }

            // 检查公司名称
            const companyName = document.getElementById('company_name_ach').value.trim();
            if (!companyName) {
                showNotification('请填入公司名称！', 'error');
                highlightInvalidField('company_name_ach');
                return false;
            }

            // 检查收入和占比
            const revenue2024 = document.getElementById('revenue_2024').value;
            const percentage2024 = document.getElementById('percentage_2024').value;

            if (revenue2024) {
                const revenueValue = parseFloat(revenue2024);
                if (isNaN(revenueValue) || revenueValue < 0) {
                    showNotification('2024高新技术产品收入必须为非负数！', 'error');
                    highlightInvalidField('revenue_2024');
                    return false;
                }
            }

            if (percentage2024) {
                const percentageValue = parseFloat(percentage2024);
                if (isNaN(percentageValue) || percentageValue < 0 || percentageValue > 100) {
                    showNotification('2024高新技术产品收入占比必须在 0 到 100 之间！', 'error');
                    highlightInvalidField('percentage_2024');
                    return false;
                }
            }

            return true;
        }

        // 创建通知函数
        function showNotification(message, type = 'info') {
            // 如果已有通知，先移除
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            // 设置图标
            let icon = 'info-circle';
            if (type === 'success') icon = 'check-circle';
            if (type === 'error') icon = 'exclamation-circle';
            if (type === 'warning') icon = 'exclamation-triangle';

            notification.innerHTML = `
                <i class="fas fa-${icon}"></i>
                <span>${message}</span>
                <button type="button" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // 添加到页面
            document.body.appendChild(notification);

            // 自动消失
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.classList.add('fade-out');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 500);
                }
            }, 5000);

            // 如果是错误，也显示alert
            if (type === 'error') {
                alert(message);
            }
        }

        function submitSingleForm() {
            const form = document.getElementById('single-upload-form');
            const submitButton = document.querySelector('#generate-submit');

            if (!validateSingleForm()) {
                return;
            }

            // 保存原始文本
            if (!submitButton.hasAttribute('data-original-text')) {
                submitButton.setAttribute('data-original-text', submitButton.innerHTML);
            }

            // 添加处理中的样式
            submitButton.classList.add('processing');
            submitButton.disabled = true;

            // 添加取消按钮
            const cancelButton = document.createElement('button');
            cancelButton.type = 'button'; // 明确设置为普通按钮
            cancelButton.className = 'cancel-generation';
            cancelButton.innerHTML = '<i class="fas fa-times"></i>';
            cancelButton.title = '取消生成';
            cancelButton.onclick = function(e) {
                e.stopPropagation(); // 阻止事件冒泡

                // 显示取消中的提示
                showNotification('正在取消生成...', 'info');

                // 发送取消请求到服务器
                socket.emit('cancel_generation');

                // 重置按钮状态
                resetButton(submitButton);

                // 移除取消按钮
                if (cancelButton.parentNode) {
                    cancelButton.parentNode.removeChild(cancelButton);
                }
            };
            submitButton.appendChild(cancelButton);

            // 获取进度指示器元素
            let progressIndicator = submitButton.querySelector('.progress-indicator');
            if (!progressIndicator) {
                console.error('找不到进度指示器元素');
                return;
            }

            // 保存原始内容
            const buttonContent = submitButton.querySelector('.button-content');
            if (buttonContent) {
                // 保存原始内容以便后续恢复
                submitButton.setAttribute('data-original-content', buttonContent.innerHTML);

                // 更新按钮内容为进度文本
                buttonContent.innerHTML = '生成中...';
            }

            // 设置初始进度
            progressIndicator.style.width = '0%';

            // 模拟进度增长 - 每200ms增加2%，直到90%
            let currentProgress = 0;
            // 清除可能存在的旧进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }
            window.progressInterval = setInterval(() => {
                currentProgress += 2;
                if (currentProgress > 90) {
                    clearInterval(window.progressInterval);
                    window.progressInterval = null;
                } else {
                    progressIndicator.style.width = `${currentProgress}%`;
                }
            }, 200);

            const formData = new FormData(form);
            fetch('/upload', {
                method: 'POST',
                body: formData
            }).then(response => {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => {
                        resetButton(submitButton);
                        if (data.success) {
                            return `文件已成功保存到本地路径：${data.message}`;
                        } else {
                            return `保存文件失败：${data.message}`;
                        }
                    });
                } else if (contentType && contentType.includes('application/zip')) {
                    return response.blob().then(blob => {
                        // 防重复下载检查
                        if (window.downloadInProgress) {
                            console.log('下载已在进行中，跳过重复下载');
                            return '下载已在进行中...';
                        }

                        const contentDisposition = response.headers.get('content-disposition');
                        let filename = '项目资料.zip';
                        if (contentDisposition) {
                            const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
                            if (filenameMatch && filenameMatch[1]) {
                                filename = filenameMatch[1];
                            }
                        }

                        // 保存下载数据到全局变量
                        window.pendingDownload = {
                            blob: blob,
                            filename: filename
                        };

                        // 重置按钮
                        resetButton(submitButton);

                        // 启用下载按钮
                        enableDownloadButton();

                        // 播放完成提示音
                        playCompletionSound();
                        return '报告生成成功，点击下载按钮保存文件';
                    });
                } else {
                    return response.text();
                }
            }).then(data => {
                if (typeof data === 'string') {
                    showNotification(data, data.includes('成功') ? 'success' : 'info');
                }
            }).catch(error => {
                console.error('Error:', error);
                resetButton(submitButton);
                showNotification('生成报告时发生错误: ' + error, 'error');
            });
        }

        // 重置按钮状态的函数
        function resetButton(button) {
            // 清除可能存在的模拟进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
                window.progressInterval = null;
            }

            // 重置下载状态
            window.downloadInProgress = false;

            // 获取按钮ID
            const buttonId = button.id;

            // 根据当前活动标签页设置不同的内容
            let contentHTML = '';
            if (window.activeTab === 'single') {
                contentHTML = '<i class="fas fa-paper-plane"></i>生成报告';
            } else if (window.activeTab === 'multiple') {
                contentHTML = '<i class="fas fa-tasks"></i>生成所有报告';
            } else if (window.activeTab === 'achievements') {
                contentHTML = '<i class="fas fa-file-export"></i>生成成果材料';
            } else {
                contentHTML = '<i class="fas fa-file-export"></i>生成报告';
            }

            // 添加过渡效果
            button.style.transition = 'all 0.5s ease';

            // 移除取消按钮
            const cancelButton = button.querySelector('.cancel-generation');
            if (cancelButton) {
                cancelButton.remove();
            }

            // 移除处理中的样式
            button.classList.remove('processing');
            button.disabled = false;

            // 获取按钮内容元素
            const buttonContent = button.querySelector('.button-content');
            if (buttonContent) {
                // 恢复原始内容
                buttonContent.innerHTML = contentHTML;
            } else {
                // 如果没有找到按钮内容元素，创建一个
                const newButtonContent = document.createElement('span');
                newButtonContent.className = 'button-content';
                newButtonContent.innerHTML = contentHTML;

                // 清空按钮内容
                button.innerHTML = '';

                // 添加按钮内容元素
                button.appendChild(newButtonContent);

                // 重新添加进度指示器
                const progressIndicator = document.createElement('div');
                progressIndicator.className = 'progress-indicator';
                progressIndicator.style.width = '0%';
                button.appendChild(progressIndicator);
            }

            // 重置进度条宽度
            const progressIndicator = button.querySelector('.progress-indicator');
            if (progressIndicator) {
                progressIndicator.style.width = '0%';
                progressIndicator.style.boxShadow = 'none';
            }

            // 添加完成动画效果
            button.classList.add('button-completed');
            setTimeout(() => {
                button.classList.remove('button-completed');
            }, 1000);
        }

        function submitMultipleForm() {
            // 安全检查：确保这是通过正确的方式调用的
            console.log('submitMultipleForm 被调用');

            // 检查是否已经在处理中
            const submitButton = document.querySelector('#generate-submit');
            if (submitButton && submitButton.classList.contains('processing')) {
                console.log('已在处理中，忽略重复调用');
                return;
            }

            const form = document.getElementById('multiple-upload-form');

            if (!validateMultipleForm()) {
                return;
            }

            // 保存原始文本
            if (!submitButton.hasAttribute('data-original-text')) {
                submitButton.setAttribute('data-original-text', submitButton.innerHTML);
            }

            // 添加处理中的样式
            submitButton.classList.add('processing');
            submitButton.disabled = true;

            // 获取进度指示器元素
            let progressIndicator = submitButton.querySelector('.progress-indicator');
            if (!progressIndicator) {
                console.error('找不到进度指示器元素');
                return;
            }

            // 保存原始内容
            const buttonContent = submitButton.querySelector('.button-content');
            if (buttonContent) {
                // 保存原始内容以便后续恢复
                submitButton.setAttribute('data-original-content', buttonContent.innerHTML);

                // 更新按钮内容为进度文本
                buttonContent.innerHTML = '批量生成中...';
            }

            // 设置初始进度
            progressIndicator.style.width = '0%';

            // 模拟进度增长 - 每300ms增加1%，直到90%（批量报告生成较慢）
            let currentProgress = 0;
            // 清除可能存在的旧进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }
            window.progressInterval = setInterval(() => {
                currentProgress += 1;
                if (currentProgress > 90) {
                    clearInterval(window.progressInterval);
                    window.progressInterval = null;
                } else {
                    progressIndicator.style.width = `${currentProgress}%`;
                }
            }, 300);

            const formData = new FormData(form);
            fetch('/upload_multiple', {
                method: 'POST',
                body: formData
            }).then(response => {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => {
                        resetButton(submitButton);
                        if (data.success) {
                            return `文件已成功保存到本地路径：${data.message}`;
                        } else {
                            return `保存文件失败：${data.message}`;
                        }
                    });
                } else if (contentType && contentType.includes('application/zip')) {
                    return response.blob().then(blob => {
                        // 防重复下载检查
                        if (window.downloadInProgress) {
                            console.log('下载已在进行中，跳过重复下载');
                            return '下载已在进行中...';
                        }

                        const contentDisposition = response.headers.get('content-disposition');
                        let filename = '项目资料.zip';
                        if (contentDisposition) {
                            const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
                            if (filenameMatch && filenameMatch[1]) {
                                filename = filenameMatch[1];
                            }
                        }

                        // 保存下载数据到全局变量
                        window.pendingDownload = {
                            blob: blob,
                            filename: filename
                        };

                        // 重置按钮
                        resetButton(submitButton);

                        // 启用下载按钮
                        enableDownloadButton();

                        // 播放完成提示音
                        playCompletionSound();
                        return '批量报告生成成功，点击下载按钮保存文件';
                    });
                } else {
                    return response.text();
                }
            }).then(data => {
                if (typeof data === 'string') {
                    showNotification(data, data.includes('成功') ? 'success' : 'info');
                }
            }).catch(error => {
                console.error('Error:', error);
                resetButton(submitButton);
                showNotification('生成多个报告时发生错误: ' + error, 'error');
            });
        }

        function submitAchievementsForm() {
            const form = document.getElementById('achievements-upload-form');
            const submitButton = document.querySelector('#generate-submit');

            if (!validateAchievementsForm()) {
                return;
            }

            // 保存原始文本
            if (!submitButton.hasAttribute('data-original-text')) {
                submitButton.setAttribute('data-original-text', submitButton.innerHTML);
            }

            // 添加处理中的样式
            submitButton.classList.add('processing');
            submitButton.disabled = true;

            // 获取进度指示器元素
            let progressIndicator = submitButton.querySelector('.progress-indicator');
            if (!progressIndicator) {
                console.error('找不到进度指示器元素');
                return;
            }

            // 保存原始内容
            const buttonContent = submitButton.querySelector('.button-content');
            if (buttonContent) {
                // 保存原始内容以便后续恢复
                submitButton.setAttribute('data-original-content', buttonContent.innerHTML);

                // 更新按钮内容为进度文本
                buttonContent.innerHTML = '成果生成中...';
            }

            // 设置初始进度
            progressIndicator.style.width = '0%';

            // 模拟进度增长 - 每250ms增加1.5%，直到90%
            let currentProgress = 0;
            // 清除可能存在的旧进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }
            window.progressInterval = setInterval(() => {
                currentProgress += 1.5;
                if (currentProgress > 90) {
                    clearInterval(window.progressInterval);
                    window.progressInterval = null;
                } else {
                    progressIndicator.style.width = `${currentProgress}%`;
                }
            }, 250);

            const formData = new FormData(form);
            fetch('/achievements', {
                method: 'POST',
                body: formData
            }).then(response => {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => {
                        resetButton(submitButton);
                        if (data.success) {
                            return `文件已成功保存到本地路径：${data.message}`;
                        } else {
                            return `保存文件失败：${data.message}`;
                        }
                    });
                } else if (contentType && contentType.includes('application/zip')) {
                    return response.blob().then(blob => {
                        // 防重复下载检查
                        if (window.downloadInProgress) {
                            console.log('下载已在进行中，跳过重复下载');
                            return '下载已在进行中...';
                        }

                        const contentDisposition = response.headers.get('content-disposition');
                        let filename = '项目资料.zip';
                        if (contentDisposition) {
                            const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
                            if (filenameMatch && filenameMatch[1]) {
                                filename = filenameMatch[1];
                            }
                        }

                        // 保存下载数据到全局变量
                        window.pendingDownload = {
                            blob: blob,
                            filename: filename
                        };

                        // 重置按钮
                        resetButton(submitButton);

                        // 启用下载按钮
                        enableDownloadButton();

                        // 播放完成提示音
                        playCompletionSound();
                        return '成果材料生成成功，点击下载按钮保存文件';
                    });
                } else {
                    return response.text();
                }
            }).then(data => {
                if (typeof data === 'string') {
                    showNotification(data, data.includes('成功') ? 'success' : 'info');
                }
            }).catch(error => {
                console.error('Error:', error);
                resetButton(submitButton);
                showNotification('生成成果材料时发生错误: ' + error, 'error');
            });
        }

        socket.on('progress', function(data) {
            // 查找处于处理中状态的按钮
            const submitButton = document.querySelector('.submit-button.processing');
            if (!submitButton) return;

            // 查找进度指示器和按钮内容
            const progressIndicator = submitButton.querySelector('.progress-indicator');
            const buttonContent = submitButton.querySelector('.button-content');

            if (progressIndicator && buttonContent) {
                // 清除可能存在的模拟进度间隔
                if (window.progressInterval) {
                    clearInterval(window.progressInterval);
                    window.progressInterval = null;
                }

                // 检查是否有错误信息
                if (data.error) {
                    // 显示错误消息
                    showNotification('生成报告时发生错误: ' + data.error, 'error');

                    // 立即重置按钮
                    resetButton(submitButton);
                    return;
                }

                // 更新进度条宽度 - 添加动画效果
                progressIndicator.style.transition = 'width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
                progressIndicator.style.width = data.progress + '%';

                // 增强进度条发光效果
                if (data.progress > 0) {
                    // 根据进度增加发光强度
                    const glowIntensity = Math.min(0.8 + (data.progress / 100) * 0.2, 1);
                    const topHighlightIntensity = Math.min(0.5 + (data.progress / 100) * 0.2, 0.7);
                    const bottomShadowIntensity = Math.min(0.2 + (data.progress / 100) * 0.1, 0.3);

                    // 设置3D效果的阴影
                    progressIndicator.style.boxShadow = `
                        0 0 ${15 + data.progress/5}px rgba(249, 115, 22, ${glowIntensity}),
                        inset 0 1px ${1 + data.progress/100}px rgba(255, 255, 255, ${topHighlightIntensity}),
                        inset 0 -1px ${1 + data.progress/100}px rgba(0, 0, 0, ${bottomShadowIntensity})
                    `;

                    // 添加脉冲动画类
                    progressIndicator.classList.add('progress-active');

                    // 根据进度调整亮度
                    const brightness = 1 + (data.progress / 100) * 0.1;
                    progressIndicator.style.filter = `brightness(${brightness})`;
                }

                // 更新进度文本 - 保持简短
                if (data.progress < 100) {
                    // 简化进度消息
                    if (window.activeTab === 'single') {
                        buttonContent.innerHTML = '生成中... ' + data.progress + '%';
                    } else if (window.activeTab === 'multiple') {
                        buttonContent.innerHTML = '批量生成中... ' + data.progress + '%';
                    } else if (window.activeTab === 'achievements') {
                        buttonContent.innerHTML = '成果生成中... ' + data.progress + '%';
                    }

                    // 根据进度更新按钮文本颜色
                    buttonContent.style.color = 'white';
                    buttonContent.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.5)';
                } else {
                    // 完成时的消息
                    buttonContent.innerHTML = '完成!';

                    // 播放完成提示音
                    playCompletionSound();

                    // 延迟重置按钮
                    setTimeout(() => {
                        resetButton(submitButton);
                    }, 1000);
                }
            }
        });

        // 添加错误处理事件
        socket.on('error', function(data) {
            console.log('收到错误事件:', data);
            
            // 查找所有可能处于处理中状态的按钮
            const processingButtons = document.querySelectorAll('.submit-button.processing, button.processing, #generate-submit.processing, #batchGenerateBtn.processing, #achievementsBtn.processing');
            
            processingButtons.forEach(button => {
                console.log('重置按钮:', button.id || button.className);
                resetButton(button);
            });

            // 清理进度条
            const progressIndicators = document.querySelectorAll('.progress-indicator');
            progressIndicators.forEach(indicator => {
                indicator.style.width = '0%';
                indicator.classList.remove('progress-active');
            });

            // 显示错误消息
            const errorMsg = data.error || data.message || '未知错误';
            showNotification('生成报告时发生错误: ' + errorMsg, 'error');
            
            // 清理模拟进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
                window.progressInterval = null;
            }
        });

        // 添加失败处理事件
        socket.on('generation_failed', function(data) {
            console.log('收到失败事件:', data);
            
            // 查找所有可能处于处理中状态的按钮
            const processingButtons = document.querySelectorAll('.submit-button.processing, button.processing, #generate-submit.processing, #batchGenerateBtn.processing, #achievementsBtn.processing');
            
            processingButtons.forEach(button => {
                console.log('重置按钮:', button.id || button.className);
                resetButton(button);
            });

            // 清理进度条
            const progressIndicators = document.querySelectorAll('.progress-indicator');
            progressIndicators.forEach(indicator => {
                indicator.style.width = '0%';
                indicator.classList.remove('progress-active');
            });

            // 显示错误消息
            const errorMsg = data.error || data.message || '未知原因';
            showNotification('生成报告失败: ' + errorMsg, 'error');
            
            // 清理模拟进度间隔
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
                window.progressInterval = null;
            }
        });

        // 播放完成提示音的函数
        function playCompletionSound() {
            console.log('尝试播放完成提示音');

            // 创建新的音频元素（这样可以避免一些浏览器的限制）
            const sound = new Audio();

            // 添加多个音源以提高兼容性
            sound.src = 'https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3';

            // 设置音量
            sound.volume = 0.8;

            // 添加加载事件
            sound.addEventListener('canplaythrough', function() {
                console.log('音频已加载，准备播放');

                // 播放音频
                sound.play()
                    .then(() => {
                        console.log('提示音播放成功');

                        // 显示通知
                        showNotification('报告生成完成！', 'success');
                    })
                    .catch(error => {
                        console.warn('无法播放提示音:', error);

                        // 如果浏览器阻止自动播放，显示通知
                        showNotification('报告生成完成！', 'success');

                        // 尝试使用系统通知
                        try {
                            if (Notification && Notification.permission === "granted") {
                                new Notification('报告生成完成', {
                                    icon: 'https://cdn-icons-png.flaticon.com/512/190/190411.png',
                                    silent: false
                                });
                            } else if (Notification && Notification.permission !== "denied") {
                                Notification.requestPermission().then(function(permission) {
                                    if (permission === "granted") {
                                        new Notification('报告生成完成', {
                                            icon: 'https://cdn-icons-png.flaticon.com/512/190/190411.png',
                                            silent: false
                                        });
                                    }
                                });
                            }
                        } catch (e) {
                            console.warn('无法使用系统通知:', e);
                        }
                    });
            });

            // 添加错误处理
            sound.addEventListener('error', function(e) {
                console.error('音频加载失败:', e);
                showNotification('报告生成完成！', 'success');
            });

            // 开始加载音频
            sound.load();
        }

        // RDPS文件上传处理
        document.getElementById('rdps_file_multi').addEventListener('change', function() {
            const parseBtn = document.getElementById('parse-rdps-btn');
            const file = this.files[0];
            
            if (file) {
                parseBtn.style.display = 'flex';
                showNotification('RDPS表已上传，点击"填充"按钮解析项目信息', 'info');
            } else {
                parseBtn.style.display = 'none';
            }
        });

        // 解析RDPS文件的函数
        function parseRdpsFile() {
            const fileInput = document.getElementById('rdps_file_multi');
            const parseBtn = document.getElementById('parse-rdps-btn');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                showNotification('请先上传RDPS表文件', 'error');
                return;
            }

            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('rdps_file', file);

            // 禁用按钮并显示加载状态
            parseBtn.disabled = true;
            parseBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>解析中';

            fetch('/parse_rdps', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    fillProjectInfo(data.projects);
                } else {
                    showNotification(data.error || '解析RDPS表失败', 'error');
                }
            })
            .catch(error => {
                console.error('解析RDPS表出错:', error);
                showNotification('解析RDPS表时发生网络错误', 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                parseBtn.disabled = false;
                parseBtn.innerHTML = '<i class="fas fa-magic"></i>填充';
            });
        }

        // 填充项目信息到表单
        function fillProjectInfo(projects) {
            if (!projects || projects.length === 0) {
                showNotification('没有找到项目信息', 'warning');
                return;
            }

            // 确保有足够的报告表单
            const currentSections = document.querySelectorAll('.report-section').length;
            const neededSections = projects.length;

            if (neededSections > currentSections) {
                // 生成更多报告表单
                generateReportSections(neededSections);
                showNotification(`已自动增加到${neededSections}个报告表单`, 'info');
            }

            // 填充每个项目的信息
            projects.forEach((project, index) => {
                const sectionIndex = index + 1;
                
                // 填充项目名称
                const projectNameInput = document.getElementById(`project_name_${sectionIndex}`);
                if (projectNameInput) {
                    projectNameInput.value = project.project_name || '';
                }

                // 填充RD序号
                const rdNumberSelect = document.getElementById(`rd_number_${sectionIndex}`);
                if (rdNumberSelect && project.rd_number) {
                    // 检查RD序号格式，确保与选项匹配
                    let rdValue = project.rd_number;
                    if (!rdValue.startsWith('RD')) {
                        rdValue = `RD${rdValue.padStart(2, '0')}`;
                    }
                    
                    // 设置选择的值
                    for (let option of rdNumberSelect.options) {
                        if (option.value === rdValue) {
                            rdNumberSelect.value = rdValue;
                            break;
                        }
                    }
                }

                // 填充开始时间
                const startDateInput = document.getElementById(`start_date_${sectionIndex}`);
                if (startDateInput && project.start_date) {
                    startDateInput.value = project.start_date;
                }

                // 填充结束时间
                const endDateInput = document.getElementById(`end_date_${sectionIndex}`);
                if (endDateInput && project.end_date) {
                    endDateInput.value = project.end_date;
                }

                // 自动展开项目信息表单
                const projectSection = document.querySelector(`.report-section:nth-child(${sectionIndex}) .form-section`);
                if (projectSection) {
                    projectSection.setAttribute('open', '');
                }
            });

            showNotification(`成功填充${projects.length}个项目的信息`, 'success');
        }

        // 修改generateReportSections函数，使其支持指定数量
        function generateReportSections(count) {
            const container = document.getElementById('report-sections');
            const currentSections = container.children.length;
            
            // 如果当前数量已经足够，则不需要添加
            if (currentSections >= count) {
                return;
            }

            // 生成新的报告表单
            for (let i = currentSections + 1; i <= count; i++) {
                const section = document.createElement('div');
                section.className = 'report-section';

                // 生成RD序号选项
                let optionsHtml = '<option value="">请选择RD序号</option>';
                for (let j = 1; j <= 20; j++) {
                    const rdValue = `RD${j < 10 ? '0' : ''}${j}`;
                    optionsHtml += `<option value="${rdValue}">${rdValue}</option>`;
                }

                section.innerHTML = `
                    <h3><i class="fas fa-file-alt"></i> 报告 ${i}</h3>

                    <details class="form-section" closed>
                        <summary><i class="fas fa-info-circle"></i> 项目信息</summary>
                        <div class="form-section-content">
                            <div class="form-group">
                                <label for="project_name_${i}"><i class="fas fa-project-diagram"></i> <span>项目名称：</span></label>
                                <input type="text" name="project_name_${i}" id="project_name_${i}" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label for="rd_number_${i}"><i class="fas fa-hashtag"></i> <span>RD序号：</span></label>
                                <select name="rd_number_${i}" id="rd_number_${i}">
                                    ${optionsHtml}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="start_date_${i}"><i class="fas fa-calendar-alt"></i> <span>开始时间：</span></label>
                                <input type="date" name="start_date_${i}" id="start_date_${i}">
                            </div>
                            <div class="form-group">
                                <label for="end_date_${i}"><i class="fas fa-calendar-check"></i> <span>结束时间：</span></label>
                                <input type="date" name="end_date_${i}" id="end_date_${i}">
                            </div>
                        </div>
                    </details>

                    <details class="form-section" closed>
                        <summary><i class="fas fa-file-upload"></i> 技术文件</summary>
                        <div class="form-section-content">
                            <div class="form-group">
                                <label for="file1_${i}"><i class="fas fa-file-upload"></i> <span>技术文件1：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="file1_${i}" id="file1_${i}">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('file1_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="file2_${i}"><i class="fas fa-file-upload"></i> <span>技术文件2：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="file2_${i}" id="file2_${i}">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('file2_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="file3_${i}"><i class="fas fa-file-upload"></i> <span>技术文件3：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="file3_${i}" id="file3_${i}">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('file3_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="file4_${i}"><i class="fas fa-file-upload"></i> <span>技术文件4：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="file4_${i}" id="file4_${i}">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('file4_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="file5_${i}"><i class="fas fa-file-upload"></i> <span>技术文件5：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="file5_${i}" id="file5_${i}">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('file5_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </details>

                    <details class="form-section" closed>
                        <summary><i class="fas fa-file-excel"></i> 辅助账目</summary>
                        <div class="form-section-content">
                            <div class="form-group">
                                <label for="excel_2022_${i}"><i class="fas fa-file-excel"></i> <span>2022辅助账（Excel）：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="excel_2022_${i}" id="excel_2022_${i}" accept=".xlsx, .xls">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2022_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                    <span class="file-status">文件已选择</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="excel_2023_${i}"><i class="fas fa-file-excel"></i> <span>2023辅助账（Excel）：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="excel_2023_${i}" id="excel_2023_${i}" accept=".xlsx, .xls">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2023_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                    <span class="file-status">文件已选择</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="excel_2024_${i}"><i class="fas fa-file-excel"></i> <span>2024辅助账（Excel）：</span></label>
                                <div class="file-input-container">
                                    <input type="file" name="excel_2024_${i}" id="excel_2024_${i}" accept=".xlsx, .xls">
                                    <button type="button" class="clear-file-btn" onclick="clearFileInput('excel_2024_${i}')">
                                        <i class="fas fa-times"></i>清除
                                    </button>
                                    <span class="file-status">文件已选择</span>
                                </div>
                            </div>
                        </div>
                    </details>
                `;
                container.appendChild(section);
            }

            // 重新设置文件输入监听器
            setTimeout(() => {
                setupFileInputListeners();
            }, 100);
        }
    </script>

    <style>
        /* 确保页面内容有足够的高度，使页脚始终可见 */
        html, body {
            min-height: 100vh;
        }

        body {
            display: flex;
            flex-direction: column;
            padding-bottom: 60px; /* 增加底部内边距 */
        }

        /* 确保主要内容区域占据所有可用空间并均匀分布内容 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* 添加硬件加速和性能优化 */
        .tab-button,
        .submit-button,
        .notification,
        .form-section summary,
        .clear-file-btn,
        .file-input-container,
        .floating-element,
        .particle,
        .add-more-button,
        .progress-bar {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
            will-change: transform, opacity;
        }

        /* 在移动设备上禁用或简化某些动画 */
        @media (max-width: 768px) {
            .floating-element {
                animation-duration: 40s; /* 减慢动画速度 */
            }

            .tab-button.active::after,
            .submit-button::after {
                animation: none; /* 禁用某些动画效果 */
                opacity: 0.3;
            }

            /* 减少阴影复杂度 */
            .card,
            .form-section,
            .submit-button,
            .tab-button {
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
        }

        /* 优化重绘和重排 */
        .container,
        .card,
        .form-section,
        .form-group,
        .file-input-container {
            contain: content; /* 告诉浏览器这个元素及其内容尽可能独立于文档的其余部分 */
        }
    </style>
</body>
</html>
