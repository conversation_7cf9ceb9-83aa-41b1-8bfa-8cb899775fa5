import os
from flask import Flask, session, redirect, url_for, render_template_string, request, make_response, send_file, jsonify, flash
from flask_socketio import SocketIO, emit
import requests
from docx import Document
from docx.shared import Pt, Cm, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL, WD_TABLE_ALIGNMENT
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from docx.table import _Cell
import time
import re
from pathlib import Path
import pandas as pd
import logging
from datetime import datetime, timedelta
import random
import math
import zipfile
import io
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Alignment, Font
import tempfile
import shutil
import json
import hashlib
from functools import wraps
from collections import defaultdict
import psutil
import pypdf

app = Flask(__name__)
app.config['SECRET_KEY'] = 'rinwriter_secret_key_2025'  # 用于会话加密的密钥
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100 MB
app.config['ENABLE_LOCAL_SAVE'] = True  # 是否启用本地保存功能
app.config['DEFAULT_DOWNLOAD_NAME'] = "项目资料"  # 设置默认下载文件名
app.config['USERNAME'] = 'rinch@'  # 默认用户名
app.config['PASSWORD'] = '18061879967@'  # 默认密码
app.config['SESSION_PERMANENT'] = False  # 会话在浏览器关闭后过期
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)  # 会话最长持续30分钟
app.config['SESSION_TYPE'] = 'filesystem'  # 使用文件系统存储会话
app.config['SESSION_USE_SIGNER'] = True  # 对cookie进行签名
app.config['SESSION_COOKIE_SECURE'] = False  # 允许非HTTPS连接使用cookie
app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止JavaScript访问cookie
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # 防止CSRF攻击
app.config['ADMIN_USERNAME'] = 'admin'  # 管理员用户名
app.config['ADMIN_PASSWORD'] = 'admin123'  # 管理员密码

# 安全配置
ADMIN_PATH = 'sys_ae2c57ee46f5'  # 安全管理员路径
SESSION_TIMEOUT = 30  # 会话超时时间（分钟）
MAX_LOGIN_ATTEMPTS = 5  # 最大登录尝试次数
LOGIN_BLOCK_TIME = 15  # 登录失败后封锁时间（分钟）
API_RATE_LIMIT = 60  # API请求频率限制（每分钟）

# 全局变量
active_sessions = {}  # 活跃会话（普通用户）
admin_sessions = {}  # 管理员会话（单独管理）
login_attempts = defaultdict(list)  # 登录尝试记录
rate_limits = defaultdict(list)  # API频率限制记录
backup_counter = 0  # 备份计数器

# 系统状态缓存
_system_stats_cache = None
_stats_cache_time = None

# 记录服务器启动时间
server_start_time = datetime.now()

# 清除启动时的登录限制（防止服务器重启后仍被锁定）
login_attempts.clear()
print("⚡ 服务器启动时已清除所有登录限制记录")

socketio = SocketIO(app)

# 配置简洁的日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

# 设置第三方库的日志级别为WARNING，减少噪音
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
logging.getLogger('requests.packages.urllib3').setLevel(logging.WARNING)
logging.getLogger('werkzeug').setLevel(logging.WARNING)

# 预算调整函数
def adjust_budget_amount(actual_amount, is_total=False):
    """
    将实际金额调整为符合预算要求的金额
    
    Args:
        actual_amount (float): 实际金额
        is_total (bool): 是否为总计金额，总计金额会调整到个位为0或5
        
    Returns:
        float: 调整后的预算金额（只能比实际金额多，不能少）
    """
    try:
        if not actual_amount or actual_amount == 0:
            return 0.0
            
        actual = float(actual_amount)
        
        if is_total:
            # 总计金额调整到个位为0或5的整数
            # 先向上取整到最接近的整数
            ceil_amount = int(actual) + (1 if actual > int(actual) else 0)
            
            # 获取个位数
            ones_digit = ceil_amount % 10
            
            if ones_digit == 0 or ones_digit == 5:
                return float(ceil_amount)
            elif ones_digit < 5:
                return float(ceil_amount + (5 - ones_digit))
            else:
                return float(ceil_amount + (10 - ones_digit))
        else:
            # 单项金额调整为整数（向上取整）
            return float(int(actual) + (1 if actual > int(actual) else 0))
            
    except (ValueError, TypeError):
        return 0.0

def adjust_budget_data(budget_data):
    """
    调整整个预算数据结构
    
    Args:
        budget_data (dict): 原始预算数据
        
    Returns:
        tuple: (调整后的预算数据, 原始数据的总计)
    """
    adjusted_budget_data = {}
    original_total = 0.0
    
    for year, subjects in budget_data.items():
        adjusted_subjects = {}
        year_total = 0.0
        original_year_total = 0.0
        
        for subject, amount in subjects.items():
            if subject == "合计":
                continue
                
            if amount and pd.notna(amount):
                original_amount = float(amount)
                original_year_total += original_amount
                # 调整单项金额
                adjusted_amount = adjust_budget_amount(float(amount), is_total=False)
                adjusted_subjects[subject] = adjusted_amount
                year_total += adjusted_amount
            else:
                adjusted_subjects[subject] = 0.0
        
        # 年度总计直接是各调整后项目的合计（不再调整个位）
        adjusted_year_total = year_total
        adjusted_subjects["合计"] = adjusted_year_total
        adjusted_budget_data[year] = adjusted_subjects
        original_total += original_year_total
    
    return adjusted_budget_data, original_total

# 用户数据管理
def load_users():
    """加载用户数据"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if os.path.exists('users.json'):
                with open('users.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data
            return {}
        except (json.JSONDecodeError, FileNotFoundError) as e:
            if attempt < max_retries - 1:
                time.sleep(0.1)  # 短暂等待后重试
                logger.warning(f"加载用户数据失败，重试 {attempt + 1}/{max_retries}: {e}")
                continue
            else:
                logger.error(f"加载用户数据失败，已用尽所有重试: {e}")
                return {}
        except Exception as e:
            logger.error(f"加载用户数据失败: {e}")
            return {}

def save_users(users_data):
    """保存用户数据（带原子操作和文件锁）"""
    import tempfile
    import shutil
    
    # 尝试导入fcntl，Windows上不可用
    try:
        import fcntl
        HAS_FCNTL = True
    except ImportError:
        HAS_FCNTL = False
    
    max_retries = 5  # 增加重试次数
    for attempt in range(max_retries):
        temp_file = None
        lock_file = None
        try:
            # 创建锁文件，防止并发写入
            lock_file_path = 'users.json.lock'
            try:
                lock_file = open(lock_file_path, 'w')
                # 尝试获取文件锁（仅在支持的系统上）
                if HAS_FCNTL:
                    try:
                        fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    except:
                        pass  # 忽略文件锁错误，继续执行
            except:
                pass  # 如果无法创建锁文件，继续执行
            
            # 使用临时文件进行原子写入
            with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', 
                                           dir='.', prefix='users_temp_', 
                                           suffix='.json', delete=False) as temp_file:
                # 写入数据到临时文件
                json.dump(users_data, temp_file, ensure_ascii=False, indent=2)
                temp_name = temp_file.name
            
            # 原子性地替换原文件
            if os.path.exists('users.json'):
                shutil.move('users.json', 'users.json.bak')
            shutil.move(temp_name, 'users.json')
            
            # 删除备份文件
            if os.path.exists('users.json.bak'):
                os.remove('users.json.bak')
            
            # 释放文件锁
            if lock_file:
                try:
                    if HAS_FCNTL:
                        fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                    lock_file.close()
                    os.remove(lock_file_path)
                except:
                    pass
                
            return True
            
        except Exception as e:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.remove(temp_file.name)
                except:
                    pass
            
            # 释放文件锁
            if lock_file:
                try:
                    if HAS_FCNTL:
                        fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                    lock_file.close()
                    os.remove('users.json.lock')
                except:
                    pass
                    
            if attempt < max_retries - 1:
                wait_time = 0.2 * (attempt + 1)  # 渐进式等待时间
                time.sleep(wait_time)
                logger.warning(f"保存用户数据失败，重试 {attempt + 1}/{max_retries}: {e}")
                continue
            else:
                logger.error(f"保存用户数据失败，已用尽所有重试: {e}")
                
                # 尝试恢复备份
                if os.path.exists('users.json.bak'):
                    try:
                        shutil.move('users.json.bak', 'users.json')
                        logger.info("已恢复用户数据备份")
                    except:
                        pass
                return False

def backup_users_data():
    """备份用户数据"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        backup_file = os.path.join(backup_dir, f'users_backup_{timestamp}.json')
        if os.path.exists('users.json'):
            shutil.copy2('users.json', backup_file)
            
            # 只保留最近10个备份
            backup_files = sorted([f for f in os.listdir(backup_dir) if f.startswith('users_backup_')])
            while len(backup_files) > 10:
                oldest = backup_files.pop(0)
                os.remove(os.path.join(backup_dir, oldest))
                
            logger.info(f"用户数据备份完成: {backup_file}")
            return backup_file
    except Exception as e:
        logger.error(f"备份用户数据失败: {e}")
    return None

def restore_users_data(backup_file):
    """恢复用户数据"""
    try:
        backup_path = os.path.join('backups', backup_file)
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, 'users.json')
            logger.info(f"用户数据恢复完成: {backup_file}")
            return True
    except Exception as e:
        logger.error(f"恢复用户数据失败: {e}")
    return False

def get_system_stats():
    """获取系统状态信息（带缓存机制）"""
    global _system_stats_cache, _stats_cache_time
    
    current_time = time.time()
    
    # 使用5秒缓存避免频繁计算
    if (_system_stats_cache is not None and 
        _stats_cache_time is not None and 
        current_time - _stats_cache_time < 5):
        return _system_stats_cache
    
    try:
        # 使用更短的interval避免阻塞
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 系统负载
        if hasattr(psutil, 'getloadavg'):
            load_avg = psutil.getloadavg()
        else:
            # Windows系统没有getloadavg，用CPU使用率模拟
            load_avg = (cpu_percent/100, cpu_percent/100, cpu_percent/100)
        
        # 网络统计（简化版）
        network_io = psutil.net_io_counters()
        
        # 计算在线用户数：优先使用内存中的会话数据
        online_count = len(active_sessions)
        
        # 计算总报告数：使用缓存机制
        try:
            users = load_users()
            total_reports = sum(user_data.get('used_reports', 0) for user_data in users.values())
        except Exception as e:
            logger.warning(f"获取报告统计失败，使用缓存值: {e}")
            total_reports = _system_stats_cache.get('total_reports', 0) if _system_stats_cache else 0
        
        stats = {
            'cpu_usage': round(cpu_percent, 1),
            'memory_usage': round(memory_percent, 1),
            'load_1': round(load_avg[0], 2),
            'load_5': round(load_avg[1], 2),
            'load_15': round(load_avg[2], 2),
            'network_sent': network_io.bytes_sent,
            'network_recv': network_io.bytes_recv,
            'online_users': online_count,
            'total_reports': total_reports
        }
        
        # 更新缓存
        _system_stats_cache = stats
        _stats_cache_time = current_time
        
        return stats
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        # 如果有缓存，返回缓存数据
        if _system_stats_cache:
            return _system_stats_cache
        # 否则返回默认值
        return {
            'cpu_usage': 0,
            'memory_usage': 0,
            'load_1': 0,
            'load_5': 0,
            'load_15': 0,
            'network_sent': 0,
            'network_recv': 0,
            'online_users': 0,
            'total_reports': 0
        }

def create_download_zip(files_dict):
    """
    创建包含多个文件的ZIP压缩包，用于客户端下载

    Args:
        files_dict: 字典，键为文件在ZIP中的名称，值为文件的完整路径

    Returns:
        BytesIO对象，包含ZIP文件内容
    """
    memory_file = io.BytesIO()
    with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
        for file_name, file_path in files_dict.items():
            if os.path.exists(file_path):
                zf.write(file_path, file_name)
            else:
                logger.warning(f"文件不存在，无法添加到ZIP: {file_path}")

    memory_file.seek(0)
    return memory_file

def send_zip_file(memory_file, filename_prefix="项目资料"):
    """
    发送ZIP文件给客户端下载，确保文件名正确设置

    Args:
        memory_file: BytesIO对象，包含ZIP文件内容
        filename_prefix: 文件名前缀，默认为"项目资料"

    Returns:
        Flask响应对象
    """
    from flask import send_file

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 使用拼音作为文件名
    filename = "xiangmuziliao-" + timestamp + ".zip"

    # 确保memory_file指针在开始位置
    memory_file.seek(0)

    # 使用send_file发送内存文件
    return send_file(
        memory_file,
        mimetype='application/zip',
        as_attachment=True,
        download_name=filename
    )

API_KEY = "sk-gWplx0rvJUOtfw8jEYGiBW65PFePNXhQeYtARRZ3KtiOcnjc"  # 请替换为你的实际 API 密钥
UPLOAD_URL = "https://api.moonshot.cn/v1/files"
CHAT_URL = "https://api.moonshot.cn/v1/chat/completions"

# 错误处理工具函数
def get_user_friendly_error(error_type, original_error=None):
    """
    将技术性错误转换为用户友好的错误信息
    """
    error_map = {
        'api_timeout': '❌ AI服务响应超时，请稍后重试',
        'api_connection': '❌ 无法连接到AI服务，请检查网络连接',
        'api_quota': '❌ AI服务配额不足，请联系管理员',
        'api_key': '❌ AI服务认证失败，请联系管理员',
        'file_upload': '📁 文件上传失败，请检查文件格式',
        'file_processing': '📁 文件处理失败，请检查文件内容',
        'excel_read': '📊 Excel文件读取失败，请检查文件格式',
        'zip_extract': '📦 ZIP文件解压失败，请检查文件完整性',
        'invalid_data': '📝 数据格式错误，请检查输入内容',
        'permission_denied': '🔒 没有操作权限，请联系管理员',
        'disk_space': '💾 磁盘空间不足，请联系管理员',
        'unknown': '❌ 系统错误，请稍后重试'
    }
    
    user_msg = error_map.get(error_type, error_map['unknown'])
    
    # 如果有原始错误，简化后追加
    if original_error:
        simplified_error = str(original_error)
        # 提取关键错误信息
        if 'timeout' in simplified_error.lower():
            return error_map['api_timeout']
        elif 'connection' in simplified_error.lower():
            return error_map['api_connection']
        elif 'quota' in simplified_error.lower() or 'rate limit' in simplified_error.lower():
            return error_map['api_quota']
        elif 'unauthorized' in simplified_error.lower() or 'api key' in simplified_error.lower():
            return error_map['api_key']
        elif 'permission' in simplified_error.lower():
            return error_map['permission_denied']
        elif 'space' in simplified_error.lower() or 'disk' in simplified_error.lower():
            return error_map['disk_space']
    
    return user_msg

def log_and_emit_error(error_type, operation, original_error=None, emit_socket=True):
    """
    统一的错误日志记录和Socket.IO事件发送
    """
    # 简洁的日志记录
    if original_error:
        logger.error(f"{operation} 失败: {str(original_error)[:100]}")
    else:
        logger.error(f"{operation} 失败: {error_type}")
    
    # 获取用户友好的错误信息
    user_error = get_user_friendly_error(error_type, original_error)
    
    # 发送Socket.IO事件
    if emit_socket:
        try:
            socketio.emit('error', {'error': user_error})
        except Exception as e:
            logger.warning(f"发送错误事件失败: {e}")
    
    return user_error

class OpenAI:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url

    def files(self):
        return Files(self.api_key, self.base_url)

    def chat(self):
        return Chat(self.api_key, self.base_url)

class Files:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url

    def create(self, file, purpose):
        url = f"{self.base_url}/files"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        files = {"file": file, "purpose": purpose}
        response = requests.post(url, headers=headers, files=files)
        return response.json()

class Chat:
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url

    def create_completion(self, model, messages, temperature, timeout=30):
        url = f"{self.base_url}/chat/completions"
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": model, "messages": messages, "temperature": temperature}
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=timeout)
            response.raise_for_status()  # 抛出HTTP错误
            result = response.json()
            
            # 检查API返回的错误
            if 'error' in result:
                error_msg = result['error'].get('message', '未知API错误')
                raise ValueError(f"API错误: {error_msg}")
            
            return result
            
        except requests.exceptions.Timeout:
            raise ValueError("API响应超时")
        except requests.exceptions.ConnectionError:
            raise ValueError("无法连接到API服务")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise ValueError("API密钥无效")
            elif e.response.status_code == 429:
                raise ValueError("API调用频率过高")
            elif e.response.status_code == 500:
                raise ValueError("API服务器内部错误")
            else:
                raise ValueError(f"API请求失败: {e.response.status_code}")
        except ValueError:
            raise  # 重新抛出我们自定义的错误
        except Exception as e:
            raise ValueError(f"API调用失败: {str(e)}")

client = OpenAI(api_key=API_KEY, base_url="https://api.moonshot.cn/v1")
SUBJECT_MAPPING = {
    "人员人工费用": "一、直接从事研发活动的本企业在职人员人工费用",
    "直接投入费用": "二、研发活动直接投入的费用",
    "直接费用-不含人员人工费用": "二、研发活动直接投入的费用",
    "折旧费用与长期待摊费用": "三、折旧费用与长期待摊费用",
    "设计费用": "四、设计费用",
    "装备调试费用": "五、装备调试费",
    "装备调试费用与试验费用": "五、装备调试费",
    "无形资产摊销费用": "六、专门用于研发活动的无形资产摊销费",
    "现场试验费用": "七、勘探、开发技术的现场试验费",
    "论证、评审费用": "八、研发成果的论证、鉴定、评审、验收费",
    "其他费用": "九、与研发活动直接相关的其他费用",
    "其他费用（含小计）": "九、与研发活动直接相关的其他费用",
    "其他费用(不含小计)": "九、与研发活动直接相关的其他费用",
    "合计": "合计"
}

IGNORE_SUBJECTS = [
    "内部研究开发费用",
    "委托外部研究开发费用",
    "其中:境内的外部研发费用",
    "研究开发费用（内、外部）小计",
    "公司法定代表人：",
    "主管会计工作的公司负责人：",
    "公司会计机构负责人：",
    "公司盖章："
]

def clean_content(content, section_title):
    avoid_words = [
        "一种", "实用新型专利", "发明专利", "专利", "本专利", "本实用", "本发明", "该专利", "该发明",
        "旨在", "专利说明", "结构", "新型", "技术方案", "智能", "环保", "第一", "第二", "该实用",
        "一号", "二号", "三号", "本体", "提示词", "格式", "要求", "示例", "根据", "文件内容", "以下是",
        "动态生成", "技术指标", "参数", "另一个", "还包括", "最后", "合理推导", "若", "未明确", "需实际确定",
        "具体数值未给出", "描述", "拟定"
    ]

    # 替换禁用词时，使用中性词或空字符串，并尝试保持句子完整性
    for word in avoid_words:
        if word in ["该", "第一", "第二"]:  # 特定指代词替换为中性表达
            content = content.replace(word, "此") if word == "该" else content.replace(word, "")
        else:
            content = content.replace(word, "")

    content = re.sub(r'\n+', '\n', content)
    content = re.sub(r'\*+', '', content)
    content = re.sub(r'#+', '', content)
    content = re.sub(r'_{2,}', '', content)
    content = re.sub(r'```', '', content)
    content = re.sub(r'`', '', content)
    content = re.sub(r'[$$                                      $${}]', '', content)
    content = re.sub(r'(\d{4}\.\d{2}月)(\d{4}\.\d{2}月)(：)', r'\1-\2\3', content)
    # 修复正则表达式，避免使用可变长度的look-behind
    # 先标记行首的 $$N$$ 模式，然后移除非行首的模式
    content = re.sub(r'^\$\$\s*[1-9]\s*\$\$', 'LINE_START_MARKER', content, flags=re.MULTILINE)  # 标记行首
    content = re.sub(r'\$\$\s*[1-9]\s*\$\$', '', content)  # 移除所有其他模式
    content = re.sub(r'LINE_START_MARKER', '$$', content)  # 恢复行首标记

        # 特殊处理"2. 项目开发的目的和意义"部分 - 简化逻辑，避免重复生成
    if section_title == "2. 项目开发的目的和意义":
        # 检查是否有重复的"意义："部分
        significance_matches = list(re.finditer(r'意义[：:]', content))
        if len(significance_matches) > 1:
            # 如果有多个"意义："，只保留第一个
            first_significance_pos = significance_matches[0].start()
            second_significance_pos = significance_matches[1].start()
            # 保留从开头到第二个"意义："之前的内容
            content = content[:second_significance_pos].strip()
        
        # 确保基本格式正确 - 如果缺少意义部分，让AI重新生成而不使用套话
        if not re.search(r'目的[：:]', content):
            content = f"目的：　　{content}"
        # 如果缺少意义部分，保持原状，让AI生成完整内容而不添加套话

    # 处理其他特殊部分
    if section_title == "4. 达到的主要技术或经济指标":
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if not re.match(r'^\（\d\）\s*', line.strip()):
                # 检查是否包含技术单位
                if not re.search(r'[：:]\s*[\d\.±]+\s*(毫米|米/分钟|次/秒|米|厘米|微米|纳米|千克|克|毫克|升|毫升|瓦|千瓦|伏|安培|欧姆|赫兹|帕斯卡|牛顿|焦耳|流明|坎德拉|摄氏度|华氏度|开尔文|分贝|像素|比特|字节|兆赫|千赫|兆字节|千字节|吉字节|特字节)', line):
                    # 如果不包含技术单位，尝试添加适当的单位
                    if re.search(r'精度|误差|偏差|变形', line):
                        # 精度类指标添加毫米单位
                        if not re.search(r'[：:]\s*[\d\.]+', line):
                            line = re.sub(r'([：:]\s*[\d\.]+)(%|％)', r'\1 毫米', line)
                    elif re.search(r'速度|速率|效率', line):
                        # 速度类指标添加米/分钟或次/秒单位
                        if not re.search(r'[：:]\s*[\d\.]+\s*(米/分钟|次/秒)', line):
                            if re.search(r'传输|数据', line):
                                line = re.sub(r'([：:]\s*[\d\.]+)(%|％)?', r'\1 次/秒', line)
                            else:
                                line = re.sub(r'([：:]\s*[\d\.]+)(%|％)?', r'\1 米/分钟', line)
                lines[i-1] = f"（{i}）{line.strip()}"
        content = '\n\n'.join(lines)
    if section_title in ["2. 关键技术", "3. 创新点"]:
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if not re.match(r'^[①②③④⑤⑥⑦⑧⑨]\s*', line.strip()):
                lines[i] = f"① {line.strip()}" if i == 0 else f"② {line.strip()}" if i == 1 else f"③ {line.strip()}" if i == 2 else f"④ {line.strip()}"
        content = '\n\n'.join(lines)
    content = re.sub(r'\n\s*\n+', '\n\n', content)
    return content.strip()

def generate_report_with_kimi(file_contents, company_name, start_date, end_date, project_name):
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError as e:
        logger.error(f"日期格式错误：{e}")
        raise ValueError("日期格式错误，请使用 YYYY-MM-DD 格式")

    if end < start:
        logger.error("结束日期不能早于开始日期")
        raise ValueError("结束日期不能早于开始日期")

    if (end - start).days <= 0:
        logger.error("项目时间跨度必须大于 0")
        raise ValueError("项目时间跨度必须大于 0")

    total_days = (end - start).days
    total_months = total_days // 30 + (1 if total_days % 30 > 0 else 0)
    stages = 4
    months_per_stage = total_months // stages
    extra_months = total_months % stages

    outline = [
        ("一、立项依据", ""),
        ("1. 国内外现状、水平和发展趋势",
         "根据项目名称'{project_name}'，撰写专业技术分析，总字数500字，形成2-3个自然流畅的段落，每段开头必须有两个全角空格缩进（　　）。内容要求：1）从多角度描述该技术领域现状，可选择不同切入点（如技术成熟度、应用范围、产业规模、市场竞争等）；2）客观分析国内外技术水平，既要提及差距，也要突出国内优势（如制造成本、市场规模、应用创新、产业链完整性、研发速度等方面的优势），避免一味强调落后；3）描述发展趋势时要有变化，可从技术演进路径、应用场景扩展、产业格局调整、竞争态势变化等不同维度阐述，表达方式要多样化。严禁提及任何具体公司名称、企业名称或上传文件中的具体技术内容，保持通用性技术描述。写作要求：表达方式要有随机性和变化，避免每次都用相同的描述模式；严禁使用'展望未来X年'、'预计未来X-X年'、'随着技术发展'、'在技术推动下'等固定套路表述；禁止使用'首先...其次...最后'、'一方面...另一方面'等程式化结构；避免'该领域'、'该技术'等重复指代；句式结构要丰富多变；段落衔接要自然流畅；严禁提及'绿色环保'、'节能减排'、'智能化'、'可持续发展'、'环境友好'、'低碳'、'生态'等环保智能化概念；用词精准且有变化，体现专业技术文档的表达水准。"),
        ("2. 项目开发的目的和意义",
         "严格按照以下格式生成，目的与意义要形成逻辑关联：\n目的：　　[目的内容，150-200字，以两个全角空格开头]\n\n意义：　　[意义内容，150-200字，以两个全角空格开头，一段话形式]\n\n写作要求：目的部分要具体提炼当前存在的技术或应用问题，用具体的问题描述替代泛泛而谈；意义部分以一段话形式阐述，简明扼要地说明解决目的中提到的问题后带来的主要价值和效果，不要分条列举，不要过于详细。严禁使用以下套话：'本项目具有重要的实际应用价值和市场前景''通过提高产品性能和质量，可以增强企业竞争力''项目成果将有助于提升企业的技术水平和市场竞争力''为企业的可持续发展奠定基础''为用户提供更可靠、更高效的产品体验'等通用表述。要求内容具体化，体现项目解决特定问题后的独特价值。禁止使用'专利'、'本专利'、'本实用'、'本发明'、'该专利'、'该发明'等词汇。不提及上传文件名称。"),
        ("3. 本项目达到的技术水平及市场前景",
         "本项目所在领域的市场发展大方向，字数控制在300字，以一整段话说明，段落开头必须有两个全角空格缩进（　　），不详细介绍具体技术内容，不过多提及环保和智能，内容需符合实际行业趋势，不使用'综上所述'等总结性表述，避免使用'专利'、'本专利'、'本实用'、'本发明'、'该专利'、'该发明'等词汇。"),
        ("二、开发内容和目标", ""),
        ("1. 研发的主要内容",
         "从多个上传文件内容融合后自然概述研发的主要方向，字数控制在150字，形成一段话，段落开头必须有两个全角空格缩进（　　），不提及上传文件名称，不提及公司名称或公司介绍，不详细介绍技术内容，仅简洁明了阐述研发大致方向和解决问题后的效果。"),
        ("2. 关键技术",
         "提炼3-4项关键技术，每项用①②③④表述。格式：'①技术名称：技术原理和核心功能'。要求：技术名称避免使用'XX机构''XX装置'等通用模板化表述，应体现具体的技术特点（如'电动伺服夹持技术''精密角度控制技术'）；重点阐述技术原理和工作机制；避免详细描述零部件结构和组成；重点说明技术解决的核心问题和实现的功能；不要描述效率提升、工作效率等效果（这些留给创新点部分）；语言简洁专业；每项80-120字。"),
        ("3. 创新点",
         "提炼3-4个技术创新点，每项用①②③④表述。格式：'①具体技术名称：突破性改进和应用价值'。要求：从不同角度挖掘技术创新，避免与关键技术简单重复；技术名称应体现具体的技术特点或应用方向；重点阐述相对现有技术的突破和解决的实际问题；其中仅需1条创新点体现该技术对'{project_name}'项目主体产品本身的直接支撑效果（如对产品性能、质量、可靠性的提升），避免写成'适用于XXX研发'或'有助于XXX开发'等对研发过程的支撑；其他创新点重点突出技术突破本身；语言自然流畅，体现技术的创新价值；每项70-100字。避免使用'创新要点''技术创新'等通用词汇开头。"),
        ("4. 达到的主要技术或经济指标",
         "从关键技术内容中拟写出具体量化指标，至少4项，最多8项，确保指标直接关联关键技术。指标需包括技术指标（如'节能效率'、'使用寿命'），指标名称简洁（不超过5个字）。根据关键技术和创新点描述的产品类别（如照明设备、医疗设备），动态选择适配的指标类型（如照明设备节能效率通常提升15%-30%）。每项以'（1）'、'（2）'等序号开头，格式为'（序号）指标名称：具体值或范围'，如'（1）灯光亮度：98流明'。具体值或范围需量化明确（如百分比、时间、距离）。基于行业通用标准推导（如照明设备节能效率通常提升15%-30%）。每项单独成段，无需说明文字。")
    ]

    report = {}
    total_steps = len(outline) + 4
    current_step = 0

    for section, prompt in outline:
        current_step += 1
        progress = int((current_step / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': f"正在生成 {section}"})
        time.sleep(0.1)

        if prompt:
            # 动态插入 project_name 到提示词
            formatted_prompt = prompt.format(project_name=project_name) if '{project_name}' in prompt else prompt
            try:
                # 设置超时时间，避免长时间等待
                response = client.chat().create_completion(
                    model="moonshot-v1-32k",
                    messages=[
                       {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长中文技术文档撰写。请生成专业、流畅、自然的中文内容，注重语义连贯性和语体一致性，避免生硬或模板化表达。基于提供的技术文件内容，生成贴合项目的技术描述，确保语句通顺、逻辑清晰，确保指定序号格式（①、②、③或（1）、（2）、（3））单独成段，每段间空一行，不提及指示内容。"},
                        {"role": "user", "content": f"{formatted_prompt}\n文件内容如下：\n{file_contents}"}
                    ],
                    temperature=0.5
                )
                cleaned_content = response['choices'][0]['message']['content'].strip()
            except ValueError as e:
                # API自定义错误，直接抛出用户友好的错误信息
                error_msg = log_and_emit_error('unknown', f"生成{section}", e, emit_socket=False)
                raise ValueError(error_msg)
            except Exception as e:
                # 其他未知错误
                error_msg = log_and_emit_error('unknown', f"生成{section}", e, emit_socket=False)
                raise ValueError(error_msg)
            cleaned_content = clean_content(cleaned_content, section)
            report[section] = cleaned_content
        else:
            report[section] = ""
        time.sleep(1)

    # ... 后续代码保持不变 ...
    # （包括"三、现有开发条件和工作基础"、"四、计划进度"等部分的生成逻辑）

    current_step += 1
    progress = int((current_step / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "生成现有开发条件和工作基础"})
    report["三、现有开发条件和工作基础"] = (
        "公司自项目启动以来，始终专注于关键技术的研发，积累了扎实的开发实力。在技术与人事管理方面，公司建立了一套完善制度，"
        "并通过设立研发费用辅助账，为项目顺利推进提供了有力保障。同时，实施的研发激励政策有效调动了团队积极性，推动技术攻关不断深入。"
        "公司配备的先进研发设备与检测设备，完全满足项目的技术需求，为研发工作奠定了坚实基础。\n\n"
        "在前期工作方面，本项目已完成充分调研，公司通过多次会议深入讨论了项目的可行性与必要性，细化了实施细节，"
        "明确了开展方式与计划安排。这些成果为后续研发提供了可靠依据，确保项目稳步推进。"
    )

    # *** 重写简洁可靠的时间分配算法，彻底避免重叠和同月问题 ***
    stage_dates = []
    
    # 定义各阶段的推荐比例：[3, 5, 3, 1] = 12个月基准 - 加强研发设计阶段
    base_recommended_months = [3, 5, 3, 1]
    
    # 根据项目总时长调整各阶段分配
    if total_months >= 18:  # 长项目（1.5年以上）
        scale_factor = total_months / 12
        recommended_months = [int(month * scale_factor) for month in base_recommended_months]
        # 调整差值
        months_diff = total_months - sum(recommended_months)
        for i in range(abs(months_diff)):
            recommended_months[i % len(recommended_months)] += 1 if months_diff > 0 else -1
    elif total_months >= 6:  # 中等项目（6-18个月）
        scale_factor = total_months / 12
        recommended_months = [max(1, int(month * scale_factor)) for month in base_recommended_months]
        # 调整总和
        current_sum = sum(recommended_months)
        diff = total_months - current_sum
        for i in range(abs(diff)):
            if diff > 0:
                recommended_months[i % len(recommended_months)] += 1
            elif recommended_months[i % len(recommended_months)] > 1:
                recommended_months[i % len(recommended_months)] -= 1
    else:  # 短项目（小于6个月）
        # 确保每个阶段至少1个月，优先给研发设计阶段更多时间
        if total_months >= 4:
            recommended_months = [1, 2, 1, 1]  # 给研发设计2个月
        elif total_months == 3:
            recommended_months = [1, 2, 1]     # 给研发设计2个月
        elif total_months == 2:
            recommended_months = [1, 1]        # 各1个月
        else:  # 1个月
            recommended_months = [1]           # 只有1个阶段
    
    # 新的时间分配逻辑：平均分配总时间，避免同月问题
    current_year = start.year
    current_month = start.month
    months_allocated = 0  # 已分配的月数
    
    for i in range(min(stages, len(recommended_months))):
        stage_months = recommended_months[i]
        
        # 计算阶段开始
        stage_start_year = current_year
        stage_start_month = current_month
        
        # 计算阶段结束：确保不会出现同月开始结束
        if stage_months == 1:
            # 单月阶段：如果不是最后一个阶段，延长到下个月
            if i < len(recommended_months) - 1:
                # 不是最后阶段，延长到下个月
                stage_end_month = current_month + 1
                stage_end_year = current_year
                if stage_end_month > 12:
                    stage_end_month = 1
                    stage_end_year += 1
                months_allocated += 2  # 实际占用2个月
            else:
                # 最后阶段，使用项目结束时间
                stage_end_year = end.year
                stage_end_month = end.month
                months_allocated += 1
        else:
            # 多月阶段：正常计算
            end_month_total = current_month + stage_months - 1
            stage_end_year = current_year + (end_month_total - 1) // 12
            stage_end_month = ((end_month_total - 1) % 12) + 1
            months_allocated += stage_months
        
        # 确保不超过项目结束日期
        if datetime(stage_end_year, stage_end_month, 1) > end:
            stage_end_year = end.year
            stage_end_month = end.month
        
        # 格式化日期字符串 - 特殊处理同月情况
        start_str = f"{stage_start_year}年{stage_start_month:02d}月"
        end_str = f"{stage_end_year}年{stage_end_month:02d}月"
        
        # 如果开始月和结束月相同，只显示一个月份
        if start_str == end_str:
            stage_dates.append((start_str, None))  # None表示只显示开始月份
        else:
            stage_dates.append((start_str, end_str))
        
        # 更新下一阶段的开始时间
        current_month = stage_end_month + 1
        current_year = stage_end_year
        if current_month > 12:
            current_month = 1
            current_year += 1
        
        # 防止超出项目结束日期
        if datetime(current_year, current_month, 1) > end:
            break


    # *** 使用通用的阶段描述替换具体步骤 ***
    generic_phase_descriptions = [
        "项目启动和规划阶段，包括项目团队组建、需求分析、市场调研和项目计划制定。",
        "研发设计阶段，着重进行核心技术攻关、原型设计与初步开发。",
        "测试与优化阶段，包括内部测试、性能优化和用户反馈收集。",
        "完善与准备阶段，完成最终产品完善、文档编写和市场推广准备。"
    ]

    # *** 修改 plan_content 的格式 ***
    # 确保描述列表长度至少和 stage_dates 一样长
    while len(generic_phase_descriptions) < len(stage_dates):
        generic_phase_descriptions.append(f"阶段 {len(generic_phase_descriptions) + 1} 任务") # 补充默认描述

    plan_content_parts = []
    # 使用 stage_dates 的实际长度来迭代
    for i in range(len(stage_dates)):
        start_str, end_str = stage_dates[i]
        # 使用索引 i 获取描述，防止列表越界
        desc = generic_phase_descriptions[i] if i < len(generic_phase_descriptions) else f"阶段 {i+1} 任务"

        # 处理特殊情况：如果end_str为None，表示只显示单个月份
        if end_str is None:
            plan_content_parts.append(f"{start_str}，{desc}")
        else:
            # 检查时间段是否有效 (开始时间不晚于结束时间)
            try:
                # 尝试解析，如果失败则跳过此时间段
                start_dt_check_str = start_str.replace('年', '-').replace('月', '-01')
                end_dt_check_str = end_str.replace('年', '-').replace('月', '-01') # 取月份第一天比较
                start_dt_check = datetime.strptime(start_dt_check_str, "%Y-%m-%d")
                end_dt_check = datetime.strptime(end_dt_check_str, "%Y-%m-%d")

                # 获取结束月份的最后一天
                next_month_year = end_dt_check.year + (end_dt_check.month // 12)
                next_month_month = (end_dt_check.month % 12) + 1
                try:
                    end_dt_check_last_day = datetime(next_month_year, next_month_month, 1) - timedelta(days=1)
                except ValueError: # 处理可能的无效日期
                    # 如果下个月第一天无效，尝试使用当前月份的最后一天
                    current_month_last_day_approx = datetime(end_dt_check.year, end_dt_check.month, 28) # 从28号开始尝试
                    while True:
                        try:
                            next_day = current_month_last_day_approx + timedelta(days=1)
                            if next_day.month != current_month_last_day_approx.month:
                                end_dt_check_last_day = current_month_last_day_approx
                                break
                            current_month_last_day_approx += timedelta(days=1)
                        except OverflowError: # 防止日期超出范围
                            end_dt_check_last_day = end_dt_check # 出错则使用当前日期
                            break

                if start_dt_check <= end_dt_check_last_day: # 确保开始月份不晚于结束月份
                    plan_content_parts.append(f"{start_str}至{end_str}，{desc}")
                else:
                    logger.warning(f"计划进度中发现无效时间段（开始晚于结束），将跳过：{start_str} 至 {end_str}")
            except ValueError as ve:
                logger.warning(f"无法解析计划进度中的日期格式，将跳过：'{start_str}' 或 '{end_str}'. 错误: {ve}")

    plan_content = "\n\n".join(plan_content_parts) # 使用有效的段落组合

    current_step += 1
    progress = int((current_step / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "生成计划进度"})
    report["四、计划进度"] = plan_content

    current_step += 1
    progress = int((current_step / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "设置经费概算和研究人员名单"})
    report["五、经费概算"] = {} # 初始化为空字典，稍后填充
    report["七、主要研究人员名单"] = "" # <--- 修改在这里

    return report

def trim_content_for_rdrps(key_tech_content, innovation_content, project_name, target_total_min=400, target_total_max=500):
    """精简 RD_PS 表中关键技术和创新点内容，确保总字数在 400-500 字之间，并为每项添加正确序号"""
    logger.info("开始精简 RD_PS 表的关键技术和创新点内容")

    # 预处理内容，移除现有序号并分割段落
    def preprocess_content(content):
        # 首先按行分割内容
        lines = content.split("\n")

        # 过滤掉空行
        lines = [line.strip() for line in lines if line.strip()]

        # 提取所有有内容的项
        valid_items = []
        for line in lines:
            # 移除现有的序号（①②③④⑤⑥⑦⑧⑨）
            clean_line = re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line).strip()
            if clean_line:  # 只保留有内容的项
                valid_items.append(clean_line)

        # 如果没有有效项，添加默认内容
        if not valid_items:
            valid_items = ["未提供相关内容"]

        return valid_items

    # 分割原始内容并过滤空行
    key_tech_lines = preprocess_content(key_tech_content)
    innovation_lines = preprocess_content(innovation_content)

    # 确保至少有一项内容
    if not key_tech_lines:
        key_tech_lines = ["未提供关键技术内容"]
        logger.warning("原始关键技术内容为空，使用默认内容")

    if not innovation_lines:
        innovation_lines = ["未提供创新点内容"]
        logger.warning("原始创新点内容为空，使用默认内容")

    # 计算当前字数（去除序号）
    key_tech_length = sum(len(re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line)) for line in key_tech_lines)
    innovation_length = sum(len(re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line)) for line in innovation_lines)
    total_length = key_tech_length + innovation_length
    logger.info(f"原始字数 - 关键技术: {key_tech_length}, 创新点: {innovation_length}, 总计: {total_length}")

    # 目标字数分配
    target_key_tech_min, target_key_tech_max = 200, 250
    target_innovation_min, target_innovation_max = 200, 250
    target_item_min, target_item_max = 60, 100

    # 精简关键技术
    trimmed_key_tech = []
    current_key_tech_length = 0
    for i, line in enumerate(key_tech_lines[:4]):  # 最多 4 项
        clean_line = re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line).strip()
        line_length = len(clean_line)

        if line_length > target_item_max:
            # 按句子边界精简
            sentences = re.split(r'(?<=[。；])', clean_line)
            trimmed_line = ""
            current_length = 0
            for sentence in sentences:
                if current_length + len(sentence) <= target_item_max and sentence.strip():
                    trimmed_line += sentence
                    current_length += len(sentence)
                else:
                    break
            if not trimmed_line.endswith('。'):
                trimmed_line = trimmed_line.rsplit('，', 1)[0] + '。' if '，' in trimmed_line else trimmed_line
        else:
            trimmed_line = clean_line

        if trimmed_line and current_key_tech_length + len(trimmed_line) <= target_key_tech_max:
            trimmed_key_tech.append(trimmed_line)
            current_key_tech_length += len(trimmed_line)

    # 不再根据项目名称自动补充内容，只使用原始内容
    # 如果关键技术不足3项，尝试从原始内容中提取更多项
    logger.info(f"关键技术项数: {len(trimmed_key_tech)}, 字数: {current_key_tech_length}")
    # 不再自动补充内容，保持原样

    # 精简创新点
    trimmed_innovation = []
    current_innovation_length = 0
    for i, line in enumerate(innovation_lines[:4]):  # 最多 4 项
        clean_line = re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line).strip()
        line_length = len(clean_line)

        if line_length > target_item_max:
            # 按句子边界精简
            sentences = re.split(r'(?<=[。；])', clean_line)
            trimmed_line = ""
            current_length = 0
            for sentence in sentences:
                if current_length + len(sentence) <= target_item_max and sentence.strip():
                    trimmed_line += sentence
                    current_length += len(sentence)
                else:
                    break
            if not trimmed_line.endswith('。'):
                trimmed_line = trimmed_line.rsplit('，', 1)[0] + '。' if '，' in trimmed_line else trimmed_line
        else:
            trimmed_line = clean_line

        if trimmed_line and current_innovation_length + len(trimmed_line) <= target_innovation_max:
            trimmed_innovation.append(trimmed_line)
            current_innovation_length += len(trimmed_line)

    # 不再根据项目名称自动补充内容，只使用原始内容
    # 如果创新点不足3项，保持原样
    logger.info(f"创新点项数: {len(trimmed_innovation)}, 字数: {current_innovation_length}")
    # 不再自动补充内容，保持原样

    # 调整总字数到 400-500 字
    total_length = current_key_tech_length + current_innovation_length
    logger.info(f"初次调整后总字数: {total_length}")

    # 不再自动补充内容以达到最小字数要求
    # 如果总字数不足，保持原样
    logger.info(f"总字数: {total_length}, 目标最小字数: {target_total_min}")
    # 不再自动补充内容，保持原样

    # 不再延长现有项内容
    # 如果总字数仍不足，保持原样
    if total_length < target_total_min:
        shortfall = target_total_min - total_length
        logger.info(f"总字数仍不足 {shortfall} 字，但不再自动延长内容")
        # 不再自动延长内容，保持原样

    # 如果总字数超限，优先削减较长的部分
    while total_length > target_total_max and (len(trimmed_key_tech) > 2 or len(trimmed_innovation) > 2):
        if key_tech_length > innovation_length and len(trimmed_key_tech) > 2:
            trimmed_key_tech = trimmed_key_tech[:2]
            current_key_tech_length = sum(len(line) for line in trimmed_key_tech)
            logger.info(f"削减关键技术到 2 项以控制总字数，当前关键技术字数: {current_key_tech_length}")
        elif len(trimmed_innovation) > 2:
            trimmed_innovation = trimmed_innovation[:2]
            current_innovation_length = sum(len(line) for line in trimmed_innovation)
            logger.info(f"削减创新点到 2 项以控制总字数，当前创新点字数: {current_innovation_length}")
        total_length = current_key_tech_length + current_innovation_length

    # 确保关键技术和创新点都至少有一项内容
    if not trimmed_key_tech:
        trimmed_key_tech = ["未提供关键技术内容"]
        current_key_tech_length = len(trimmed_key_tech[0])
        logger.warning("精简后关键技术为空，使用默认内容")

    if not trimmed_innovation:
        trimmed_innovation = ["未提供创新点内容"]
        current_innovation_length = len(trimmed_innovation[0])
        logger.warning("精简后创新点为空，使用默认内容")

    # --- 修改开始 ---
    def format_with_numbers(items, max_items=3):
        """为每一项添加正确的序号（①、②、③等），序号后无空格，确保没有空序号"""
        result = []

        # 过滤掉空项，并确保每项都有实际内容
        valid_items = []
        for item in items[:max_items]:
            # 移除可能存在的序号和空格
            clean_item = re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', item).strip()
            if clean_item:  # 只保留有内容的项
                valid_items.append(clean_item)

        # 如果没有有效项，添加一个默认项
        if not valid_items:
            valid_items = ["未提供相关内容"]

        # 为每个有效项添加序号，确保序号连续
        for i, item in enumerate(valid_items):
            number = chr(0x2460 + i)  # ①, ②, ③, ...
            result.append(f"{number}{item}")

        return result

    # 确保关键技术和创新点都有内容
    if not trimmed_key_tech:
        logger.warning("关键技术列表为空，将使用默认内容")
        trimmed_key_tech = ["未提供关键技术内容"]

    if not trimmed_innovation:
        logger.warning("创新点列表为空，将使用默认内容")
        trimmed_innovation = ["未提供创新点内容"]

    formatted_trimmed_key_tech = format_with_numbers(trimmed_key_tech)
    formatted_trimmed_innovation = format_with_numbers(trimmed_innovation)

    # 确保关键技术有内容，并且序号连续
    if not formatted_trimmed_key_tech:
        key_tech_result = "①未提供关键技术内容"  # 不需要添加空格，因为已经设置了首行缩进
    else:
        # 检查序号是否连续
        valid_key_tech = []
        for i, item in enumerate(formatted_trimmed_key_tech):
            # 提取序号和内容
            match = re.match(r'^([①②③④⑤⑥⑦⑧⑨])(.*)', item)
            if match and match.group(2).strip():
                # 使用正确的序号
                number = chr(0x2460 + i)  # ①, ②, ③, ...
                valid_key_tech.append(f"{number}{match.group(2).strip()}")  # 不需要添加空格

        # 如果没有有效项，添加默认内容
        if not valid_key_tech:
            valid_key_tech = ["①未提供关键技术内容"]  # 不需要添加空格

        key_tech_result = "\n".join(valid_key_tech)
    # --- 修改结束 ---

    key_tech_length = sum(len(re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line)) for line in formatted_trimmed_key_tech)
    logger.info(f"精简后关键技术: {key_tech_length} 字\n内容:\n{key_tech_result}")

    # --- 修改开始 ---
    # 确保创新点有内容，并且序号连续
    if not formatted_trimmed_innovation:
        innovation_result = "①未提供创新点内容"  # 不需要添加空格，因为已经设置了首行缩进
    else:
        # 检查序号是否连续
        valid_innovation = []
        for i, item in enumerate(formatted_trimmed_innovation):
            # 提取序号和内容
            match = re.match(r'^([①②③④⑤⑥⑦⑧⑨])(.*)', item)
            if match and match.group(2).strip():
                # 使用正确的序号
                number = chr(0x2460 + i)  # ①, ②, ③, ...
                valid_innovation.append(f"{number}{match.group(2).strip()}")  # 不需要添加空格

        # 如果没有有效项，添加默认内容
        if not valid_innovation:
            valid_innovation = ["①未提供创新点内容"]  # 不需要添加空格

        innovation_result = "\n".join(valid_innovation)
    # --- 修改结束 ---

    innovation_length = sum(len(re.sub(r'^[①②③④⑤⑥⑦⑧⑨]\s*', '', line)) for line in formatted_trimmed_innovation)
    logger.info(f"精简后创新点: {innovation_length} 字\n内容:\n{innovation_result}")

    # 最终微调：确保总字数在 400-500 字
    total_length = key_tech_length + innovation_length
    if total_length < target_total_min:
        shortfall = target_total_min - total_length
        logger.info(f"最终微调：总字数仍不足 {shortfall} 字，但不再自动补充内容")
        # 不再自动补充内容，保持原样

    logger.info(f"最终字数 - 关键技术: {key_tech_length}, 创新点: {innovation_length}, 总计: {total_length}")
    if not (target_total_min <= total_length <= target_total_max):
        logger.warning(f"总字数 {total_length} 超出目标范围 [{target_total_min}, {target_total_max}]")
    else:
        logger.info(f"总字数 {total_length} 符合目标范围 [{target_total_min}, {target_total_max}]")

    return key_tech_result, innovation_result

def generate_rdrps_table(report_data, project_name):
    """生成 RD_PS 表，精简核心技术及创新点部分的关鍵技术和创新点内容"""
    rdrps_table = {
        "项目编号": report_data.get("项目编号", ""),
        "核心技术及创新点": {
            "关键技术": "",
            "创新点": ""
        }
    }

    # 获取原始关键技术和创新点内容
    key_tech_content = report_data.get("2. 关键技术", "")
    innovation_content = report_data.get("3. 创新点", "")

    # 精简内容仅用于 RD_PS 表
    if key_tech_content and innovation_content:
        trimmed_key_tech, trimmed_innovation = trim_content_for_rdrps(
            key_tech_content, innovation_content, project_name
        )
        rdrps_table["核心技术及创新点"]["关键技术"] = trimmed_key_tech
        rdrps_table["核心技术及创新点"]["创新点"] = trimmed_innovation
    else:
        logger.warning("关键技术或创新点内容缺失，RD_PS表将使用原始内容")
        rdrps_table["核心技术及创新点"]["关键技术"] = key_tech_content
        rdrps_table["核心技术及创新点"]["创新点"] = innovation_content

    return rdrps_table

@app.route('/generate', methods=['POST'])
def generate():
    form_data = request.form.to_dict()
    files_data = request.files
    logger.debug(f"Received form data: {form_data}")
    logger.debug(f"Received files: {list(files_data.keys())}")

    # 获取表单数据
    company_name = form_data.get('company_name', '').strip()
    start_date = form_data.get('start_date', '').strip()
    end_date = form_data.get('end_date', '').strip()
    project_name = form_data.get('project_name', '').strip()
    rd_number = form_data.get('rd_number', '').strip()
    # 获取自定义项目编号和验收报告编号（如果有）
    project_number = form_data.get('project_number', '').strip()
    acceptance_number = form_data.get('acceptance_number', '').strip()
    # 如果没有提供自定义编号，则使用默认值
    if not project_number:
        project_number = rd_number
    if not acceptance_number:
        acceptance_number = f"YS-{rd_number}"
    output_path = form_data.get('output_path', '').strip()
    staff_names = [
        name.strip() for name in form_data.get('staff_names', '').split(',')
        if name.strip()
    ] or ["姓名在此输入"]

    # 获取上传的 ZIP 文件
    tech_files_zip = files_data.get('tech_files_zip')
    total_steps = 7
    step_counter = [0]

    # 验证必填字段
    if not all([company_name, start_date, end_date, project_name, rd_number]):
        error_msg = "请填写所有必填字段（公司名称、开始日期、结束日期、项目名称、项目编号）！"
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400
    if not tech_files_zip:
        error_msg = "请上传技术文件 Zip 压缩包！"
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400

    # 进度更新：开始处理技术文件
    step_counter[0] += 1
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "开始处理技术文件"})

    # 解析 ZIP 文件中的 PDF 和 Word 文档
    file_contents = []
    try:
        tech_files_zip.seek(0)
        zip_content = io.BytesIO(tech_files_zip.read())
        with zipfile.ZipFile(zip_content, 'r') as zip_ref:
            for file_name in zip_ref.namelist():
                with zip_ref.open(file_name) as file:
                    if file_name.lower().endswith('.pdf'):
                        try:
                            file.mode = 'rb'
                            pdf_reader = pypdf.PdfReader(file)
                            text = ""
                            for page in pdf_reader.pages:
                                extracted_text = page.extract_text() or ""
                                text += extracted_text
                            file_contents.append(text)
                            logger.info(f"成功提取 PDF 文件 {file_name} 的内容")
                        except Exception as e:
                            logger.error(f"PDF 文件 {file_name} 解析失败: {e}")
                    elif file_name.lower().endswith(('.doc', '.docx')):
                        try:
                            doc = Document(file)
                            text = "\n".join([para.text for para in doc.paragraphs])
                            file_contents.append(text)
                            logger.info(f"成功提取 Word 文件 {file_name} 的内容")
                        except Exception as e:
                            logger.error(f"Word 文件 {file_name} 解析失败: {e}")
        file_contents = "\n\n".join(file_contents)
        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "技术文件处理完成"})
    except zipfile.BadZipFile as e:
        error_msg = f"处理 ZIP 文件失败: 请确保上传的是有效的 ZIP 文件"
        logger.error(f"处理 ZIP 文件失败: {e}")
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400
    except Exception as e:
        error_msg = f"处理 ZIP 文件失败: {e}"
        logger.error(error_msg)
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400

    # 进度更新：开始生成报告内容
    step_counter[0] += 1
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "开始生成报告内容"})

    # 调用 Moonshot AI 生成报告
    try:
        report = generate_report_with_kimi(file_contents, company_name, start_date, end_date, project_name)
        report["项目编号"] = rd_number
        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "报告内容生成完成"})
    except Exception as e:
        error_msg = f"生成报告失败: {e}"
        logger.error(error_msg)
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400

    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    # 生成 RD_PS 表的文件名（保持原逻辑）
    output_filename = f"{company_name}-{project_name}-{rd_number}-{datetime.now().strftime('%Y%m%d')}"

    # 生成项目报告的文件名：RD序号-项目名称-企业名称-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    cleaned_project_name = clean_filename(project_name)
    cleaned_company_name = clean_filename(company_name)
    cleaned_rd_number = clean_filename(rd_number)
    report_filename = f"{cleaned_rd_number}-{cleaned_project_name}-{cleaned_company_name}-{timestamp}"
    report_file_path = os.path.join(output_path, f"{report_filename}.docx") if output_path else f"{report_filename}.docx"

    # 进度更新：保存项目计划书
    step_counter[0] += 1
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': "正在保存项目计划书"})

    # 保存项目计划书到 Word 文档
    try:
        save_report_to_docx(report, report_file_path, company_name, start_date, end_date, project_name, rd_number, staff_names)
        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "项目计划书保存完成"})
    except Exception as e:
        error_msg = f"保存项目计划书失败: {e}"
        logger.error(error_msg)
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400

    # 进度更新：生成 RD_PS 表
    step_counter[0] += 1
    progress = 100
    socketio.emit('progress', {'progress': progress, 'message': "正在生成 RD_PS 表"})

    # 生成 RD_PS 表，精简关键技术和创新点
    try:
        rdrps_table = generate_rdrps_table(report, project_name)
        # 保存 RD_PS 表为 Excel 文件（使用原文件名逻辑）
        output_rdrps_path = os.path.join(output_path, f"RD_PS_{output_filename}.xlsx") if output_path else f"RD_PS_{output_filename}.xlsx"
        wb = Workbook()
        ws = wb.active
        ws.append(["项目编号", rdrps_table["项目编号"]])
        ws.append(["核心技术", rdrps_table["核心技术及创新点"]["关键技术"]])
        ws.append(["创新点", rdrps_table["核心技术及创新点"]["创新点"]])
        for row in ws.iter_rows():
            for cell in row:
                cell.alignment = Alignment(wrap_text=True, vertical='center')
        wb.save(output_rdrps_path)
        logger.info(f"RD_PS 表生成并保存至: {output_rdrps_path}")
    except Exception as e:
        error_msg = f"生成或保存 RD_PS 表失败: {e}"
        logger.error(error_msg)
        socketio.emit('error', {'error': error_msg})
        return error_msg, 400

    return (
        f"项目计划书: {report_file_path}\n"
        f"RD_PS 表: {output_rdrps_path}"
    )

def guess_gender(name):
    male_indicators = [
        "强", "伟", "军", "峰", "杰", "勇", "刚", "明", "阳", "辉", "健", "宇", "博", "涛", "磊", "鹏", "龙", "超", "平", "海", "波", "航", "洋", "林", "斌", "宏", "城", "伟", "杰", "斌", "强", "军", "勇", "辉", "明", "亮", "波", "涛", "峰", "宇", "博", "鹏", "龙", "超", "平", "海", "航", "洋", "林", "斌", "宏", "城"
    ]
    female_indicators = [
        "丽", "芳", "艳", "娟", "梅", "霞", "红", "娜", "婷", "静", "雪", "兰", "秀", "英", "敏", "慧", "洁", "静", "雅", "诗", "梦", "雨", "思", "瑶", "婷", "丽", "芳", "艳", "娟", "梅", "霞", "红", "娜", "静", "雪", "兰", "秀", "英", "敏", "慧", "洁", "雅", "诗", "梦", "雨", "思", "瑶"
    ]

    for indicator in male_indicators:
        if indicator in name:
            return "男"
    for indicator in female_indicators:
        if indicator in name:
            return "女"
    return random.choice(["男", "女"])

# ==============================================================================
# 函数：save_report_to_docx
# 功能：将生成的报告内容保存为 Word (.docx) 文件，包含格式化和表格生成
# ==============================================================================
def save_report_to_docx(report, output_file_path, company_name, start_date, end_date, project_name, rd_number, staff_names, equipment_list=None, project_number=None):
    """
    将报告数据保存到 Word 文档。

    Args:
        report (dict): 包含报告各部分内容的字典。
        output_file_path (str): 输出 Word 文件的完整路径。
        company_name (str): 公司名称。
        start_date (str): 项目开始日期 (YYYY-MM-DD)。
        end_date (str): 项目结束日期 (YYYY-MM-DD)。
        project_name (str): 项目名称。
        rd_number (str): 项目编号 (RD序号)。
        staff_names (list): 研发人员姓名列表。
        equipment_list (list, optional): 仪器设备清单列表，每个元素是一个包含 'name', 'value', 'description' 的字典。默认为 None。
    """
    if equipment_list is None:
        equipment_list = [] # 确保 equipment_list 是一个列表

    # 检查设备列表是否有足够的项目（至少3项）
    has_equipment_section = equipment_list and len(equipment_list) >= 3

    # 确保输出目录存在
    output_dir = os.path.dirname(output_file_path)
    if output_dir: # 只有在路径包含目录时才创建
        try:
            os.makedirs(output_dir, exist_ok=True)
        except OSError as e:
            logger.error(f"无法创建目录 {output_dir}: {e}. 文件将尝试保存在当前目录。")
            # 如果无法创建目录，尝试将文件名作为相对路径保存
            output_file_path = os.path.basename(output_file_path)


    doc = Document()
    # --- 设置页面边距 (A4) ---
    sections = doc.sections
    for section in sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
    logger.debug("Word 文档页面边距设置完成")

    # --- 添加封面 ---
    # 添加空行调整间距
    doc.add_paragraph()
    doc.add_paragraph()
    # 主标题
    p_title = doc.add_paragraph("研究开发项目计划书")
    p_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    # 设置标题字体
    for run in p_title.runs:
        run.font.name = '黑体'
        # 下面这行是关键，确保中文字体应用正确
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(25) # 设置字号
    logger.debug("封面主标题添加完成")
    # 添加空行
    doc.add_paragraph()
    doc.add_paragraph()

    # 项目名称
    p_proj_name = doc.add_paragraph()
    p_proj_name.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY # 两端对齐
    run = p_proj_name.add_run("项目名称：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run = p_proj_name.add_run(project_name)
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True # 添加下划线
    doc.add_paragraph() # 段后空行

    # 项目编号
    p_rd_num = doc.add_paragraph()
    p_rd_num.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    run = p_rd_num.add_run("项目编号：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    # 使用自定义项目编号（如果提供）或默认的RD序号
    display_project_number = project_number if project_number else rd_number
    run = p_rd_num.add_run(display_project_number)
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    doc.add_paragraph()

    # 企业名称
    p_comp_name = doc.add_paragraph()
    p_comp_name.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    run = p_comp_name.add_run("企业名称：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run = p_comp_name.add_run(company_name)
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    doc.add_paragraph()

    # 项目负责人
    director_name = staff_names[0] if staff_names else "姓名在此输入" # 取第一个人为负责人
    p_director = doc.add_paragraph()
    p_director.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    run = p_director.add_run("项目负责人：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run = p_director.add_run(director_name)
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    run = p_director.add_run("  电话：") # 添加电话标签
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run = p_director.add_run("电话号码在此输入") # 电话占位符
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    doc.add_paragraph()

    # 项目起止时间
    p_dates = doc.add_paragraph()
    p_dates.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    run = p_dates.add_run("项目起止时间：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    # 格式化日期
    try:
        start_date_str = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m-%d")
        end_date_str = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m-%d")
    except ValueError:
        logger.warning(f"日期格式无法解析: start='{start_date}', end='{end_date}'. 将使用原始字符串。")
        start_date_str = start_date
        end_date_str = end_date
    run = p_dates.add_run(f"{start_date_str} 至 {end_date_str}")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    doc.add_paragraph()

    # 报告日期 (使用项目开始日期作为报告日期)
    p_report_date = doc.add_paragraph()
    p_report_date.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    run = p_report_date.add_run("报告日期：")
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    try:
        report_date_str = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m-%d")
    except ValueError:
        report_date_str = start_date # 使用原始字符串如果格式错误
    run = p_report_date.add_run(report_date_str)
    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(15)
    run.font.underline = True
    doc.add_paragraph()
    logger.debug("封面信息添加完成")

    # --- 换页 ---
    doc.add_page_break()
    logger.debug("分页符添加完成")

    # --- 定义标题级别映射 ---
    section_map = {
        "一、立项依据": 2,
        "二、开发内容和目标": 2,
        "三、现有开发条件和工作基础": 2,
        "四、计划进度": 2,
        "五、经费概算": 2,
        "六、采用的仪器设备清单": 2, # 新增
        "七、主要研究人员名单": 2, # 修改
        # 子项保持三级标题
        "1. 国内外现状、水平和发展趋势": 3,
        "2. 项目开发的目的和意义": 3,
        "3. 本项目达到的技术水平及市场前景": 3,
        "1. 研发的主要内容": 3,
        "2. 关键技术": 3,
        "3. 创新点": 3,
        "4. 达到的主要技术或经济指标": 3,
    }

    # --- 定义报告内容的标准顺序 ---
    report_order = [
        "一、立项依据",
        "1. 国内外现状、水平和发展趋势",
        "2. 项目开发的目的和意义",
        "3. 本项目达到的技术水平及市场前景",
        "二、开发内容和目标",
        "1. 研发的主要内容",
        "2. 关键技术",
        "3. 创新点",
        "4. 达到的主要技术或经济指标",
        "三、现有开发条件和工作基础",
        "四、计划进度",
        "五、经费概算",
        # 注意：六、仪器设备清单 和 七、主要研究人员名单 会在循环内部特殊处理
        "七、主要研究人员名单" # 确保这个键存在于 report 字典中 (在 generate_report_with_kimi 中设置)
    ]

    # 创建有序的报告项列表，以便按顺序写入文档
    ordered_report_items = []
    report_keys_processed = set() # 记录已处理的key，防止重复添加

    for key in report_order:
        if key in report and key not in report_keys_processed:
            # 特殊处理经费概算和人员名单，只添加键名用于定位，内容在后面处理
            if key == "五、经费概算" or key == "七、主要研究人员名单":
                 ordered_report_items.append((key, None)) # 用 None 占位
            else:
                 ordered_report_items.append((key, report[key]))
            report_keys_processed.add(key)

    # 添加其他未在标准顺序中定义的项（理论上不应发生）
    # 排除不应该出现在文档中的字段
    excluded_fields = {"项目编号", "ip_numbers", "patent_info", "五、经费概算"}
    for section, content in report.items():
        if section not in report_keys_processed and section not in excluded_fields:
            logger.warning(f"发现未定义顺序的报告部分: '{section}'，将追加到末尾")
            ordered_report_items.append((section, content))

    # --- 遍历有序的报告项并写入文档 ---
    for section, content in ordered_report_items:
        level = section_map.get(section, 3) # 获取标题级别，默认为3

        # 添加标题
        if section != "五、经费概算" and section != "七、主要研究人员名单": # 经费和人员标题在下面单独处理
             p_heading = doc.add_heading(section, level=level)
             p_heading.paragraph_format.line_spacing = 1.5 # 设置行距
             # 设置标题字体为黑体，四号（14磅），黑色
             for run in p_heading.runs:
                 run.font.name = '黑体'
                 run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
                 run.font.size = Pt(14) # 四号字体为14磅
                 run.font.color.rgb = RGBColor(0, 0, 0) # 黑色
             logger.debug(f"添加标题: '{section}' (级别 {level})")

        # --- 处理经费概算表格 ---
        if section == "五、经费概算":
            p_budget_heading = doc.add_heading("五、经费概算", level=2) # 手动添加标题
            p_budget_heading.paragraph_format.line_spacing = 1.5
            # 设置标题字体为黑体，四号（14磅），黑色
            for run in p_budget_heading.runs:
                run.font.name = '黑体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
                run.font.size = Pt(14) # 四号字体为14磅
                run.font.color.rgb = RGBColor(0, 0, 0) # 黑色
            logger.debug("处理经费概算部分...")

            budget_content = report.get("五、经费概算", {}) # 获取预算数据
            if isinstance(budget_content, dict) and 'data' in budget_content:
                budget_data = budget_content.get('data', {})
                budget_start_date = budget_content.get('start_date', start_date) # 使用传入的日期或全局日期
                budget_end_date = budget_content.get('end_date', end_date)
                logger.info(f"经费概算数据年份: {list(budget_data.keys())}")

                # --- 调整预算数据 ---
                adjusted_budget_data, original_total = adjust_budget_data(budget_data)
                # 计算调整后数据的总计（直接使用各年度合计的合计）
                final_adjusted_total = sum(
                    float(year_data.get("合计", 0)) 
                    for year_data in adjusted_budget_data.values()
                )
                
                # 存储原始数据供其他文档使用
                report['五、经费概算']['original_data'] = budget_data
                report['五、经费概算']['original_total'] = original_total
                report['五、经费概算']['adjusted_data'] = adjusted_budget_data
                report['五、经费概算']['adjusted_total'] = final_adjusted_total

                # --- 添加概算表格前的文字 ---
                p = doc.add_paragraph()
                p.alignment = WD_ALIGN_PARAGRAPH.LEFT
                p.paragraph_format.line_spacing = 1.5
                
                # 添加"项目预计总经费"文字
                run1 = p.add_run("项目预计总经费")
                run1.font.name = '宋体'
                run1._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run1.font.size = Pt(13)
                
                # 添加带下划线的总经费数值
                run2 = p.add_run(f"{final_adjusted_total:.0f}")  # 不显示小数点
                run2.font.name = '宋体'
                run2._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run2.font.size = Pt(13)
                run2.underline = True  # 添加下划线
                
                # 添加"万元"文字
                run3 = p.add_run("万元")
                run3.font.name = '宋体'
                run3._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run3.font.size = Pt(13)

                p = doc.add_paragraph("项目经费支出预算表")
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                p.paragraph_format.line_spacing = 1.5
                for run in p.runs:
                    run.font.name = '黑体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体'); run.font.size = Pt(13)

                p = doc.add_paragraph("单位：万元")
                p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                p.paragraph_format.line_spacing = 1.5
                for run in p.runs:
                    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(13)

                # --- 计算年份跨度 ---
                try:
                    start_year = int(budget_start_date[:4])
                    end_year = int(budget_end_date[:4])
                    years = end_year - start_year + 1
                    if years <= 0: years = 1 # 至少一年
                    year_range = list(range(start_year, end_year + 1))
                    logger.debug(f"计算得到的经费年份范围: {year_range} (共 {years} 年)")
                except Exception as e:
                    logger.error(f"计算经费年份失败: {e}. 默认使用一年。")
                    years = 1
                    start_year = datetime.now().year
                    year_range = [start_year]

                # --- 创建经费表格 ---
                # 表格行数 = 固定标题行(3) + 数据行(9) + 合计行(1) + 总计行(1) = 14
                budget_table = doc.add_table(rows=14, cols=1 + years) # 1列科目 + N年数据
                budget_table.style = 'Table Grid'
                budget_table.alignment = WD_ALIGN_PARAGRAPH.CENTER
                budget_table.autofit = False # 关闭自动调整以手动设置列宽

                # 设置列宽
                budget_table.columns[0].width = Cm(10.0) # 科目列宽
                col_width_years = Cm(2.5) # 每年的列宽
                if years * 2.5 > 16: # 如果总宽度过大，适当缩小
                     col_width_years = Cm(16.0 / years) if years > 0 else Cm(2.5)

                for i in range(1, years + 1):
                    budget_table.columns[i].width = col_width_years
                logger.debug(f"经费表格列宽设置: 科目={budget_table.columns[0].width.cm:.2f}cm, 年份={col_width_years.cm:.2f}cm")

                # 设置默认行高和单元格对齐
                for row in budget_table.rows:
                    row.height = Cm(0.3) # 调整默认行高
                    for cell in row.cells:
                        cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                        for paragraph in cell.paragraphs:
                            paragraph.paragraph_format.keep_together = True # 防止分页
                            paragraph.paragraph_format.space_after = Pt(0)
                            paragraph.paragraph_format.space_before = Pt(0)

                # --- 填充表格标题行 ---
                # 第1行：大标题合并单元格
                title_cells = budget_table.rows[0].cells
                title_cells[0].text = "项目经费支出预算表"
                if years > 0: # 只有多于0年时才合并
                    title_cells[0].merge(title_cells[years]) # 从第0个合并到最后一个
                for paragraph in title_cells[0].paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                # 第2行：科目/预算数
                hdr_cells = budget_table.rows[1].cells
                hdr_cells[0].text = "科    目"
                for i in range(1, years + 1):
                    hdr_cells[i].text = "预算数"
                for cell in hdr_cells:
                    for paragraph in cell.paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                # 第3行：年份
                year_cells = budget_table.rows[2].cells
                year_cells[0].text = "" # 第一列留空
                for i, year in enumerate(year_range):
                    if i + 1 < len(year_cells): # 检查索引是否越界
                         year_cells[i + 1].text = str(year)
                    else:
                         logger.warning(f"年份数量 ({len(year_range)}) 与表格列数 ({len(year_cells)-1}) 不匹配")
                         break
                for cell in year_cells:
                    for paragraph in cell.paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                # --- 填充表格数据行 ---
                # 标准科目顺序
                standard_subjects_order = [
                    "一、直接从事研发活动的本企业在职人员人工费用",
                    "二、研发活动直接投入的费用",
                    "三、折旧费用与长期待摊费用",
                    "四、设计费用",
                    "五、装备调试费",
                    "六、专门用于研发活动的无形资产摊销费",
                    "七、勘探、开发技术的现场试验费",
                    "八、研发成果的论证、鉴定、评审、验收费",
                    "九、与研发活动直接相关的其他费用",
                    # "合计" 会在后面单独计算和填充
                ]

                yearly_totals = [0.0] * years # 初始化每年的合计金额

                for idx, subject in enumerate(standard_subjects_order):
                    row_index = idx + 3 # 数据从第4行开始 (索引为3)
                    row_cells = budget_table.rows[row_index].cells
                    # 填充科目名称
                    row_cells[0].text = subject
                    row_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT # 科目左对齐
                    for run in row_cells[0].paragraphs[0].runs:
                         run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                    # 填充每年的调整后预算金额
                    for j, year in enumerate(year_range):
                        year_str = str(year)
                        amount = 0.0
                        if year_str in adjusted_budget_data and subject in adjusted_budget_data[year_str]:
                            amount = adjusted_budget_data[year_str][subject]
                            yearly_totals[j] += float(amount) if pd.notna(amount) else 0.0

                        cell_index = j + 1
                        if cell_index < len(row_cells):
                             # 格式化金额为整数（无小数点），或显示"—"
                             row_cells[cell_index].text = f"{int(amount)}" if pd.notna(amount) and float(amount) != 0 else "—"
                             row_cells[cell_index].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER # 金额居中
                             for run in row_cells[cell_index].paragraphs[0].runs:
                                  run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)
                        else:
                             logger.warning(f"填充预算时索引越界: row={row_index}, col={cell_index}")
                             break # 跳出年份循环

                # --- 填充合计行 (第13行，索引12) ---
                sum_row_index = 12
                sum_row_cells = budget_table.rows[sum_row_index].cells
                sum_row_cells[0].text = "合计"
                sum_row_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER # 合计居中
                for run in sum_row_cells[0].paragraphs[0].runs:
                     run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                for j, total in enumerate(yearly_totals):
                    cell_index = j + 1
                    if cell_index < len(sum_row_cells):
                        sum_row_cells[cell_index].text = f"{int(total)}" if total != 0 else "—"
                        sum_row_cells[cell_index].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in sum_row_cells[cell_index].paragraphs[0].runs:
                             run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)
                    else:
                         logger.warning(f"填充合计时索引越界: col={cell_index}")
                         break

                # --- 填充总计行 (第14行，索引13) ---
                grand_total_row_index = 13
                total_cells = budget_table.rows[grand_total_row_index].cells
                total_cells[0].text = "总计"
                total_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in total_cells[0].paragraphs[0].runs:
                     run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)

                grand_total = final_adjusted_total  # 使用最终调整后的总计
                # 将总计值存储到report数据中，供立项通知直接使用
                report['五、经费概算']['grand_total'] = int(grand_total) if grand_total != 0 else 0
                if years > 0:
                    total_cells[1].text = f"{int(grand_total)}" if grand_total != 0 else "—"
                    total_cells[1].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in total_cells[1].paragraphs[0].runs:
                        run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)
                    # 合并总计行的年份单元格
                    if years > 1:
                        total_cells[1].merge(total_cells[years]) # 从第1个合并到最后一个
                logger.info("成功添加经费概算表格")

            else:
                # 如果经费概算数据无效或缺失
                p = doc.add_paragraph("未提供或未能解析有效的经费概算数据。")
                p.paragraph_format.line_spacing = 1.5
                for run in p.runs:
                    run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(11)
                logger.warning("经费概算数据无效或缺失，未添加表格")

            # --- 在经费概算处理 *之后*，检查是否添加仪器设备清单 ---

            if has_equipment_section:
                # 添加设备清单标题
                p_equip_heading = doc.add_heading("六、采用的仪器设备清单", level=2)
                p_equip_heading.paragraph_format.line_spacing = 1.5
                # 设置标题字体为黑体，四号（14磅），黑色
                for run in p_equip_heading.runs:
                    run.font.name = '黑体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
                    run.font.size = Pt(14) # 四号字体为14磅
                    run.font.color.rgb = RGBColor(0, 0, 0) # 黑色
                logger.debug("添加仪器设备清单标题")

                # 创建设备清单表格
                equip_table = doc.add_table(rows=len(equipment_list) + 1, cols=4)
                equip_table.style = 'Table Grid'
                equip_table.alignment = WD_ALIGN_PARAGRAPH.CENTER
                equip_table.autofit = False # 关闭自动调整

                # 设置列宽 (按照指定尺寸)
                equip_table.columns[0].width = Cm(1.4)   # 序号列：1.4厘米
                equip_table.columns[1].width = Cm(4.28)  # 设备名称列：4.28厘米
                equip_table.columns[2].width = Cm(2.42)  # 设备原值列：2.42厘米
                equip_table.columns[3].width = Cm(7.26)  # 设备作用列：7.26厘米
                logger.debug("仪器设备表格列宽设置完成：序号=1.4cm, 名称=4.28cm, 原值=2.42cm, 作用=7.26cm")

                # 设置默认行高和单元格格式
                for row in equip_table.rows:
                    row.height = Cm(0.6) # 根据需要调整行高
                    for cell in row.cells:
                        cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                        for paragraph in cell.paragraphs:
                            paragraph.paragraph_format.keep_together = True
                            paragraph.paragraph_format.space_after = Pt(6) # 调整段后间距
                            paragraph.paragraph_format.space_before = Pt(6) # 调整段前间距

                # --- 填充设备表头 ---
                equip_hdr_cells = equip_table.rows[0].cells
                equip_hdr_cells[0].text = "序号"
                equip_hdr_cells[1].text = "设备名称"
                equip_hdr_cells[2].text = "设备原值（万元）"
                equip_hdr_cells[3].text = "设备作用"
                for cell in equip_hdr_cells:
                    for paragraph in cell.paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        # 设置行距为0.8厘米（约22.68磅）
                        paragraph.paragraph_format.line_spacing = Pt(22.68)
                        for run in paragraph.runs:
                            run.font.name = '宋体'
                            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                            run.font.size = Pt(12) # 表头使用小四号字（12磅）
                            run.bold = True # 表头加粗
                logger.debug("仪器设备表格表头填充完成")

                # --- 填充设备数据行 ---
                for idx, equip_item in enumerate(equipment_list):
                    row_cells = equip_table.rows[idx + 1].cells
                    row_cells[0].text = str(idx + 1)
                    row_cells[1].text = equip_item.get("name", "")
                    row_cells[2].text = equip_item.get("value", "") # 直接使用处理好的字符串
                    row_cells[3].text = equip_item.get("description", "")

                    # 设置数据单元格格式
                    for cell_idx, cell in enumerate(row_cells):
                         for paragraph in cell.paragraphs:
                            # 序号和原值居中，名称和作用左对齐
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER if cell_idx in [0, 2] else WD_ALIGN_PARAGRAPH.LEFT
                            paragraph.paragraph_format.keep_together = True
                            # 设置行距为0.8厘米（约22.68磅）
                            paragraph.paragraph_format.line_spacing = Pt(22.68)
                            for run in paragraph.runs:
                                run.font.name = '宋体'
                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                run.font.size = Pt(12) # 内容使用小四号字（12磅）
                logger.info(f"成功添加包含 {len(equipment_list)} 条记录的仪器设备清单表格")

                # 在设备清单表格后添加一个空段落，增加视觉间距
                doc.add_paragraph()
            else:
                # 如果设备列表为空或少于3项，跳过设备清单部分
                if equipment_list:
                    logger.warning(f"设备列表项目数量不足（{len(equipment_list)} < 3），跳过设备清单部分")
                else:
                    logger.warning("设备列表为空，跳过设备清单部分")

        # --- 处理主要研究人员名单表格 ---
        elif section == "七、主要研究人员名单":
            # 根据是否有设备清单部分来调整标题序号
            staff_heading_text = "七、主要研究人员名单" if has_equipment_section else "六、主要研究人员名单"
            p_staff_heading = doc.add_heading(staff_heading_text, level=2) # 手动添加标题
            p_staff_heading.paragraph_format.line_spacing = 1.5
            # 设置标题字体为黑体，四号（14磅），黑色
            for run in p_staff_heading.runs:
                run.font.name = '黑体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
                run.font.size = Pt(14) # 四号字体为14磅
                run.font.color.rgb = RGBColor(0, 0, 0) # 黑色
            logger.debug("处理主要研究人员名单部分...")

            # 添加表格前的空行
            p = doc.add_paragraph()
            p.paragraph_format.line_spacing = 1.5

            if not staff_names:
                staff_names = ["姓名在此输入"] # 使用默认占位符
                logger.warning("未提供有效的研究人员姓名，使用默认占位符")

            num_rows = len(staff_names) + 1
            staff_table = doc.add_table(rows=num_rows, cols=4)
            staff_table.style = 'Table Grid'
            staff_table.alignment = WD_ALIGN_PARAGRAPH.CENTER
            staff_table.autofit = False # 关闭自动调整

            # 设置列宽
            staff_table.columns[0].width = Cm(3.0) # 姓名
            staff_table.columns[1].width = Cm(2.0) # 性别
            staff_table.columns[2].width = Cm(4.0) # 职务（职称）
            staff_table.columns[3].width = Cm(4.0) # 岗位
            logger.debug("研究人员表格列宽设置完成")

            # 设置默认行高和单元格格式
            for row in staff_table.rows:
                row.height = Cm(0.6) # 调整行高
                for cell in row.cells:
                    cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                    for paragraph in cell.paragraphs:
                        paragraph.paragraph_format.keep_together = True
                        paragraph.paragraph_format.space_after = Pt(6) # 调整段后间距
                        paragraph.paragraph_format.space_before = Pt(6) # 调整段前间距

            # --- 填充人员表头 ---
            staff_hdr_cells = staff_table.rows[0].cells
            staff_hdr_cells[0].text = "姓名"
            staff_hdr_cells[1].text = "性别"
            staff_hdr_cells[2].text = "职务（职称）"
            staff_hdr_cells[3].text = "岗位"
            for cell in staff_hdr_cells:
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12); run.bold = True
            logger.debug("研究人员表格表头填充完成")

            # --- 填充人员数据行 ---
            total_staff = len(staff_names)
            # 简单的岗位分配逻辑 (70% 设计，后两人试制/质管)
            rd_design_count = math.floor(total_staff * 0.7) if total_staff > 1 else 0

            for idx, name in enumerate(staff_names):
                row_cells = staff_table.rows[idx + 1].cells
                row_cells[0].text = name # 姓名
                row_cells[1].text = guess_gender(name) # 性别猜测

                # 职务和岗位分配
                if idx == 0: # 第一个人
                    row_cells[2].text = "研发总监"
                    row_cells[3].text = "项目负责人"
                else:
                    row_cells[2].text = "研发工程师"
                    if idx < rd_design_count + 1:
                         row_cells[3].text = "研发设计"
                    elif total_staff >= 4 and idx == total_staff - 2: # 倒数第二个 (至少4人时)
                         row_cells[3].text = "研发试制"
                    elif total_staff >= 3 and idx == total_staff - 1: # 最后一个 (至少3人时)
                         row_cells[3].text = "质量管理"
                    else: # 其他情况或人数较少时默认为设计
                         row_cells[3].text = "研发设计"

                # 设置数据单元格格式
                for cell in row_cells:
                    for paragraph in cell.paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        paragraph.paragraph_format.keep_together = True
                        for run in paragraph.runs:
                            run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(12)
            logger.info(f"成功添加包含 {len(staff_names)} 人的主要研究人员名单表格")

            # --- 添加签名和日期 ---
            # 添加空行
            p = doc.add_paragraph()
            p.paragraph_format.line_spacing = 1.5

            # 公司名称（带后缀）
            company_name_display = company_name
            if not company_name.endswith(("有限公司", "有限责任公司")):
                 company_name_display = f"{company_name}有限公司"

            # 格式化签名日期（使用项目开始日期）
            try:
                signature_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                formatted_date = signature_date_obj.strftime("%Y 年 %m 月 %d 日")
            except ValueError:
                formatted_date = start_date # 格式错误则使用原始字符串

            signature_text = f"{company_name_display}\n{formatted_date}"
            p_signature = doc.add_paragraph(signature_text)
            p_signature.alignment = WD_ALIGN_PARAGRAPH.RIGHT # 右对齐
            p_signature.paragraph_format.line_spacing = 1.5
            for run in p_signature.runs:
                 run.font.name = '宋体'; run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体'); run.font.size = Pt(13)
            logger.debug("添加签名和日期完成")


        # --- 处理普通文本内容 ---
        elif content and isinstance(content, str):
             # 特殊处理关键技术和创新点部分
             if section in ["2. 关键技术", "3. 创新点"]:
                 # 首先，确保内容是有效的，并且序号连续
                 valid_content = []

                 # 分割成段落，但保留序号
                 paragraphs = content.split("\n")

                 # 收集所有有效段落
                 valid_paragraphs = []
                 for para_text in paragraphs:
                     para_text_stripped = para_text.strip()
                     if para_text_stripped:  # 只保留非空段落
                         valid_paragraphs.append(para_text_stripped)

                 # 重新构建内容，确保序号连续
                 if valid_paragraphs:
                     # 提取所有带序号的内容
                     numbered_items = []
                     for para in valid_paragraphs:
                         match = re.match(r'^([①②③④⑤⑥⑦⑧⑨])(.*)', para)
                         if match:
                             number, content_text = match.groups()
                             if content_text.strip():  # 只保留有内容的项
                                 numbered_items.append((number, content_text.strip()))

                     # 如果没有有效的带序号内容，添加默认内容
                     if not numbered_items:
                         numbered_items = [(chr(0x2460), "未提供相关内容")]

                     # 重新生成连续序号的内容
                     for i, (_, item_content) in enumerate(numbered_items):
                         number = chr(0x2460 + i)  # ①, ②, ③, ...
                         valid_content.append(f"{number}{item_content}")
                 else:
                     # 如果没有有效段落，添加默认内容
                     valid_content = ["①未提供相关内容"]

                 # 添加处理后的内容到文档
                 for para_text in valid_content:
                     p_text = doc.add_paragraph()
                     p_text.paragraph_format.line_spacing = 1.5  # 设置行距

                     # 设置首行缩进2个字符（约28磅）
                     p_text.paragraph_format.left_indent = Pt(0)  # 不设置左缩进
                     p_text.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2个字符

                     # 添加文本内容并设置字体
                     run = p_text.add_run(para_text)
                     run.font.name = '宋体'
                     run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                     run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                 logger.debug(f"添加关键技术/创新点内容完成: Section='{section}'，共 {len(valid_content)} 项")
             else:
                 # 处理其他普通文本内容
                 # 分割成段落
                 paragraphs = content.split("\n\n")
                 for para_text in paragraphs:
                     para_text_stripped = para_text.strip()
                     if not para_text_stripped:  # 跳过空段落
                         continue

                     p_text = doc.add_paragraph()
                     p_text.paragraph_format.line_spacing = 1.5  # 设置行距

                     # --- 根据标题级别和内容设置缩进 ---
                     # 对于"2. 项目开发的目的和意义"部分，需要特殊处理
                     if section == "2. 项目开发的目的和意义":
                         # 特殊处理"目的和意义"部分，避免重复和确保正确缩进
                         # 检查整个段落是否同时包含"目的"和"意义"
                         if "目的" in para_text_stripped and "意义" in para_text_stripped:
                             # 分割段落，提取目的和意义部分
                             parts = para_text_stripped.split("意义")
                             purpose_text = parts[0].replace("目的", "").strip()
                             meaning_text = parts[1].strip() if len(parts) > 1 else ""

                             # 清理特殊字符和可能重复的"目的"和"意义"字样
                             purpose_text = re.sub(r'^[:：↓→\s]+', '', purpose_text)
                             purpose_text = re.sub(r'^目的\s*', '', purpose_text)  # 移除开头可能重复的"目的"
                             purpose_text = re.sub(r'^目的\s*', '', purpose_text)  # 再次检查，以防有多重嵌套

                             meaning_text = re.sub(r'^[:：↓→\s]+', '', meaning_text)
                             meaning_text = re.sub(r'^意义\s*', '', meaning_text)  # 移除开头可能重复的"意义"
                             meaning_text = re.sub(r'^意义\s*', '', meaning_text)  # 再次检查，以防有多重嵌套

                             # 确保内容不为空或不完整
                             if not purpose_text.strip() or len(purpose_text.strip()) < 20 or not purpose_text.strip().endswith(('。', '.')):
                                 purpose_text = "本项目旨在解决行业中的关键技术问题，提高产品性能和质量。通过创新设计和工艺改进，克服现有技术的局限性，实现更高效、更可靠的产品性能。"
                             if not meaning_text.strip() or len(meaning_text.strip()) < 20 or not meaning_text.strip().endswith(('。', '.')):
                                 meaning_text = "项目成果能够解决行业内的实际技术难题，填补相关技术空白。通过技术创新和工艺优化，显著提升产品的关键性能指标，降低生产成本，提高生产效率。项目实施后将形成具有自主知识产权的核心技术，增强企业在细分市场的技术优势和竞争地位。"

                             # 添加目的标题
                             p_text.paragraph_format.first_line_indent = Pt(0)  # 标题不缩进
                             run = p_text.add_run("目的：")
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 添加目的内容
                             p_content = doc.add_paragraph()
                             p_content.paragraph_format.line_spacing = 1.5  # 设置行距
                             p_content.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符
                             run = p_content.add_run(purpose_text)
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 添加意义标题和内容
                             if meaning_text:
                                 p_meaning_title = doc.add_paragraph()
                                 p_meaning_title.paragraph_format.line_spacing = 1.5
                                 p_meaning_title.paragraph_format.first_line_indent = Pt(0)  # 标题不缩进
                                 run = p_meaning_title.add_run("意义：")
                                 run.font.name = '宋体'
                                 run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                 run.font.size = Pt(13)

                                 p_meaning_content = doc.add_paragraph()
                                 p_meaning_content.paragraph_format.line_spacing = 1.5
                                 p_meaning_content.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符
                                 run = p_meaning_content.add_run(meaning_text)
                                 run.font.name = '宋体'
                                 run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                 run.font.size = Pt(13)

                             # 跳过后面的文本添加，因为我们已经手动添加了
                             continue

                         # 处理单独的"目的："段落
                         elif para_text_stripped.startswith("目的：") or (para_text_stripped.startswith("目的") and not para_text_stripped.startswith("目的和意义")):
                             title = "目的："
                             # 去掉"目的："或"目的"，并清理特殊字符
                             if para_text_stripped.startswith("目的："):
                                 # 提取目的后面的内容，跳过冒号
                                 raw_content = para_text_stripped[3:].strip()
                             else:
                                 # 提取目的后面的内容
                                 raw_content = para_text_stripped[2:].strip()

                             # 清理内容中的特殊字符和格式标记
                             # 移除常见的特殊字符，如冒号、箭头等
                             content = re.sub(r'^[:：↓→\s]+', '', raw_content)
                             # 移除可能重复的"目的"字样
                             content = re.sub(r'^目的\s*', '', content)
                             # 再次检查，以防有多重嵌套
                             content = re.sub(r'^目的\s*', '', content)

                             # 如果清理后内容为空或不完整（少于20个字符或没有句号结尾），使用默认文本
                             if not content.strip() or len(content.strip()) < 20 or not content.strip().endswith(('。', '.')):
                                 content = "本项目旨在解决行业中的关键技术问题，提高产品性能和质量。通过创新设计和工艺改进，克服现有技术的局限性，实现更高效、更可靠的产品性能。"

                             # 添加标题
                             p_text.paragraph_format.first_line_indent = Pt(0)  # 标题不缩进
                             run = p_text.add_run(title)
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 添加内容
                             p_content = doc.add_paragraph()
                             p_content.paragraph_format.line_spacing = 1.5  # 设置行距
                             p_content.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符
                             run = p_content.add_run(content)
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 跳过后面的文本添加
                             continue

                         # 处理单独的"意义："段落
                         elif para_text_stripped.startswith("意义：") or para_text_stripped.startswith("意义"):
                             title = "意义："
                             # 去掉"意义："或"意义"，并清理特殊字符
                             if para_text_stripped.startswith("意义："):
                                 # 提取意义后面的内容，跳过冒号
                                 raw_content = para_text_stripped[3:].strip()
                             else:
                                 # 提取意义后面的内容
                                 raw_content = para_text_stripped[2:].strip()

                             # 清理内容中的特殊字符和格式标记
                             # 移除常见的特殊字符，如冒号、箭头等
                             content = re.sub(r'^[:：↓→\s]+', '', raw_content)
                             # 移除可能重复的"意义"字样
                             content = re.sub(r'^意义\s*', '', content)
                             # 再次检查，以防有多重嵌套
                             content = re.sub(r'^意义\s*', '', content)

                             # 如果清理后内容为空或不完整（少于20个字符或没有句号结尾），使用默认文本
                             if not content.strip() or len(content.strip()) < 20 or not content.strip().endswith(('。', '.')):
                                 content = "项目成果能够解决行业内的实际技术难题，填补相关技术空白。通过技术创新和工艺优化，显著提升产品的关键性能指标，降低生产成本，提高生产效率。项目实施后将形成具有自主知识产权的核心技术，增强企业在细分市场的技术优势和竞争地位。"

                             # 添加标题
                             p_text.paragraph_format.first_line_indent = Pt(0)  # 标题不缩进
                             run = p_text.add_run(title)
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 添加内容
                             p_content = doc.add_paragraph()
                             p_content.paragraph_format.line_spacing = 1.5  # 设置行距
                             p_content.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符
                             run = p_content.add_run(content)
                             run.font.name = '宋体'
                             run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                             run.font.size = Pt(13)  # 小四号字 (13pt 接近)

                             # 跳过后面的文本添加
                             continue
                         else:
                             p_text.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符 (小四号字)
                     elif level == 3:  # 其他三级标题下的内容
                         p_text.paragraph_format.first_line_indent = Pt(28)  # 首行缩进2字符 (小四号字)
                     else:  # 其他级别的标题下的文本（如一级、二级，虽然通常是标题）
                         p_text.paragraph_format.first_line_indent = Pt(28)  # 默认首行缩进

                     # 添加文本内容并设置字体
                     run = p_text.add_run(para_text_stripped)
                     run.font.name = '宋体'
                     run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                     run.font.size = Pt(13)  # 小四号字 (13pt 接近)
                 logger.debug(f"添加文本内容完成: Section='{section}'")

        # 处理空内容或其他类型 (选择性添加空段落或日志)
        elif not content:
             # 可以添加一个空段落以保持间距，如果需要的话
             # p = doc.add_paragraph()
             # p.paragraph_format.line_spacing = 1.5
             logger.debug(f"Section '{section}' 内容为空，跳过内容添加。")


    # --- 保存文档 ---
    try:
        doc.save(output_file_path)
        logger.info(f"报告已成功保存到: {output_file_path}")
    except PermissionError as e:
        logger.error(f"保存 Word 文档 '{output_file_path}' 失败: 权限不足或文件被占用。错误: {e}")
        raise IOError(f"保存报告 '{output_file_path}' 失败: 权限不足或文件被占用。")
    except Exception as e:
        logger.error(f"保存 Word 文档 '{output_file_path}' 时发生意外错误: {e}")
        raise IOError(f"保存报告 '{output_file_path}' 时发生意外错误: {e}")

# ==============================================================================
# 函数：save_report_to_docx 结束
# ==============================================================================

def save_acceptance_report_to_docx(report, output_file_path, company_name, start_date, end_date, project_name, rd_number, staff_names, acceptance_number=None):
    # 构建验收报告文件名 - 按照格式：RD序号-项目名称-公司名称-验收报告-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_rd_number = clean_filename(rd_number)
    cleaned_project_name = clean_filename(project_name)
    cleaned_company_name = clean_filename(company_name)

    # 获取输出路径的目录部分
    output_dir = os.path.dirname(output_file_path)
    acceptance_file_path = os.path.join(output_dir, f"{cleaned_rd_number}-{cleaned_project_name}-{cleaned_company_name}-验收报告-{timestamp}.docx")

    doc = Document()
    sections = doc.sections
    for section in sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

    p = doc.add_paragraph("验收报告")
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        run.font.name = '黑体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(25)
    doc.add_paragraph()

    table = doc.add_table(rows=9, cols=4)
    table.style = 'Table Grid'
    table.autofit = True

    for row in table.rows:
        row.height = Cm(1.0)
        for cell in row.cells:
            cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                paragraph.paragraph_format.space_before = Pt(0)
                paragraph.paragraph_format.space_after = Pt(0)
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(12)

    table.rows[0].cells[0].text = "项目名称"
    table.rows[0].cells[1].merge(table.rows[0].cells[3])
    table.rows[0].cells[1].text = project_name

    table.rows[1].cells[0].text = "验收编号"
    table.rows[1].cells[1].merge(table.rows[1].cells[3])
    # 使用自定义验收报告编号（如果提供）或默认的YS-RD序号
    display_acceptance_number = acceptance_number if acceptance_number else f"YS-{rd_number}"
    table.rows[1].cells[1].text = display_acceptance_number

    table.rows[2].cells[0].text = "起止时间"
    table.rows[2].cells[1].merge(table.rows[2].cells[3])
    start_date_str = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m-%d")
    end_date_str = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m-%d")
    table.rows[2].cells[1].text = f"{start_date_str} 至 {end_date_str}"

    table.rows[3].cells[0].text = "项目完成人"
    table.rows[3].cells[1].merge(table.rows[3].cells[3])
    completers = "，".join(staff_names) if staff_names else "姓名在此输入"
    table.rows[3].cells[1].text = completers

    table.rows[4].cells[0].text = "预算经费"
    # 使用调整后的预算数据填入预算经费
    adjusted_total = report.get("五、经费概算", {}).get("adjusted_total", 0.0)
    table.rows[4].cells[1].text = f"{int(adjusted_total)} 万元" if adjusted_total > 0 else ""
    table.rows[4].cells[2].text = "经费使用情况"
    # 使用原始实际数据填入经费使用情况
    original_total = report.get("五、经费概算", {}).get("original_total", 0.0)
    if original_total == 0.0:
        # 兼容旧数据格式
        budget_data = report.get("五、经费概算", {}).get("data", {})
        for year, subjects in budget_data.items():
            original_total += sum(float(amount) for subject, amount in subjects.items() if subject != "合计" and amount)
    table.rows[4].cells[3].text = f"{original_total:.2f} 万元"

    table.rows[5].cells[0].text = "主要内容及技术指标情况"
    table.rows[5].cells[1].merge(table.rows[5].cells[3])
    innovations = report.get("3. 创新点", "")
    innovation_lines = innovations.split("\n\n")
    formatted_innovations = []
    for line in innovation_lines:
        formatted_line = line.replace("\n", " ").strip()
        formatted_innovations.append(formatted_line)
    innovations_text = "\n".join(formatted_innovations)

    tech_indicators = report.get("4. 达到的主要技术或经济指标", "")
    indicator_lines = tech_indicators.split("\n\n")
    formatted_indicators = []
    for line in indicator_lines:
        formatted_line = line.replace("\n", " ").strip()
        formatted_indicators.append(formatted_line)
    indicators_text = "\n".join(formatted_indicators)

    content = f"1. 创新点：\n{innovations_text}\n\n2. 技术指标：\n{indicators_text}"
    table.rows[5].cells[1].text = content

    table.rows[6].cells[0].text = "取得成果情况"
    table.rows[6].cells[1].merge(table.rows[6].cells[3])
    table.rows[6].cells[1].text = "该项目自立项以来，团队精心设计，反复试验，设备实际测试达标，满足设计初衷。"

    table.rows[7].cells[0].text = "验收意见"
    table.rows[7].cells[1].merge(table.rows[7].cells[3])
    table.rows[7].cells[1].text = "\n\n验收人签名：\n时间："

    table.rows[8].cells[0].text = "公司意见"
    table.rows[8].cells[1].merge(table.rows[8].cells[3])
    table.rows[8].cells[1].text = ""

    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(12)

    doc.save(acceptance_file_path)
    logger.info(f"验收报告已保存到 {acceptance_file_path}")
    return acceptance_file_path

def save_project_approval_notice_to_docx(year, projects, company_name, output_path):
    from docx import Document
    from docx.shared import Pt, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_ALIGN_VERTICAL, WD_TABLE_ALIGNMENT
    from docx.oxml.ns import qn

    doc = Document()
    sections = doc.sections
    for section in sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

        header = section.header
        p = header.paragraphs[0]
        p.text = company_name
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        for run in p.runs:
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(10)

    doc.add_paragraph()
    p = doc.add_paragraph("项目立项通知")
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        run.font.name = '黑体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(20)

    doc.add_paragraph()

    # 筛选指定年份启动的项目
    year = int(year)
    filtered_projects = [
        project for project in projects
        if int(project['start_date'][:4]) == year
    ]

    # 如果没有符合条件的项目，添加提示
    if not filtered_projects:
        p = doc.add_paragraph(f"{year}年无立项项目。")
        p.paragraph_format.line_spacing = 1.5
        for run in p.runs:
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(12)

        signature_date = datetime.now().strftime("%Y.%m.%d")
        p = doc.add_paragraph(f"{company_name}\n{signature_date}")
        p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        for run in p.runs:
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(12)

        approval_filename = f"{company_name}-{year}-立项通知-{datetime.now().strftime('%Y-%m-%d')}.docx"
        if output_path and os.path.isdir(output_path):
            approval_file_path = os.path.join(output_path, approval_filename)
        else:
            approval_file_path = approval_filename

        doc.save(approval_file_path)
        logger.info(f"立项通知报告已保存到 {approval_file_path}")
        return approval_file_path

    # 使用第一个项目的开始日期设置会议日期
    start_date = filtered_projects[0]['start_date']
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    meeting_date = start_date_obj.strftime("%Y年%m月%d日")

    p = doc.add_paragraph(f"会议日期：{meeting_date}")
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph("会议地点：会议室")
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph("会议内容：项目立项及开发经费预决算")
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    doc.add_paragraph()

    p = doc.add_paragraph("本次办公会议形成以下决议：")
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph(
        f"1. 为了确保公司持续发展，加快开发项目的进度，根据市场调研情况及未来的发展趋势，确定{year}年开发项目（具体见下表），"
    )
    p.paragraph_format.first_line_indent = Pt(24)
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph(
        "2. 财务部门应安排好开发项目所需的经费，开发部应编制开发经费计划报总经理批准后执行。"
    )
    p.paragraph_format.first_line_indent = Pt(24)
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph(
        "3. 人力资源部应根据开发的需要通过各种渠道加大对科技人才的引进。"
    )
    p.paragraph_format.first_line_indent = Pt(24)
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    p = doc.add_paragraph(
        "4. 各相关部门应协同配合，支持开发项目的顺利进行，提供必要的支持和协助。"
    )
    p.paragraph_format.first_line_indent = Pt(24)
    p.paragraph_format.line_spacing = 1.5
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    doc.add_paragraph()

    num_projects = len(filtered_projects)
    table = doc.add_table(rows=num_projects + 1, cols=4)
    table.style = 'Table Grid'
    table.autofit = False
    table.alignment = WD_TABLE_ALIGNMENT.CENTER  # 表格居中对齐

    # 设置列宽
    table.columns[0].width = Cm(2.5)  # 项目编号
    table.columns[1].width = Cm(4.8)  # 项目名称
    table.columns[2].width = Cm(4.0)  # 项目起止时间
    table.columns[3].width = Cm(4.7)  # 预算开发经费（万元）

    # 设置表格所有单元格的字体为宋体12磅
    for row in table.rows:
        for cell in row.cells:
            cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(12)

    for row in table.rows:
        row.height = Cm(0.8)
        for cell in row.cells:
            cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER  # 垂直居中
            # 清除现有段落并添加新段落以确保样式一致
            while len(cell.paragraphs) > 0:
                cell.paragraphs[0]._element.getparent().remove(cell.paragraphs[0]._element)
            paragraph = cell.add_paragraph()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER  # 水平居中
            paragraph.paragraph_format.space_before = Pt(0)
            paragraph.paragraph_format.space_after = Pt(0)
            paragraph.paragraph_format.line_spacing = 1.0  # 单倍行距
            for run in paragraph.runs:
                run.font.name = '宋体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run.font.size = Pt(12)

    hdr_cells = table.rows[0].cells
    hdr_cells[0].paragraphs[0].text = "项目编号"
    hdr_cells[1].paragraphs[0].text = "项目名称"
    hdr_cells[2].paragraphs[0].text = "项目起止时间"
    hdr_cells[3].paragraphs[0].text = "预算开发经费（万元）"

    # 设置表头字体加粗
    for cell in hdr_cells:
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.name = '宋体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run.font.size = Pt(12)
                run.bold = True

    for idx, project in enumerate(filtered_projects):
        row_cells = table.rows[idx + 1].cells
        # 使用自定义项目编号（如果有）
        project_number = project.get('project_number', project['rd_number'])
        row_cells[0].paragraphs[0].text = project_number
        row_cells[1].paragraphs[0].text = project['project_name']
        start_date_obj = datetime.strptime(project['start_date'], "%Y-%m-%d")
        end_date_obj = datetime.strptime(project['end_date'], "%Y-%m-%d")
        row_cells[2].paragraphs[0].text = f"{start_date_obj.strftime('%Y.%m')}-{end_date_obj.strftime('%Y.%m')}"
        
        # 直接使用project中的total_budget
        row_cells[3].paragraphs[0].text = f"{int(project['total_budget']) if project['total_budget'] > 0 else 0}"

    doc.add_paragraph()

    signature_date = start_date_obj.strftime("%Y.%m.%d")
    p = doc.add_paragraph(f"{company_name}\n{signature_date}")
    p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    # 构建立项通知文件名 - 按照新格式：立项年份-公司名称-项目通知书-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_company_name = clean_filename(company_name)

    approval_filename = f"{year}-{cleaned_company_name}-项目通知书-{timestamp}.docx"
    if output_path and os.path.isdir(output_path):
        approval_file_path = os.path.join(output_path, approval_filename)
    else:
        approval_file_path = approval_filename

    doc.save(approval_file_path)
    logger.info(f"立项通知报告已保存到 {approval_file_path}")
    return approval_file_path

def save_project_summary_table(projects, company_name, output_path):
    doc = Document()
    sections = doc.sections
    for section in sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

    p = doc.add_heading("2022年-2024年研究开发活动项目一览表", level=2)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        run.font.name = '黑体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(14)

    doc.add_paragraph()

    table = doc.add_table(rows=len(projects) + 1, cols=4)
    table.style = 'Table Grid'
    table.autofit = False
    table.alignment = WD_ALIGN_PARAGRAPH.CENTER
    table.columns[0].width = Cm(2.0)
    table.columns[1].width = Cm(4.8)
    table.columns[2].width = Cm(4.0)
    table.columns[3].width = Cm(8.8)

    for row in table.rows:
        row.height = Cm(1.0)
        for cell in row.cells:
            cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            for paragraph in cell.paragraphs:
                paragraph.paragraph_format.line_spacing = 1.5
                paragraph.paragraph_format.space_before = Pt(0)
                paragraph.paragraph_format.space_after = Pt(0)
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(11)

    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = "项目编号"
    hdr_cells[1].text = "项目名称"
    hdr_cells[2].text = "实施起止时间"
    hdr_cells[3].text = "证明材料"
    for cell in hdr_cells:
        for paragraph in cell.paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = 1.5
            for run in paragraph.runs:
                run.font.name = '宋体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run.font.size = Pt(11)
                run.bold = True

    for idx, project in enumerate(projects):
        row_cells = table.rows[idx + 1].cells
        # 使用自定义项目编号（如果有）
        project_number = project.get('project_number', project['rd_number'])
        row_cells[0].text = project_number
        row_cells[1].text = project['project_name']
        start_date = datetime.strptime(project['start_date'], "%Y-%m-%d").strftime("%Y-%m")
        end_date = datetime.strptime(project['end_date'], "%Y-%m-%d").strftime("%Y-%m")
        row_cells[2].text = f"{start_date}至{end_date}"
        start_year = project['start_date'][:4]
        # 使用自定义验收报告编号（如果有）
        acceptance_number = project.get('acceptance_number', f"YS-{project['rd_number']}")
        row_cells[3].text = (
            f"1、关于对{start_year}年技术创新项目进行立项通知\n"
            f"2、研究开发项目计划书\n"
            f"3、验收报告：{acceptance_number}"
        )
        for cell in row_cells[:3]:
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                paragraph.paragraph_format.line_spacing = 1.5
        for paragraph in row_cells[3].paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
            paragraph.paragraph_format.line_spacing = 1.5

    # 构建研发活动一览表文件名 - 按照新格式：研发活动一览表-公司名称-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_company_name = clean_filename(company_name)

    summary_filename = f"研发活动一览表-{cleaned_company_name}-{timestamp}.docx"
    if output_path and os.path.isdir(output_path):
        summary_file_path = os.path.join(output_path, summary_filename)
    else:
        summary_file_path = summary_filename

    doc.save(summary_file_path)
    logger.info(f"研发活动一览表已保存到 {summary_file_path}")
    return summary_file_path

def save_rd_ps_table(projects, company_name, output_path):
    from docx import Document
    from docx.shared import Pt, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_ALIGN_VERTICAL
    from docx.oxml.ns import qn
    from docx.table import _Cell
    from docx.oxml import OxmlElement
    import os
    from datetime import datetime
    import logging
    import re

    logger = logging.getLogger(__name__)
    logger.info(f"开始生成 RD_PS 表，输出路径: {output_path}")

    doc = Document()

    # 设置页面边距，确保内容适应 A4 页面
    for section in doc.sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

    for project_idx, project in enumerate(projects):
        project_name = project['project_name']
        rd_number = project['rd_number']
        start_date = project['start_date']
        end_date = project['end_date']
        staff_names = project['staff_names']
        report = project['report']
        budget_data = report.get('五、经费概算', {}).get('data', {})

        # 计算研发人员数量
        researcher_count = len(staff_names) if staff_names else 0

        # 使用调整后的预算数据计算近3年总支出
        adjusted_total = report.get('五、经费概算', {}).get('adjusted_total', 0.0)
        original_total = report.get('五、经费概算', {}).get('original_total', 0.0)
        
        if adjusted_total > 0:
            # 使用调整后的预算数据
            total_budget = adjusted_total
        else:
            # 兼容旧数据格式，计算原始数据
            total_budget = 0.0
            for year, subjects in budget_data.items():
                year_total = sum(float(amount) for subject, amount in subjects.items() if subject != "合计" and amount)
                total_budget += year_total
        
        # 获取原始数据用于显示经费使用情况
        if original_total == 0.0:
            # 兼容旧数据格式，计算原始数据  
            for year, subjects in budget_data.items():
                year_total = sum(float(amount) for subject, amount in subjects.items() if subject != "合计" and amount)
                original_total += year_total
        
        # 获取每年的原始经费数据
        budget_2022 = sum(float(amount) for subject, amount in original_data.get('2022', {}).items() if amount and subject != "合计") if '2022' in original_data else 0
        budget_2023 = sum(float(amount) for subject, amount in original_data.get('2023', {}).items() if amount and subject != "合计") if '2023' in original_data else 0
        budget_2024 = sum(float(amount) for subject, amount in original_data.get('2024', {}).items() if amount and subject != "合计") if '2024' in original_data else 0

        # 获取立项目的
        purpose = ""
        if '2. 项目开发的目的和意义' in report:
            purpose_data = report['2. 项目开发的目的和意义']
            if isinstance(purpose_data, list) and purpose_data:
                purpose = purpose_data[0].strip()
            else:
                purpose_content = str(purpose_data).split('意义')[0].strip()
                # 移除可能存在的"目的"标题
                purpose = re.sub(r'^.*?目的[：:]\s*', '', purpose_content).strip()
                # 如果移除后为空，则使用原内容
                if not purpose:
                    purpose = purpose_content

        # 组织实施方式
        org_implementation = (
            "根据公司年度新产品开发计划和技术任务书组织实施，独立自主研究，通过技术创新成立了"
            f"'{project_name}'专业的研发小组，配备了{researcher_count}名研发人员，"
            f"预算{int(total_budget) if total_budget > 0 else 0}万元组织实施本项目的研究开发。"
        )
        purpose_and_org = f"1.立项目的：{purpose}\n\n2.组织实施方式：{org_implementation}" if purpose else "1.立项目的：未提供项目开发目的\n\n2.组织实施方式：未提供组织实施方式"

        # 核心技术及创新点（调用 trim_content_for_rdrps 精简内容）
        key_tech = report.get('2. 关键技术', '').strip() or "未找到关键技术"
        innovation = report.get('3. 创新点', '').strip() or "未找到创新点"
        trimmed_key_tech, trimmed_innovation = trim_content_for_rdrps(key_tech, innovation)

        # 直接使用 trim_content_for_rdrps 的结果，保留序号和换行
        tech_and_innov = f"1.关键技术：\n{trimmed_key_tech}\n\n2.创新点：\n{trimmed_innovation}"

        # 添加项目编号（使用原始RD序号）
        rd_paragraph = doc.add_paragraph()
        rd_run = rd_paragraph.add_run(rd_number)
        rd_run.font.name = '黑体'
        rd_run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        rd_run.font.size = Pt(14)
        rd_run.bold = True

        # 创建表格
        table = doc.add_table(rows=13, cols=2)
        table.autofit = False
        table.columns[0].width = Cm(3.6)
        table.columns[1].width = Cm(11.6)

        # 设置行高 0.5cm（核心技术及创新点部分可能需要更高行高）
        for idx, row in enumerate(table.rows):
            if idx == 11:  # "核心技术及创新点"所在行
                row.height = Cm(2.0)  # 增加行高以适应多行内容
            else:
                row.height = Cm(0.5)

        # 设置表格为实体线条
        for row in table.rows:
            for cell in row.cells:
                tc = cell._element
                tcPr = tc.get_or_add_tcPr()
                tcBorders = OxmlElement('w:tcBorders')
                for border_name in ['top', 'left', 'bottom', 'right']:
                    border = OxmlElement(f'w:{border_name}')
                    border.set(qn('w:val'), 'single')
                    border.set(qn('w:sz'), '4')
                    border.set(qn('w:space'), '0')
                    border.set(qn('w:color'), '000000')
                    tcBorders.append(border)
                tcPr.append(tcBorders)

        # 填充表格内容
        table.cell(0, 0).text = "项目名称"
        table.cell(0, 1).text = project_name
        table.cell(1, 0).text = "起止时间"
        table.cell(1, 1).text = f"{start_date}至{end_date}"
        table.cell(2, 0).text = "技术领域"
        table.cell(2, 1).text = ""
        table.cell(3, 0).text = "技术来源"
        table.cell(3, 1).text = "企业自有技术"
        table.cell(4, 0).text = "本项目研发人员数"
        table.cell(4, 1).text = str(researcher_count)
        table.cell(5, 0).text = "研发经费总预算"
        table.cell(5, 1).text = f"{int(total_budget)}" if total_budget > 0 else ""
        table.cell(6, 0).text = "研发经费近3年总支出"
        table.cell(6, 1).text = f"{original_total:.2f}" if original_total > 0 else ""
        table.cell(7, 0).text = "第一年2022年经费"
        table.cell(7, 1).text = f"{budget_2022:.2f}" if budget_2022 > 0 else ""
        table.cell(8, 0).text = "第二年2023年经费"
        table.cell(8, 1).text = f"{budget_2023:.2f}" if budget_2023 > 0 else ""
        table.cell(9, 0).text = "第三年2024年经费"
        table.cell(9, 1).text = f"{budget_2024:.2f}" if budget_2024 > 0 else ""
        table.cell(10, 0).text = "立项目的及组织实施方式"
        table.cell(10, 1).text = purpose_and_org
        table.cell(11, 0).text = "核心技术及创新点"
        table.cell(11, 1).text = tech_and_innov
        table.cell(12, 0).text = "取得的阶段性成果"
        table.cell(12, 1).text = ""

        # 设置表格字体为宋体 8 号，垂直居中
        for row in table.rows:
            for cell in row.cells:
                cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    paragraph.paragraph_format.space_before = Pt(0)
                    paragraph.paragraph_format.space_after = Pt(0)
                    paragraph.paragraph_format.line_spacing = 1
                    paragraph.paragraph_format.keep_together = True
                    paragraph.paragraph_format.keep_with_next = True
                    for run in paragraph.runs:
                        run.font.name = '宋体'
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                        run.font.size = Pt(8)

        # 确保表格不跨页
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph.paragraph_format.keep_together = True
                    paragraph.paragraph_format.keep_with_next = True

        # 添加分页符
        if project_idx < len(projects) - 1:
            doc.add_page_break()

    # 保存文档 - 按照新格式：RD_PS表-公司名称-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_company_name = clean_filename(company_name)

    output_filename = f"RD_PS表-{cleaned_company_name}-{timestamp}.docx"
    if output_path and os.path.isdir(output_path):
        output_file_path = os.path.join(output_path, output_filename)
    else:
        output_file_path = output_filename

    try:
        os.makedirs(output_path, exist_ok=True)
        doc.save(output_file_path)
        logger.info(f"RD_PS 表保存成功: {output_file_path}")
    except PermissionError as e:
        logger.error(f"保存 RD_PS 表失败: {e}")
        raise PermissionError(f"保存 RD_PS 表失败: {e}，请检查目录权限或确保文件未被占用")
    except Exception as e:
        logger.error(f"保存 RD_PS 表失败: {e}")
        raise

    return output_file_path

def extract_staff_names(staff_excel, start_date, end_date):
    staff_names = []
    if not staff_excel:
        return staff_names

    try:
        start_year = int(start_date[:4])
        end_year = int(end_date[:4])
        years = list(range(start_year, end_year + 1))
        xl = pd.ExcelFile(staff_excel)
        logger.info(f"检测到的 Sheet 名称: {xl.sheet_names}")

        for sheet_name in xl.sheet_names:
            sheet_year = None
            for year in years:
                if str(year) in sheet_name and "工资" in sheet_name:
                    sheet_year = year
                    break
            if sheet_year is None:
                logger.info(f"跳过 Sheet: {sheet_name}")
                continue

            df = pd.read_excel(staff_excel, sheet_name=sheet_name)
            logger.info(f"正在处理 Sheet: {sheet_name}")
            logger.info(f"列名: {df.columns.tolist()}")

            name_col = None
            for col in df.columns:
                if "姓名" in str(col):
                    name_col = col
                    break
            if name_col is None and not df.iloc[:, 0].str.match(r'^Unnamed:').all():
                name_col = df.columns[0]

            if name_col:
                names = df[name_col].dropna().astype(str).tolist()
                exclude_keywords = ["月份", "姓名", "合计", "总计"]
                filtered_names = [
                    name.strip() for name in names
                    if re.match(r'^[\u4e00-\u9fa5]{2,4}$', name.strip())
                    and not any(kw in name for kw in exclude_keywords)
                ]
                staff_names.extend(filtered_names)
                logger.info(f"从 {sheet_name} 提取到的姓名: {filtered_names}")
            else:
                logger.warning(f"Sheet {sheet_name} 中未找到'姓名'列")

        staff_names = list(dict.fromkeys(staff_names))
        if not staff_names:
            logger.warning("未提取到有效姓名")
            staff_names = ["姓名在此输入"]
        logger.info(f"最终研发人员名单: {staff_names}")
    except Exception as e:
        logger.error(f"处理工资表失败: {e}")
        staff_names = ["姓名在此输入"]

    return staff_names

# ==============================================================================
# 函数：extract_ip_numbers_from_rdps
# 功能：从RDPS表中提取项目名称对应的IP编号
# ==============================================================================
def extract_ip_numbers_from_rdps(rdps_file):
    """
    从RDPS表中提取项目名称对应的IP编号

    Args:
        rdps_file: 上传的RDPS表文件

    Returns:
        dict: 项目名称到IP编号列表的映射
    """
    ip_mapping = {}

    if not rdps_file or not rdps_file.filename:
        logger.info("未提供RDPS表文件")
        return ip_mapping

    try:
        # 确保文件指针在开头
        rdps_file.seek(0)

        # 读取Excel文件
        df = pd.read_excel(rdps_file)
        logger.info(f"成功读取RDPS表，共{len(df)}行数据")
        logger.info(f"RDPS表列名: {list(df.columns)}")

        # 检查必要的列是否存在
        # 第2列：项目名称，第5列：专利名称，第6列：专利号，第7列：专利类型，第8列：IP编号
        if len(df.columns) < 8:
            logger.warning(f"RDPS表列数不足，期望至少8列，实际{len(df.columns)}列")
            return ip_mapping

        # 获取各列
        project_name_col = df.columns[1]  # 第2列：项目名称
        patent_name_col = df.columns[4]   # 第5列：专利名称
        patent_number_col = df.columns[5] # 第6列：专利号
        patent_type_col = df.columns[6]   # 第7列：专利类型
        ip_number_col = df.columns[7]     # 第8列：IP编号

        logger.info(f"使用列：项目名称='{project_name_col}', 专利名称='{patent_name_col}', 专利号='{patent_number_col}', 专利类型='{patent_type_col}', IP编号='{ip_number_col}'")

        # 处理合并单元格：先填充项目名称列的空值
        # 使用forward fill方法，将上一个非空值填充到下面的空值中
        df[project_name_col] = df[project_name_col].ffill()

        logger.info(f"填充合并单元格后的前10行项目名称: {df[project_name_col].head(10).tolist()}")

        # 遍历数据行，建立项目名称到IP编号和专利信息的映射
        for idx, row in df.iterrows():
            project_name = row[project_name_col]
            patent_name = row[patent_name_col]
            patent_number = row[patent_number_col]
            patent_type = row[patent_type_col]
            ip_number = row[ip_number_col]

            # 跳过空值
            if pd.isna(project_name) or pd.isna(ip_number):
                logger.debug(f"跳过第{idx}行：项目名称或IP编号为空")
                continue

            project_name = str(project_name).strip()
            ip_number = str(ip_number).strip()

            # 处理专利信息
            patent_name = str(patent_name).strip() if not pd.isna(patent_name) else ""
            patent_number = str(patent_number).strip() if not pd.isna(patent_number) else ""
            patent_type = str(patent_type).strip() if not pd.isna(patent_type) else ""

            # 跳过空字符串
            if not project_name or not ip_number:
                logger.debug(f"跳过第{idx}行：项目名称或IP编号为空字符串")
                continue

            # 确保IP编号是数字或IP格式
            try:
                # 如果是纯数字，转换为整数再转回字符串（去除小数点）
                if ip_number.replace('.', '').isdigit():
                    ip_number = str(int(float(ip_number)))

                logger.debug(f"第{idx}行：项目='{project_name}', IP编号='{ip_number}', 专利='{patent_name}', 专利号='{patent_number}', 类型='{patent_type}'")

                # 初始化项目数据结构
                if project_name not in ip_mapping:
                    ip_mapping[project_name] = {
                        'ip_numbers': [],
                        'patents': []
                    }

                # 添加IP编号（去重）
                if ip_number not in ip_mapping[project_name]['ip_numbers']:
                    ip_mapping[project_name]['ip_numbers'].append(ip_number)
                    logger.debug(f"为项目'{project_name}'添加IP编号'{ip_number}'")

                # 添加专利信息（如果有专利名称和专利号）
                if patent_name and patent_number:
                    patent_info = {
                        'name': patent_name,
                        'number': patent_number,
                        'type': patent_type,
                        'ip_number': ip_number
                    }
                    # 检查是否已存在相同的专利（根据专利号判断）
                    existing_patent = next((p for p in ip_mapping[project_name]['patents'] if p['number'] == patent_number), None)
                    if not existing_patent:
                        ip_mapping[project_name]['patents'].append(patent_info)
                        logger.debug(f"为项目'{project_name}'添加专利：{patent_name} ({patent_number})")

            except (ValueError, TypeError):
                logger.warning(f"第{idx}行：无效的IP编号格式: {ip_number}")
                continue

        logger.info(f"从RDPS表中提取到{len(ip_mapping)}个项目的IP编号和专利信息映射")
        for project, data in ip_mapping.items():
            logger.info(f"项目'{project}': IP编号{data['ip_numbers']}, 专利数量{len(data['patents'])}")

    except Exception as e:
        logger.error(f"处理RDPS表失败: {e}")

    return ip_mapping

# ==============================================================================
# 函数：extract_project_info_from_rdps  
# 功能：从RDPS表中提取项目的基本信息（项目名称、起止时间、RD序号等）
# ==============================================================================
def extract_project_info_from_rdps(rdps_file):
    """
    从RDPS表中提取项目的基本信息

    Args:
        rdps_file: 上传的RDPS表文件

    Returns:
        list: 项目信息列表，每个元素包含 {'project_name': str, 'start_date': str, 'end_date': str, 'rd_number': str}
    """
    import re
    import calendar
    import random
    
    project_info_list = []

    if not rdps_file or not rdps_file.filename:
        logger.info("未提供RDPS表文件")
        return project_info_list

    try:
        # 确保文件指针在开头
        rdps_file.seek(0)

        # 读取Excel文件，跳过第一行标题
        df = pd.read_excel(rdps_file, header=1)  # header=1表示使用第二行作为列标题
        logger.info(f"成功读取RDPS表，共{len(df)}行数据")
        logger.info(f"RDPS表列名: {list(df.columns)}")

        # 检查列数
        if len(df.columns) < 3:
            logger.warning(f"RDPS表列数不足，期望至少3列，实际{len(df.columns)}列")
            return project_info_list

        # RDPS表的列结构为：
        # 第1列：RD序号，第2列：项目名称，第3列：起止时间
        rd_number_col = df.columns[0]    # 第1列：RD序号
        project_name_col = df.columns[1]  # 第2列：项目名称  
        time_period_col = df.columns[2]   # 第3列：起止时间

        logger.info(f"使用列：RD序号='{rd_number_col}', 项目名称='{project_name_col}', 起止时间='{time_period_col}'")

        # 处理合并单元格：填充项目名称、RD序号等列的空值
        df[rd_number_col] = df[rd_number_col].ffill()
        df[project_name_col] = df[project_name_col].ffill()
        df[time_period_col] = df[time_period_col].ffill()

        # 去重，获取唯一的项目信息
        unique_projects = df.drop_duplicates(subset=[rd_number_col, project_name_col, time_period_col])

        # 遍历每个唯一项目
        for idx, row in unique_projects.iterrows():
            rd_number = row[rd_number_col]
            project_name = row[project_name_col]
            time_period = row[time_period_col]

            # 跳过空值
            if pd.isna(rd_number) or pd.isna(project_name) or pd.isna(time_period):
                continue

            rd_number = str(rd_number).strip()
            project_name = str(project_name).strip()
            time_period = str(time_period).strip()

            # 跳过空字符串或表头信息
            if not rd_number or not project_name or not time_period or project_name == '项目名称':
                continue

            # 处理RD序号格式（转换为RD格式）
            try:
                if rd_number.replace('.', '').isdigit():
                    rd_num = int(float(rd_number))
                    rd_number = f"RD{rd_num:02d}"
                elif not rd_number.startswith('RD'):
                    match = re.search(r'\d+', rd_number)
                    if match:
                        rd_num = int(match.group())
                        rd_number = f"RD{rd_num:02d}"
            except (ValueError, TypeError):
                logger.warning(f"无效的RD序号格式: {rd_number}")

            # 解析起止时间 - 简化版本，专门处理 2022.01-2022.12 格式
            # 默认值也使用随机日期
            random_day = random.randint(8, 25)
            start_date = f"2024-01-{random_day:02d}"  # 默认值
            end_date = "2024-12-31"    # 默认值
            
            try:
                # 尝试匹配 YYYY.MM-YYYY.MM 格式
                pattern = r'(\d{4}\.\d{1,2})[\s]*-[\s]*(\d{4}\.\d{1,2})'
                match = re.search(pattern, time_period)
                if match:
                    start_str = match.group(1)  # 例如 "2022.01"
                    end_str = match.group(2)    # 例如 "2022.12"
                    
                    # 处理开始日期
                    start_parts = start_str.split('.')
                    if len(start_parts) == 2:
                        year, month = start_parts[0], start_parts[1]
                        # 生成8-25号之间的随机日期
                        random_day = random.randint(8, 25)
                        start_date = f"{year}-{month.zfill(2)}-{random_day:02d}"
                    
                    # 处理结束日期
                    end_parts = end_str.split('.')
                    if len(end_parts) == 2:
                        year, month = end_parts[0], end_parts[1]
                        month = month.zfill(2)
                        try:
                            last_day = calendar.monthrange(int(year), int(month))[1]
                            end_date = f"{year}-{month}-{last_day:02d}"
                        except:
                            end_date = f"{year}-{month}-31"
                    
                    logger.info(f"成功解析时间: {time_period} -> {start_date} 到 {end_date}")
                else:
                    logger.warning(f"无法解析时间格式: {time_period}")

            except Exception as e:
                logger.warning(f"解析时间失败: {time_period}, 错误: {e}")

            project_info = {
                'project_name': project_name,
                'start_date': start_date,
                'end_date': end_date,
                'rd_number': rd_number
            }
            
            project_info_list.append(project_info)
            logger.info(f"提取项目信息: {project_info}")

        logger.info(f"从RDPS表中提取到{len(project_info_list)}个项目的基本信息")

    except Exception as e:
        logger.error(f"处理RDPS表失败: {e}")

    return project_info_list

# ==============================================================================
# 函数：format_patent_achievements
# 功能：根据专利信息生成阶段性成果文本
# ==============================================================================
def format_patent_achievements(patent_info, acceptance_number):
    """
    根据专利信息生成阶段性成果文本

    Args:
        patent_info: 专利信息列表
        acceptance_number: 验收报告编号

    Returns:
        str: 格式化的阶段性成果文本
    """
    if not patent_info:
        return f"4.验收报告：{acceptance_number}"

    # 按专利类型分类
    invention_patents = []
    utility_patents = []
    pending_patents = []

    for patent in patent_info:
        patent_type = patent.get('type', '').strip()
        if '发明专利' in patent_type:
            invention_patents.append(patent)
        elif '实用新型' in patent_type:
            utility_patents.append(patent)
        elif '受理专利' in patent_type or '受理' in patent_type:
            pending_patents.append(patent)

    achievements = []
    section_num = 1

    # 1. 发明专利
    if invention_patents:
        achievements.append(f"{section_num}.获得{len(invention_patents)}项发明专利")
        for i, patent in enumerate(invention_patents, 1):
            achievements.append(f"①{patent['name']}：{patent['number']}" if i == 1 else f"②{patent['name']}：{patent['number']}")
        section_num += 1

    # 2. 实用新型专利
    if utility_patents:
        achievements.append(f"{section_num}.获得{len(utility_patents)}项实用新型专利：")
        for i, patent in enumerate(utility_patents, 1):
            symbol = "①②③④⑤⑥⑦⑧⑨⑩"[i-1] if i <= 10 else f"({i})"
            achievements.append(f"{symbol}{patent['name']}：{patent['number']}")
        section_num += 1

    # 3. 受理专利
    if pending_patents:
        achievements.append(f"{section_num}.受理{len(pending_patents)}项专利")
        for i, patent in enumerate(pending_patents, 1):
            symbol = "①②③④⑤⑥⑦⑧⑨⑩"[i-1] if i <= 10 else f"({i})"
            achievements.append(f"{symbol}{patent['name']}：{patent['number']}")
        section_num += 1

    # 4. 验收报告
    achievements.append(f"{section_num}.验收报告：{acceptance_number}")

    return "\n".join(achievements)

# ==============================================================================
# 函数：process_single_report
# 功能：处理单个报告的生成流程，包括文件处理、API调用、数据整合和保存
# ==============================================================================
def process_single_report(form_data, files_data, report_index, company_name, staff_excel, total_steps, step_counter, is_single_report=False, rdps_file=None):
    prefix = "" if is_single_report else f"_{report_index}"
    # 预期处理的技术文件字段名
    expected_file_keys = [f'file{i}{prefix}' for i in range(1, 6)]
    logger.info(f"报告 {report_index or '单个'}: 期望的技术文件字段名: {expected_file_keys}")

    # 获取上传的技术文件
    uploaded_files = [files_data.get(key) for key in expected_file_keys]
    uploaded_files = [file for file in uploaded_files if file and file.filename] # 过滤掉空文件
    logger.info(f"报告 {report_index or '单个'}: 实际上传的技术文件: {[file.filename for file in uploaded_files if file]}")

    # 获取上传的年度辅助账 Excel 文件
    excel_files = {
        '2022': files_data.get(f'excel_2022{prefix}'),
        '2023': files_data.get(f'excel_2023{prefix}'),
        '2024': files_data.get(f'excel_2024{prefix}')
    }
    excel_files = {year: file for year, file in excel_files.items() if file and file.filename} # 过滤掉空文件
    logger.info(f"报告 {report_index or '单个'}: 实际上传的辅助账文件: {list(excel_files.keys())}")

    # 获取表单中的项目基本信息
    project_name = form_data.get(f'project_name{prefix}', '').strip()
    rd_number = form_data.get(f'rd_number{prefix}', '').strip()
    # 获取自定义项目编号和验收报告编号（如果有）
    project_number = form_data.get(f'project_number{prefix}', '').strip()
    acceptance_number = form_data.get(f'acceptance_number{prefix}', '').strip()
    start_date = form_data.get(f'start_date{prefix}', '').strip()
    end_date = form_data.get(f'end_date{prefix}', '').strip()

    # 获取项目起始年份（只使用开始年份）
    start_year = start_date[:4] if start_date else ""

    # 如果没有提供自定义编号，则使用默认值
    base_number = project_number or acceptance_number

    if not base_number:
        # 如果两个编号都没有提供，使用RD序号作为基础
        project_number = rd_number
        acceptance_number = f"YS-{rd_number}"
    else:
        # 使用新的格式：前端输入的编号-项目起始年份-RD序号
        project_number = f"{base_number}-{start_year}-{rd_number}"
        # 使用新的格式：用户填入的编号-YS-RD序号
        acceptance_number = f"{base_number}-YS-{rd_number}"

    logger.info(f"报告 {report_index or '单个'}: 生成项目编号: {project_number}, 验收报告编号: {acceptance_number}")

    output_path = form_data.get('output_path', '').strip() # 输出路径是全局的

    logger.info(f"开始处理报告 {report_index or '单个'}: 项目名称='{project_name}', RD序号='{rd_number}', 文件数量={len(uploaded_files)}, 辅助账年份={list(excel_files.keys())}")

    # 基础验证
    if not uploaded_files:
        logger.error(f"报告 {report_index or '单个'}: 没有技术文件")
        return f"报告 {report_index or '单个'} ({project_name}): 没有技术文件，请至少上传一个技术文件！"
    if not all([company_name, project_name, rd_number, start_date, end_date]):
        error_msg = f"报告 {report_index or '单个'} ({project_name}): 必填字段缺失（公司名称、项目名称、RD序号、开始/结束日期）。"
        logger.error(error_msg)
        return error_msg

    # 更新进度：开始处理
    step_counter[0] += 1
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 开始处理 '{project_name}'"})

    # 处理技术文件内容提取 (使用 Kimi API)
    file_contents = []
    for i, uploaded_file in enumerate(uploaded_files, 1):
        try:
            file_content = uploaded_file.read()
            file_name = uploaded_file.filename
            logger.info(f"报告 {report_index or '单个'}: 上传文件 '{file_name}' 到 Kimi 进行内容提取")
            # --- 使用 Kimi API 提取文件内容 ---
            # 1. 上传文件
            response_upload = client.files().create(
                file=(file_name, file_content, uploaded_file.content_type),
                purpose="file-extract" # 指定目的为文件提取
            )
            file_id = response_upload.get("id")

            if not file_id:
                logger.error(f"报告 {report_index or '单个'}: 文件 '{file_name}' 上传失败，Kimi API 未返回 ID。响应: {response_upload}")
                # 可以选择跳过此文件或返回错误
                continue # 跳过此文件

            logger.info(f"报告 {report_index or '单个'}: 文件 '{file_name}' 上传成功，File ID: {file_id}")

            # 2. 轮询文件状态或直接尝试获取内容 (Kimi 可能需要时间处理)
            # Kimi file-extract 可能不需要显式轮询，直接请求 content endpoint
            file_content_url = f"{client.base_url}/files/{file_id}/content"
            headers = {"Authorization": f"Bearer {client.api_key}"}

            # 尝试获取内容，可能需要重试或等待
            max_retries = 3
            retry_delay = 5 # 秒
            for attempt in range(max_retries):
                response_content = requests.get(file_content_url, headers=headers)
                if response_content.status_code == 200:
                    extracted_text = response_content.text
                    file_contents.append(extracted_text)
                    logger.info(f"报告 {report_index or '单个'}: 成功获取文件 '{file_name}' (ID: {file_id}) 的提取内容")
                    break # 成功获取，跳出重试循环
                elif response_content.status_code == 404 and attempt < max_retries - 1:
                     logger.warning(f"报告 {report_index or '单个'}: 获取文件 '{file_name}' 内容失败 (尝试 {attempt+1}/{max_retries})，状态码 404，将在 {retry_delay} 秒后重试...")
                     time.sleep(retry_delay)
                else:
                    logger.error(f"报告 {report_index or '单个'}: 无法获取文件 '{file_name}' (ID: {file_id}) 内容，状态码: {response_content.status_code}，响应: {response_content.text}")
                    break # 最终失败，跳出重试循环
            else: # 如果循环正常结束（即重试次数耗尽）
                 logger.error(f"报告 {report_index or '单个'}: 重试多次后仍无法获取文件 '{file_name}' (ID: {file_id}) 内容，跳过此文件。")
                 continue # 跳过此文件处理

        except Exception as e:
            logger.error(f"报告 {report_index or '单个'}: 处理上传文件 '{uploaded_file.filename}' 时发生错误: {e}")
            # 根据需要决定是否中止整个报告生成
            return f"报告 {report_index or '单个'} ({project_name}): 处理技术文件 '{uploaded_file.filename}' 时出错: {e}"

        # 更新文件处理进度
        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 处理完文件 '{uploaded_file.filename}'"})

    # 合并所有提取的文件内容
    combined_content = "\n\n".join(file_contents)
    if not combined_content:
         logger.error(f"报告 {report_index or '单个'} ({project_name}): 未能从任何技术文件中提取到内容。")
         return f"报告 {report_index or '单个'} ({project_name}): 未能从任何技术文件中提取到内容。"

    # 处理辅助账 Excel 文件，提取经费概算数据
    step_counter[0] += 1
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 处理辅助账 Excel 文件"})
    budget_data = {}
    for year, excel_file in excel_files.items():
        if excel_file:
            try:
                # 确保从文件开头读取
                excel_file.seek(0)
                # 读取指定 sheet 和表头位置
                df = pd.read_excel(excel_file, sheet_name="研发费用汇总表", header=2) # 表头在第3行 (索引为2)
                logger.info(f"报告 {report_index or '单个'}: 成功读取 {year} 年辅助账 '研发费用汇总表' sheet")
                # 清理列名，转为大写以便匹配
                df.columns = df.columns.str.strip().str.upper()
                # 重命名第一列为"科目"以便引用
                df = df.rename(columns={df.columns[0]: "科目"})

                # 移除"合计"列（如果存在）
                if "合计" in [col.upper() for col in df.columns]:
                    df = df.drop(columns=[col for col in df.columns if col.upper() == "合计"])

                # 检查 RD 编号列是否存在
                rd_number_upper = rd_number.upper()
                # 查找包含 RD 编号的列名 (允许部分匹配，例如 'RD01' 或 'RD01项目')
                target_rd_col = None
                for col in df.columns:
                     if rd_number_upper == col.strip(): # 完全匹配优先
                          target_rd_col = col
                          break
                     elif rd_number_upper in col.strip(): # 包含匹配
                          target_rd_col = col
                          # 不 break，继续查找是否有更好的完全匹配

                if target_rd_col:
                    logger.info(f"报告 {report_index or '单个'}: 在 {year} 年辅助账中找到匹配列 '{target_rd_col}' (目标 RD: {rd_number_upper})")
                    mapped_data = {}
                    for _, row in df.iterrows():
                        excel_subject = row["科目"]
                        # 跳过空科目或需要忽略的科目
                        if pd.isna(excel_subject) or str(excel_subject).strip() in IGNORE_SUBJECTS:
                            continue
                        # 清理科目名称（例如移除 "其中：" 前缀）
                        excel_subject = re.sub(r'^(其中：|其中:)\s*', '', str(excel_subject).strip())

                        # 映射到标准科目名称
                        if excel_subject in SUBJECT_MAPPING:
                            standard_subject = SUBJECT_MAPPING[excel_subject]
                            amount = row[target_rd_col]
                            # 累加相同标准科目的金额 (以处理 "其中：" 的情况)
                            current_amount = mapped_data.get(standard_subject, 0)
                            mapped_data[standard_subject] = current_amount + (float(amount) if pd.notna(amount) and amount != 0 else 0)

                    budget_data[year] = mapped_data
                    logger.info(f"报告 {report_index or '单个'} - {year}年 研发费用映射: {mapped_data}")
                else:
                    logger.warning(f"报告 {report_index or '单个'} - 在 {year} 年辅助账 Excel 文件中未找到研发编号 '{rd_number_upper}' 对应的列。")
            except ValueError as e:
                logger.warning(f"报告 {report_index or '单个'} - 处理 {year} 年的 Excel 文件失败：表头或数据格式可能不正确。错误：{e}")
            except Exception as e:
                 logger.warning(f"报告 {report_index or '单个'} - 处理 {year} 年的 Excel 文件失败：{e}")

    # 提取研发人员名单
    staff_names = extract_staff_names(staff_excel, start_date, end_date)

    # 处理RDPS表，提取IP编号和专利信息
    ip_numbers = []
    patent_info = []
    if rdps_file:
        try:
            ip_mapping = extract_ip_numbers_from_rdps(rdps_file)
            logger.info(f"报告 {report_index or '单个'}: 当前项目名称: '{project_name}'")
            logger.info(f"报告 {report_index or '单个'}: RDPS表中的所有项目: {list(ip_mapping.keys())}")

            # 尝试精确匹配
            if project_name in ip_mapping:
                project_data = ip_mapping[project_name]
                ip_numbers = project_data['ip_numbers']
                patent_info = project_data['patents']
                logger.info(f"报告 {report_index or '单个'}: 精确匹配找到项目'{project_name}'的IP编号: {ip_numbers}, 专利数量: {len(patent_info)}")
            else:
                # 尝试模糊匹配
                found_match = False
                for rdps_project_name, project_data in ip_mapping.items():
                    # 检查项目名称是否包含在RDPS项目名称中，或者反之
                    if (project_name in rdps_project_name) or (rdps_project_name in project_name):
                        ip_numbers = project_data['ip_numbers']
                        patent_info = project_data['patents']
                        logger.info(f"报告 {report_index or '单个'}: 模糊匹配找到项目'{project_name}' -> RDPS项目'{rdps_project_name}'的IP编号: {ip_numbers}, 专利数量: {len(patent_info)}")
                        found_match = True
                        break

                if not found_match:
                    logger.info(f"报告 {report_index or '单个'}: 在RDPS表中未找到项目'{project_name}'的IP编号和专利信息")
        except Exception as e:
            logger.error(f"报告 {report_index or '单个'}: 处理RDPS表失败: {e}")

    # 使用 Kimi 生成报告主体内容
    try:
        report = generate_report_with_kimi(combined_content, company_name, start_date, end_date, project_name)
        report["项目编号"] = rd_number # 添加项目编号到报告数据中
        # 将处理好的经费数据放入 report 字典
        report['五、经费概算'] = {'data': budget_data, 'start_date': start_date, 'end_date': end_date}
        # 将IP编号和专利信息放入 report 字典
        report['ip_numbers'] = ip_numbers
        report['patent_info'] = patent_info
        logger.info(f"报告 {report_index or '单个'} ({project_name}): Kimi 内容生成完成")
    except Exception as e:
        logger.error(f"报告 {report_index or '单个'} ({project_name}): 调用 Kimi 生成报告内容失败: {e}")
        return f"报告 {report_index or '单个'} ({project_name}): 生成报告内容失败: {e}"

    # --- 新增：处理仪器设备清单 ---
    step_counter[0] += 1 # 为处理设备清单增加一步
    progress = int((step_counter[0] / total_steps) * 100)
    socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 处理仪器设备信息"})

    equipment_list_final = []
    if staff_excel:
        try:
            # 尝试读取 BytesIO 或文件路径 (复用之前的 staff_excel 对象或路径)
            # 确保 staff_excel 对象指针在开头
            if hasattr(staff_excel, 'seek'):
                staff_excel.seek(0) # 重置指针
                excel_data = io.BytesIO(staff_excel.read()) # 创建新的 BytesIO 对象以防万一
                excel_data.seek(0)
            elif isinstance(staff_excel, str) and os.path.exists(staff_excel):
                excel_data = staff_excel
            else:
                 # 如果 staff_excel 是 FileStorage 但之前未处理，这里处理
                 if hasattr(staff_excel, 'filename'):
                     staff_excel.seek(0)
                     excel_data = io.BytesIO(staff_excel.read())
                     excel_data.seek(0)
                 else:
                     excel_data = None
                     logger.warning("无法确定 staff_excel 的类型或它无效。")

            if excel_data:
                 # 检查 "折旧" sheet 是否存在，也接受 "折旧表" 或包含 "折旧" 的sheet名
                 xls = pd.ExcelFile(excel_data)
                 depreciation_sheet_name = None

                 # 尝试找到折旧相关的sheet
                 for sheet_name in xls.sheet_names:
                     if sheet_name == "折旧" or sheet_name == "折旧表" or "折旧" in sheet_name:
                         depreciation_sheet_name = sheet_name
                         break

                 if depreciation_sheet_name:
                     logger.info(f"报告 {report_index or '单个'}: 找到折旧相关sheet: '{depreciation_sheet_name}'")
                     df_depreciation = pd.read_excel(xls, sheet_name=depreciation_sheet_name)
                     logger.info(f"报告 {report_index or '单个'}: 成功读取 '{depreciation_sheet_name}' sheet 用于设备清单")

                     # 打印列名，帮助调试
                     logger.info(f"报告 {report_index or '单个'}: 折旧sheet列名: {list(df_depreciation.columns)}")

                     # 根据您提供的折旧sheet截图，我们需要特别处理这个表格的结构
                     # 从截图看，表格包含"产品名称"和"产品原值"等信息

                     # 打印所有列名，帮助调试
                     logger.info(f"报告 {report_index or '单个'}: 折旧sheet所有列名: {list(df_depreciation.columns)}")

                     # 检查是否是研发固定资产折旧表格式
                     # 1. 检查列名中是否包含特定关键词
                     has_rd_keywords = any('产品名称' in str(col) or '产品原值' in str(col) or '产品类别' in str(col) for col in df_depreciation.columns)

                     # 2. 检查表格标题是否包含"研发固定资产折旧表"或相关关键词
                     has_rd_title = False
                     if len(df_depreciation) > 0:
                         # 检查前几行是否包含"研发固定资产折旧表"或相关关键词
                         for i in range(min(5, len(df_depreciation))):
                             row_str = ' '.join(str(val) for val in df_depreciation.iloc[i].values)
                             if '研发固定资产折旧表' in row_str or '研发' in row_str and '折旧' in row_str:
                                 has_rd_title = True
                                 logger.info(f"报告 {report_index or '单个'}: 检测到研发固定资产折旧表标题: {row_str}")
                                 break

                     # 3. 检查表格结构是否符合研发固定资产折旧表的特征
                     # 例如，检查是否有"年折旧额"列或月度折旧列
                     has_monthly_depreciation = False
                     month_columns = ['1月折旧', '2月折旧', '3月折旧', '4月折旧', '5月折旧', '6月折旧',
                                     '7月折旧', '8月折旧', '9月折旧', '10月折旧', '11月折旧', '12月折旧']
                     for col in df_depreciation.columns:
                         col_str = str(col).lower()
                         if any(month in col_str for month in ['月折旧', '月份', '月度']):
                             has_monthly_depreciation = True
                             logger.info(f"报告 {report_index or '单个'}: 检测到月度折旧列: {col}")
                             break

                     # 综合判断是否为研发固定资产折旧表
                     is_rd_asset_table = has_rd_keywords or has_rd_title or has_monthly_depreciation

                     # 如果表格列数大于10，很可能是研发固定资产折旧表（月度折旧表通常有12个月的列）
                     if len(df_depreciation.columns) > 10:
                         is_rd_asset_table = True
                         logger.info(f"报告 {report_index or '单个'}: 根据列数判断为研发固定资产折旧表，列数: {len(df_depreciation.columns)}")

                     if is_rd_asset_table:
                         logger.info(f"报告 {report_index or '单个'}: 检测到研发固定资产折旧表格式")
                         # 对于研发固定资产折旧表，使用特定的列识别逻辑
                         # 根据截图，产品名称在第一列
                         if len(df_depreciation.columns) > 0:
                             asset_name_col = df_depreciation.columns[0]  # 直接使用第一列作为产品名称列
                             logger.info(f"报告 {report_index or '单个'}: 使用第一列作为产品名称列: {asset_name_col}")
                         else:
                             # 如果没有列，尝试其他方法
                             asset_name_col = next((col for col in df_depreciation.columns if '产品名称' in str(col)), None)
                             if not asset_name_col:
                                 asset_name_col = next((col for col in df_depreciation.columns if '产品类别' in str(col)), None)
                             if not asset_name_col:
                                 asset_name_col = None
                                 logger.warning(f"报告 {report_index or '单个'}: 无法找到产品名称列")
                     else:
                         # 原有的资产名称列识别逻辑
                         asset_name_col = next((col for col in df_depreciation.columns if '产品名称' in str(col)), None)
                         if not asset_name_col:
                             asset_name_col = next((col for col in df_depreciation.columns if '资产名称' in str(col)), None)
                         if not asset_name_col:
                             asset_name_col = next((col for col in df_depreciation.columns if '名称' in str(col)), None)
                         if not asset_name_col and len(df_depreciation.columns) > 0:
                             # 使用第一列作为资产名称列
                             asset_name_col = df_depreciation.columns[0]
                             logger.info(f"报告 {report_index or '单个'}: 未找到资产名称列，使用第一列: {asset_name_col}")

                     # 打印所有列名，帮助调试
                     logger.info(f"报告 {report_index or '单个'}: 折旧sheet所有列名: {list(df_depreciation.columns)}")

                     # 尝试匹配"产品原值"列，如果没有，则尝试使用"资产原值"或包含"原值"的列
                     asset_value_col = None

                     if is_rd_asset_table:
                         logger.info(f"报告 {report_index or '单个'}: 处理研发固定资产折旧表的资产原值列")

                         # 1. 检查是否有"产品原值"列
                         asset_value_col = next((col for col in df_depreciation.columns if '产品原值' in str(col)), None)
                         if asset_value_col:
                             logger.info(f"报告 {report_index or '单个'}: 找到产品原值列: {asset_value_col}")

                         # 2. 如果没有，检查是否有包含"原值"的列
                         if not asset_value_col:
                             for col in df_depreciation.columns:
                                 if '原值' in str(col):
                                     asset_value_col = col
                                     logger.info(f"报告 {report_index or '单个'}: 找到包含'原值'的列: {asset_value_col}")
                                     break

                         # 3. 根据截图，资产原值在第四列，如果前面方法没找到，直接使用第四列
                         if not asset_value_col and len(df_depreciation.columns) > 3:
                             asset_value_col = df_depreciation.columns[3]  # 索引从0开始，第四列索引为3
                             logger.info(f"报告 {report_index or '单个'}: 未找到原值列，使用第四列(产品原值): {asset_value_col}")
                         # 如果表格列数不足4列，尝试使用第三列
                         elif not asset_value_col and len(df_depreciation.columns) > 2:
                             asset_value_col = df_depreciation.columns[2]
                             logger.info(f"报告 {report_index or '单个'}: 列数不足4列，使用第三列: {asset_value_col}")

                         # 4. 如果表格中有"产品原值"列，但可能不在前几列，尝试查找
                         if not asset_value_col:
                             # 检查每一行的数据，寻找可能包含数值的列
                             for col in df_depreciation.columns:
                                 try:
                                     # 尝试将列转换为数值
                                     numeric_values = pd.to_numeric(df_depreciation[col], errors='coerce')
                                     # 如果有超过50%的值可以转为数值，且平均值大于1000，可能是原值列
                                     if numeric_values.notna().mean() > 0.5 and numeric_values.mean() > 1000:
                                         asset_value_col = col
                                         logger.info(f"报告 {report_index or '单个'}: 找到可能的原值列(数值较大): {asset_value_col}")
                                         break
                                 except Exception as e:
                                     logger.warning(f"报告 {report_index or '单个'}: 检查列 {col} 是否为数值时出错: {e}")
                     else:
                         # 原有的资产原值列识别逻辑
                         # 1. 精确匹配常见列名
                         value_column_names = ['产品原值', '资产原值', '原值', '设备原值', '固定资产原值']
                         for col_name in value_column_names:
                             for col in df_depreciation.columns:
                                 if col_name in str(col):
                                     asset_value_col = col
                                     logger.info(f"报告 {report_index or '单个'}: 找到资产原值列 '{col}' (包含 '{col_name}')")
                                     break
                             if asset_value_col:
                                 break

                         # 2. 如果没找到，尝试模糊匹配包含"原值"的列
                         if not asset_value_col:
                             for col in df_depreciation.columns:
                                 if '原值' in str(col).lower():
                                     asset_value_col = col
                                     logger.info(f"报告 {report_index or '单个'}: 找到资产原值列 '{col}' (包含 '原值')")
                                     break

                         # 3. 如果仍然没找到，尝试查找数值类型的列（排除第一列，通常是名称列）
                         if not asset_value_col and len(df_depreciation.columns) > 1:
                             # 检查每一列的数据类型，寻找数值类型的列
                             for col in df_depreciation.columns[1:]:  # 跳过第一列
                                 # 尝试将列转换为数值类型，看是否大部分能成功转换
                                 try:
                                     # 检查非空值中能转为数值的比例
                                     non_na_values = df_depreciation[col].dropna()
                                     if len(non_na_values) > 0:
                                         numeric_count = sum(pd.to_numeric(non_na_values, errors='coerce').notna())
                                         if numeric_count / len(non_na_values) > 0.5:  # 如果超过50%的值可以转为数值
                                             asset_value_col = col
                                             logger.info(f"报告 {report_index or '单个'}: 找到可能的资产原值列 '{col}' (数值类型列)")
                                             break
                                 except Exception as e:
                                     logger.warning(f"报告 {report_index or '单个'}: 检查列 '{col}' 是否为数值类型时出错: {e}")

                         # 4. 如果还是没找到，使用第三列（如果存在）
                         if not asset_value_col and len(df_depreciation.columns) > 2:
                             asset_value_col = df_depreciation.columns[2]
                             logger.info(f"报告 {report_index or '单个'}: 未找到资产原值列，使用第三列: {asset_value_col}")

                     logger.info(f"报告 {report_index or '单个'}: 使用的列名 - 资产名称: {asset_name_col}, 资产原值: {asset_value_col}")

                     if asset_name_col and asset_value_col:
                         # 提取数据并去重
                         df_depreciation_valid = df_depreciation.dropna(subset=[asset_name_col])

                         # 打印前几行数据，帮助调试
                         logger.info(f"报告 {report_index or '单个'}: 折旧sheet前5行数据: {df_depreciation_valid.head(5).to_dict('records')}")

                         # 处理资产名称列，提取名称部分（如果包含"|数量"格式或其他格式）
                         def extract_asset_name(name_str):
                             if pd.isna(name_str):
                                 return ""
                             name_str = str(name_str).strip()
                             # 如果包含"|", 则提取前面的部分作为名称
                             if "|" in name_str:
                                 return name_str.split("|")[0].strip()
                             # 处理可能的其他格式
                             if "（" in name_str and "）" in name_str:
                                 return name_str.split("（")[0].strip()
                             if "(" in name_str and ")" in name_str:
                                 return name_str.split("(")[0].strip()
                             return name_str

                         # 创建一个新列用于存储处理后的资产名称
                         df_depreciation_valid['处理后资产名称'] = df_depreciation_valid[asset_name_col].apply(extract_asset_name)

                         # 处理资产原值列，确保是数值
                         def clean_value(value):
                             if pd.isna(value):
                                 return 0
                             if isinstance(value, str):
                                 # 移除可能的非数字字符
                                 value = ''.join(c for c in value if c.isdigit() or c == '.' or c == '-')
                                 try:
                                     return float(value)
                                 except:
                                     return 0
                             try:
                                 # 尝试直接转换为浮点数
                                 return float(value)
                             except (ValueError, TypeError):
                                 return 0

                         # 创建一个新列用于存储处理后的资产原值（转换为万元）
                         def convert_to_wan(value):
                             # 先清理值
                             cleaned_value = clean_value(value)
                             # 转换为万元（除以10000）
                             return cleaned_value / 10000.0

                         # 应用转换函数
                         df_depreciation_valid['处理后资产原值'] = df_depreciation_valid[asset_value_col].apply(convert_to_wan)

                         # 打印处理后的原值，帮助调试
                         logger.info(f"报告 {report_index or '单个'}: 处理后的资产原值示例: {df_depreciation_valid['处理后资产原值'].head(5).tolist()}")

                         # 过滤掉可能的表头行、无效行、公司名称行和合计行
                         if is_rd_asset_table:
                             # 对于研发固定资产折旧表，使用特定的过滤逻辑
                             df_depreciation_valid = df_depreciation_valid[
                                 # 排除表头、合计行和空行
                                 ~df_depreciation_valid['处理后资产名称'].astype(str).str.contains('合计|小计|总计|产品名称|产品类别|序号', case=False) &
                                 # 排除空值
                                 (df_depreciation_valid['处理后资产名称'].astype(str).str.len() > 0) &
                                 # 确保原值列有数值
                                 (df_depreciation_valid['处理后资产原值'] > 0)
                             ]
                             # 打印过滤后的行数
                             logger.info(f"报告 {report_index or '单个'}: 研发固定资产折旧表过滤后剩余 {len(df_depreciation_valid)} 行")
                         else:
                             # 原有的过滤逻辑
                             df_depreciation_valid = df_depreciation_valid[
                                 ~df_depreciation_valid['处理后资产名称'].astype(str).str.contains('名称|产品|资产|编号|序号|公司|合计', case=False) &
                                 (df_depreciation_valid['处理后资产名称'].astype(str).str.len() > 0)
                             ]

                         # 使用处理后的资产名称去重，并确保正确映射资产原值
                         equipment_data = df_depreciation_valid[['处理后资产名称', '处理后资产原值']].drop_duplicates(subset=['处理后资产名称'], keep='first').to_dict('records')

                         # 打印提取的设备数据，帮助调试
                         logger.info(f"报告 {report_index or '单个'}: 提取的设备数据(前5条): {equipment_data[:5] if len(equipment_data) > 5 else equipment_data}")

                         # 记录提取到的设备数据
                         logger.info(f"报告 {report_index or '单个'}: 提取到的设备数据: {equipment_data}")
                         logger.info(f"报告 {report_index or '单个'}: 从折旧表提取到 {len(equipment_data)} 条独特的设备数据")

                         # 为每个设备生成作用描述
                         for idx, equip in enumerate(equipment_data):
                             equip_name = str(equip['处理后资产名称']).strip()

                             # 使用处理后的资产原值
                             equip_value = equip.get('处理后资产原值', 0)

                             # 将原值格式化为字符串，处理 NaN，确保显示两位小数
                             # 注意：原值已经转换为万元，直接格式化即可
                             try:
                                 equip_value_float = float(equip_value)
                                 equip_value_str = f"{equip_value_float:.2f}"
                             except (ValueError, TypeError):
                                 equip_value_str = "0.00" if pd.isna(equip_value) else f"{0:.2f}"

                             logger.info(f"设备 '{equip_name}' 的原值: {equip_value} -> 格式化为: {equip_value_str}")

                             if not equip_name: # 跳过无效名称
                                 continue

                             # 更新进度：为每个设备调用 API
                             step_counter[0] += 1 # 每次API调用算一步
                             # 智能进度计算：确保不超过95%，为后续步骤留出空间
                             progress = min(int((step_counter[0] / total_steps) * 95), 95)
                             socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 生成设备 '{equip_name[:10]}...' 的作用描述"})

                             # 检查缓存，避免重复API调用
                             cache_key = equip_name.strip().lower()
                             if cache_key in EQUIPMENT_DESCRIPTION_CACHE:
                                 description = EQUIPMENT_DESCRIPTION_CACHE[cache_key]
                                 logger.info(f"设备 '{equip_name}' 使用缓存描述: {description}")
                             else:
                                 description = "作用待补充" # 默认值
                                 try:
                                     # 修改提示，要求生成设备作用描述，不包含"用于"、"应用于"等词语，也不要包含设备名称本身：{equip_name}"
                                     prompt = f"为以下设备生成一个不超过15字的简要功能描述，说明该设备在研发中的具体作用，不要包含'用于'、'应用于'等词语，也不要包含设备名称本身：{equip_name}"
                                     response = client.chat().create_completion(
                                         model="moonshot-v1-8k", # 选用 8k 模型
                                         messages=[
                                             {"role": "system", "content": "你是 Kimi AI 助手，请生成简洁的技术描述。"},
                                             {"role": "user", "content": prompt}
                                         ],
                                         temperature=0.2
                                     )
                                     if response and response.get('choices') and response['choices'][0].get('message'):
                                         description = response['choices'][0]['message'].get('content', '').strip()
                                         description = description.replace('"', '').replace("'", "").replace("`", "") # 清理
                                         description = description[:15] # 截断
                                         # 保存到缓存
                                         EQUIPMENT_DESCRIPTION_CACHE[cache_key] = description
                                         logger.info(f"设备 '{equip_name}' 的作用描述: {description} (已缓存)")
                                     else:
                                         logger.warning(f"为设备 '{equip_name}' 生成描述时 Kimi API 未返回有效响应: {response}")
                                         # 使用默认描述，不包含"用于项目"的文字
                                         description = "研发测试分析"
                                         # 也缓存默认描述
                                         EQUIPMENT_DESCRIPTION_CACHE[cache_key] = description
                                         logger.info(f"为设备 '{equip_name}' 使用默认描述: {description} (已缓存)")

                                 except Exception as api_err:
                                     logger.error(f"为设备 '{equip_name}' 生成描述时出错: {api_err}")
                                     # 使用默认描述，不包含"用于项目"的文字
                                     description = "研发测试分析"
                                     # 也缓存默认描述
                                     EQUIPMENT_DESCRIPTION_CACHE[cache_key] = description
                                     logger.info(f"为设备 '{equip_name}' 使用默认描述: {description} (已缓存)")

                             # 添加到最终设备列表，确保原值正确
                             equipment_list_final.append({
                                 "name": equip_name,
                                 "value": equip_value_str,  # 使用处理后的原值字符串
                                 "description": description,
                                 "index": idx + 1  # 添加序号，从1开始
                             })

                             # 记录添加的设备信息
                             logger.info(f"添加设备到最终列表: 名称='{equip_name}', 原值='{equip_value_str}', 描述='{description}'")
                             time.sleep(0.3) # 增加 API 调用间隔

                     else:
                         logger.warning(f"报告 {report_index or '单个'}: '折旧' sheet 中未同时找到包含 '资产名称' 和 '资产原值' 的列")
                         # 添加默认设备数据，描述不包含"用于项目"的文字
                         equipment_list_final = [
                             {"name": "电脑", "value": "8000.00", "description": "数据处理计算", "index": 1},
                             {"name": "测试设备", "value": "12000.00", "description": "性能参数测试", "index": 2},
                             {"name": "开发工具", "value": "5000.00", "description": "软件程序开发", "index": 3}
                         ]
                         logger.info(f"报告 {report_index or '单个'}: 使用默认设备数据: {equipment_list_final}")
                 else:
                     logger.warning(f"报告 {report_index or '单个'}: 在 staff_excel 文件中未找到名为 '折旧' 的 sheet")
                     # 添加默认设备数据，描述不包含"用于项目"的文字
                     equipment_list_final = [
                         {"name": "电脑", "value": "8000.00", "description": "数据处理计算", "index": 1},
                         {"name": "测试设备", "value": "12000.00", "description": "性能参数测试", "index": 2},
                         {"name": "开发工具", "value": "5000.00", "description": "软件程序开发", "index": 3}
                     ]
                     logger.info(f"报告 {report_index or '单个'}: 使用默认设备数据: {equipment_list_final}")
        except Exception as e:
            logger.error(f"报告 {report_index or '单个'}: 处理 '折旧' sheet 时发生意外错误: {e}")
            # 添加默认设备数据，描述不包含"用于项目"的文字
            equipment_list_final = [
                {"name": "电脑", "value": "8000.00", "description": "数据处理计算", "index": 1},
                {"name": "测试设备", "value": "12000.00", "description": "性能参数测试", "index": 2},
                {"name": "开发工具", "value": "5000.00", "description": "软件程序开发", "index": 3}
            ]
            logger.info(f"报告 {report_index or '单个'}: 使用默认设备数据: {equipment_list_final}")

    # --- 设备清单处理结束 ---

    # 直接使用项目报告中"总计"行显示的数据，不进行任何计算
    # 优先使用存储的grand_total（项目报告"总计"行的实际显示值）
    total_budget = report.get('五、经费概算', {}).get('grand_total', 0)
    
    # 如果没有grand_total，则使用fallback逻辑
    if total_budget == 0:
        adjusted_total = report.get('五、经费概算', {}).get('adjusted_total', 0.0)
        if adjusted_total > 0:
            total_budget = int(adjusted_total)
        else:
            # 最后fallback：使用原始数据
            total_budget = 0.0
            for year_data in budget_data.values():
                total_budget += sum(amount for subject, amount in year_data.items() if subject != "合计")
            total_budget = int(total_budget)

    # 准备输出文件路径
    # 确保 output_path 存在
    if output_path and not os.path.isdir(output_path):
        try:
            os.makedirs(output_path, exist_ok=True)
            logger.info(f"创建输出目录: {output_path}")
        except OSError as e:
            logger.error(f"无法创建输出目录 {output_path}: {e}。将在当前目录保存文件。")
            output_path = "" # 如果无法创建，则退回到当前目录

    # 构建默认文件名 - 按照格式：RD序号-项目名称-公司名称-项目计划书-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_rd_number = clean_filename(rd_number)
    cleaned_project_name = clean_filename(project_name)
    cleaned_company_name = clean_filename(company_name)

    report_filename = f"{cleaned_rd_number}-{cleaned_project_name}-{cleaned_company_name}-项目计划书-{timestamp}.docx"

    if output_path:
        output_file_path = os.path.join(output_path, report_filename)
    else:
        output_file_path = report_filename # 保存到当前运行目录

    # 更新进度：保存项目计划书
    step_counter[0] += 1
    progress = min(int((step_counter[0] / total_steps) * 98), 98)  # 为最后步骤留出空间
    socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 保存 '{project_name}' 的项目计划书"})

    # 调用保存函数，传入设备列表和自定义项目编号
    try:
        save_report_to_docx(report, output_file_path, company_name, start_date, end_date, project_name, rd_number, staff_names, equipment_list_final, project_number) # 传递 equipment_list_final 和 project_number
        logger.info(f"报告 {report_index or '单个'} ({project_name}): 项目计划书已保存至 {output_file_path}")
    except Exception as e:
         logger.error(f"报告 {report_index or '单个'} ({project_name}): 保存项目计划书失败: {e}")
         return f"报告 {report_index or '单个'} ({project_name}): 保存项目计划书失败: {e}"

    # 更新进度：保存验收报告
    step_counter[0] += 1
    progress = min(int((step_counter[0] / total_steps) * 99), 99)  # 最后一个主要步骤
    socketio.emit('progress', {'progress': progress, 'message': f"处理报告 {report_index or '单个'}: 保存 '{project_name}' 的验收报告"})

    try:
        # 注意：save_acceptance_report_to_docx 可能也需要更新以使用 output_file_path 的基础名
        # 确保验收报告文件名基于 output_file_path
        acceptance_file_path = save_acceptance_report_to_docx(report, output_file_path, company_name, start_date, end_date, project_name, rd_number, staff_names, acceptance_number)
        logger.info(f"报告 {report_index or '单个'} ({project_name}): 验收报告已保存至 {acceptance_file_path}")
    except Exception as e:
        logger.error(f"报告 {report_index or '单个'} ({project_name}): 保存验收报告失败: {e}")
        # 即使验收报告失败，也尝试返回成功信息，但记录错误
        acceptance_file_path = f"保存失败: {e}" # 在结果中指示错误

    # 获取文件名（不包含路径）
    report_filename = os.path.basename(output_file_path)
    acceptance_filename = os.path.basename(acceptance_file_path)

    # 最终返回结果
    return {
        'result': f"项目报告: {output_file_path}\n验收报告: {acceptance_file_path}",
        'report_file_path': output_file_path,
        'acceptance_file_path': acceptance_file_path,
        'files': {
            report_filename: output_file_path,
            acceptance_filename: acceptance_file_path
        },
        'project_info': {
            'year': start_date[:4],
            'project_name': project_name,
            'rd_number': rd_number,
            'project_number': project_number,  # 添加自定义项目编号
            'acceptance_number': acceptance_number,  # 添加自定义验收报告编号
            'start_date': start_date,
            'end_date': end_date,
            'total_budget': total_budget,
            'report': report, # 包含经费和可能的其他数据
            'staff_names': staff_names,
            'equipment_list': equipment_list_final, # 添加设备列表信息
            'ip_numbers': ip_numbers,  # 添加IP编号信息
            'patent_info': patent_info  # 添加专利信息
        }
    }
# ==============================================================================
# 函数：process_single_report 结束
# ==============================================================================

def save_rd_ps_table(projects, company_name, output_path):
    from docx import Document
    from docx.shared import Pt, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_ALIGN_VERTICAL
    from docx.oxml.ns import qn # 确保 qn 已导入
    from docx.table import _Cell
    from docx.oxml import OxmlElement # 确保 OxmlElement 已导入
    import os
    from datetime import datetime
    import logging
    import re

    logger = logging.getLogger(__name__)
    logger.info(f"开始生成 RD_PS 表，输出路径: {output_path}")

    doc = Document()

    # 设置页面边距，确保内容适应 A4 页面
    for section in doc.sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

    for project_idx, project in enumerate(projects):
        project_name = project['project_name']
        rd_number = project['rd_number']
        start_date = project['start_date']
        end_date = project['end_date']
        staff_names = project['staff_names']
        report = project['report']
        budget_data = report.get('五、经费概算', {}).get('data', {})

        # 计算研发人员数量
        researcher_count = len(staff_names) if staff_names else 0

        # 使用调整后的预算数据计算近3年总支出
        adjusted_total = report.get('五、经费概算', {}).get('adjusted_total', 0.0)
        original_total = report.get('五、经费概算', {}).get('original_total', 0.0)
        
        if adjusted_total > 0:
            # 使用调整后的预算数据
            total_budget = adjusted_total
        else:
            # 兼容旧数据格式，计算原始数据
            total_budget = 0.0
            for year, subjects in budget_data.items():
                year_total = sum(float(amount) for subject, amount in subjects.items() if subject != "合计" and amount)
                total_budget += year_total
        
        # 获取原始数据用于显示经费使用情况
        if original_total == 0.0:
            # 兼容旧数据格式，计算原始数据  
            for year, subjects in budget_data.items():
                year_total = sum(float(amount) for subject, amount in subjects.items() if subject != "合计" and amount)
                original_total += year_total
        
        # 获取每年的原始经费数据
        budget_2022 = sum(float(amount) for subject, amount in budget_data.get('2022', {}).items() if amount and subject != "合计") if '2022' in budget_data else 0
        budget_2023 = sum(float(amount) for subject, amount in budget_data.get('2023', {}).items() if amount and subject != "合计") if '2023' in budget_data else 0
        budget_2024 = sum(float(amount) for subject, amount in budget_data.get('2024', {}).items() if amount and subject != "合计") if '2024' in budget_data else 0

        # 获取立项目的
        purpose = ""
        if '2. 项目开发的目的和意义' in report:
            purpose_data = report['2. 项目开发的目的和意义']
            # 检查是否为列表以及列表是否非空
            if isinstance(purpose_data, list) and purpose_data:
                 purpose = purpose_data[0].strip() # 获取列表的第一个元素
            else:
                 # 否则按原逻辑处理（假设是字符串）
                 purpose_content = str(purpose_data).split('意义')[0].strip()
                 # 移除可能存在的"目的"标题
                 purpose = re.sub(r'^.*?目的[：:]\s*', '', purpose_content).strip()
                 # 如果移除后为空，则使用原内容
                 if not purpose:
                     purpose = purpose_content


        # 组织实施方式
        org_implementation = (
            "根据公司年度新产品开发计划和技术任务书组织实施，独立自主研究，通过技术创新成立了"
            f"'{project_name}'专业的研发小组，配备了{researcher_count}名研发人员，"
            f"预算{int(total_budget) if total_budget > 0 else 0}万元组织实施本项目的研究开发。"
        )
        purpose_and_org = f"1.立项目的：{purpose}\n\n2.组织实施方式：{org_implementation}" if purpose else "1.立项目的：未提供项目开发目的\n\n2.组织实施方式：未提供组织实施方式"

        # 核心技术及创新点（调用 trim_content_for_rdrps 精简内容）
        key_tech = report.get('2. 关键技术', '').strip() or "未找到关键技术"
        innovation = report.get('3. 创新点', '').strip() or "未找到创新点"
        # 注意：确保 trim_content_for_rdrps 返回的是带序号的格式
        trimmed_key_tech, trimmed_innovation = trim_content_for_rdrps(key_tech, innovation, project_name)

        # 直接使用 trim_content_for_rdrps 返回的带序号格式
        # 注意：确保 trim_content_for_rdrps 返回的字符串包含正确的换行符 (\n\n)
        tech_and_innov = f"1.关键技术：\n{trimmed_key_tech}\n\n2.创新点：\n{trimmed_innovation}"

        # 添加 RD 序号
        rd_paragraph = doc.add_paragraph()
        rd_run = rd_paragraph.add_run(rd_number)
        rd_run.font.name = '黑体'
        rd_run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        rd_run.font.size = Pt(14)
        rd_run.bold = True

        # 创建表格（增加一行用于IP编号）
        table = doc.add_table(rows=14, cols=2)
        table.autofit = False
        table.columns[0].width = Cm(3.6)
        table.columns[1].width = Cm(11.6)

        # 设置行高 0.5cm
        for row in table.rows:
            row.height = Cm(0.5)

        # 设置表格为实体线条
        for row in table.rows:
            for cell in row.cells:
                tc = cell._element
                tcPr = tc.get_or_add_tcPr()
                tcBorders = OxmlElement('w:tcBorders')
                for border_name in ['top', 'left', 'bottom', 'right']:
                    border = OxmlElement(f'w:{border_name}')
                    border.set(qn('w:val'), 'single')
                    border.set(qn('w:sz'), '4')
                    border.set(qn('w:space'), '0')
                    border.set(qn('w:color'), '000000')
                    tcBorders.append(border)
                tcPr.append(tcBorders)

        # 获取IP编号信息
        ip_numbers_list = project.get('ip_numbers', [])
        # 对IP编号进行排序（如果都是数字的话）
        if ip_numbers_list:
            try:
                # 尝试将IP编号转换为数字进行排序
                sorted_ips = sorted(ip_numbers_list, key=lambda x: int(x) if x.isdigit() else float('inf'))
                ip_numbers_text = ",".join(sorted_ips)
            except (ValueError, TypeError):
                # 如果无法转换为数字，则按字符串排序
                ip_numbers_text = ",".join(sorted(ip_numbers_list))
        else:
            ip_numbers_text = ""

        # 填充表格内容
        table.cell(0, 0).text = "项目名称"
        table.cell(0, 1).text = project_name
        table.cell(1, 0).text = "起止时间"
        table.cell(1, 1).text = f"{start_date}至{end_date}"
        table.cell(2, 0).text = "技术领域"
        table.cell(2, 1).text = ""
        table.cell(3, 0).text = "技术来源"
        table.cell(3, 1).text = "企业自有技术"
        table.cell(4, 0).text = "IP编号"  # 新增的IP编号行
        table.cell(4, 1).text = ip_numbers_text
        table.cell(5, 0).text = "本项目研发人员数"  # 原第4行，现在是第5行
        table.cell(5, 1).text = str(researcher_count)
        table.cell(6, 0).text = "研发经费总预算"  # 原第5行，现在是第6行
        table.cell(6, 1).text = f"{int(total_budget)}" if total_budget > 0 else ""
        table.cell(7, 0).text = "研发经费近3年总支出"  # 原第6行，现在是第7行
        table.cell(7, 1).text = f"{original_total:.2f}" if original_total > 0 else ""
        table.cell(8, 0).text = "第一年2022年经费"  # 原第7行，现在是第8行
        table.cell(8, 1).text = f"{budget_2022:.2f}" if budget_2022 > 0 else ""
        table.cell(9, 0).text = "第二年2023年经费"  # 原第8行，现在是第9行
        table.cell(9, 1).text = f"{budget_2023:.2f}" if budget_2023 > 0 else ""
        table.cell(10, 0).text = "第三年2024年经费"  # 原第9行，现在是第10行
        table.cell(10, 1).text = f"{budget_2024:.2f}" if budget_2024 > 0 else ""
        table.cell(11, 0).text = "立项目的及组织实施方式"  # 原第10行，现在是第11行
        table.cell(11, 1).text = purpose_and_org
        table.cell(12, 0).text = "核心技术及创新点"  # 原第11行，现在是第12行
        # 将修改后的 tech_and_innov 填入
        table.cell(12, 1).text = tech_and_innov
        table.cell(13, 0).text = "取得的阶段性成果"  # 原第12行，现在是第13行

        # 生成阶段性成果内容
        patent_info = project.get('patent_info', [])
        acceptance_number = project.get('acceptance_number', '')
        achievements_text = format_patent_achievements(patent_info, acceptance_number)
        table.cell(13, 1).text = achievements_text

        # 设置表格字体为宋体 8 号，垂直居中
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                # 对于多行内容的单元格（如核心技术及创新点），保持默认的顶部对齐可能更好
                if row_idx == 12 and cell_idx == 1: # 核心技术及创新点内容单元格（原第11行，现在是第12行）
                     cell.vertical_alignment = WD_ALIGN_VERTICAL.TOP
                elif row_idx == 11 and cell_idx == 1: # 立项目的及组织实施方式内容单元格（原第10行，现在是第11行）
                     cell.vertical_alignment = WD_ALIGN_VERTICAL.TOP

                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    paragraph.paragraph_format.space_before = Pt(0)
                    paragraph.paragraph_format.space_after = Pt(0)
                    paragraph.paragraph_format.line_spacing = 1 # 设置为单倍行距
                    # --- 移除可能导致问题的行 ---
                    # paragraph.paragraph_format.keep_together = True
                    # paragraph.paragraph_format.keep_with_next = True
                    # --- 移除结束 ---
                    for run in paragraph.runs:
                        run.font.name = '宋体'
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                        run.font.size = Pt(8) # 确保字体大小为8号

        # 调整包含多行内容的单元格行高 (例如，核心技术及创新点)
        table.rows[11].height = Cm(1.5) # 调整立项目的行高（原第10行，现在是第11行）
        table.rows[12].height = Cm(3.0) # 调整核心技术及创新点行高（原第11行，现在是第12行）

        # --- 修正错误的代码 ---
        # 尝试阻止行跨页 (访问行属性的正确方式)
        # 注意：即使这样设置，Word 的分页行为也可能不完全符合预期
        for row in table.rows:
             try:
                 trPr = row._tr.get_or_add_trPr() # 获取或添加行属性
                 cantSplit = OxmlElement('w:cantSplit')
                 trPr.append(cantSplit)
                 # 同样可以尝试设置单元格的段落属性来防止分页
                 for cell in row.cells:
                     for paragraph in cell.paragraphs:
                         pPr = paragraph._p.get_or_add_pPr()
                         pPr.keepNext_val = True # 尝试与下一段保持在一起
                         pPr.keepLines_val = True # 尝试保持段落行在一起
             except Exception as e_split:
                 logger.warning(f"设置表格行不跨页属性时出错: {e_split}")
        # --- 修正结束 ---

        # 添加分页符 (如果不是最后一个项目)
        if project_idx < len(projects) - 1:
            doc.add_page_break()

    # 保存文档 - 按照新格式：RD_PS表-公司名称-时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    # 清理文件名中的非法字符
    def clean_filename(name):
        return re.sub(r'[<>:"/\\|?*]', '_', name.strip())

    cleaned_company_name = clean_filename(company_name)

    output_filename = f"RD_PS表-{cleaned_company_name}-{timestamp}.docx"
    if output_path and os.path.isdir(output_path):
        output_file_path = os.path.join(output_path, output_filename)
    else:
        # 如果 output_path 为空，直接使用文件名保存在当前目录
        output_file_path = output_filename

    try:
        # 如果提供了输出路径，确保目录存在
        if output_path and os.path.isdir(output_path):
            os.makedirs(output_path, exist_ok=True)
        elif output_path: # 如果提供了 output_path 但它不是一个有效目录
             logger.warning(f"提供的输出路径 '{output_path}' 不是有效目录，将在当前目录保存文件。")
             output_file_path = output_filename # 退回当前目录

        doc.save(output_file_path)
        logger.info(f"RD_PS 表保存成功: {output_file_path}")
    except PermissionError as e:
        logger.error(f"保存 RD_PS 表失败: {e}")
        raise PermissionError(f"保存 RD_PS 表失败: {e}，请检查目录权限或确保文件未被占用")
    except Exception as e:
        logger.error(f"保存 RD_PS 表失败: {e}")
        raise

    return output_file_path

# 登录检查装饰器
def login_required(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

@app.after_request
def add_header(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    return response

@app.route('/logout')
def logout():
    """用户退出登录"""
    try:
        username = session.get('username')
        session_id = session.get('session_id')
        
        # 清理会话记录
        if session_id and session_id in active_sessions:
            del active_sessions[session_id]
            logger.info(f"用户 {username} 退出登录，已清理会话: {session_id[:8]}...")
        
        # 更新用户状态为离线（只有在确实存在时才更新）
        if username:
            try:
                users = load_users()
                if username in users:
                    users[username]['status'] = 'offline'
                    save_users(users)
            except Exception as e:
                logger.warning(f"更新用户 {username} 状态失败: {e}")
    
    except Exception as e:
        logger.error(f"退出登录时发生错误: {e}")
    finally:
        # 无论如何都要清理会话
        session.clear()
    
    response = make_response(redirect(url_for('login')))
    response.delete_cookie('session')
    return response

def is_local_ip(ip):
    """
    判断是否是本地IP地址

    Args:
        ip: IP地址

    Returns:
        bool: 是否是本地IP
    """
    # 如果配置中禁用了本地保存功能，直接返回 False
    if not app.config.get('ENABLE_LOCAL_SAVE', True):
        logger.info("本地保存功能已禁用")
        return False

    # 本地IP地址列表
    local_ips = ['127.0.0.1', '::1', 'localhost']

    # 检查是否是本地IP
    if ip in local_ips:
        logger.info(f"IP {ip} 是本地IP地址")
        return True

    # 检查是否是本地网段
    if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.16.'):
        logger.info(f"IP {ip} 是本地网段IP地址")
        return True

    # 由于IPv6地址的复杂性，我们将所有IPv6地址视为本地地址
    if ':' in ip:
        logger.info(f"IP {ip} 是IPv6地址，视为本地IP")
        return True

    logger.info(f"IP {ip} 不是本地IP地址")
    return False

@app.route('/upload', methods=['POST'])
@login_required
def upload():
    username = session.get('username')
    
    # 检查用户权限
    if not check_user_permission(username, 'single'):
        log_user_error(username, '单项报告生成', '没有单项报告生成权限', request.remote_addr)
        return jsonify({'success': False, 'message': '您没有单项报告生成权限'}), 403
    
    # 检查用户额度
    quota_info = check_user_quota(username)
    if not quota_info['has_quota']:
        log_user_error(username, '单项报告生成', '报告额度不足', request.remote_addr)
        return jsonify({'success': False, 'message': f'报告额度不足，剩余额度：{quota_info["remaining"]}'}), 403
    
    form_data = request.form.to_dict()
    files_data = request.files
    company_name = form_data.get('company_name', '').strip()
    staff_excel = files_data.get('staff_excel')
    rdps_file = files_data.get('rdps_file')  # 获取RDPS表文件

    # 获取用户指定的保存路径
    user_output_path = form_data.get('output_path', '').strip()

    # 获取客户端IP地址
    client_ip = request.remote_addr
    is_local = is_local_ip(client_ip)

    # 创建临时目录用于存储生成的文件
    temp_dir = tempfile.mkdtemp()
    try:
        # 使用临时目录作为输出路径
        form_data['output_path'] = temp_dir

        # 动态计算总步骤数：基础步骤 + 额外文件生成步骤
        # 基础步骤：开始处理(1) + 技术文件处理(5) + 辅助账处理(1) + 生成报告(1) + 设备处理(1) + 保存计划书(1) + 保存验收报告(1) = 11步
        # 额外步骤：RD_PS表(1) + 立项通知(1) + 研发活动一览表(1) = 3步
        # 设备API调用步骤：预估最多10个设备
        total_steps = 11 + 3 + 10  # 基础11步 + 额外3步 + 预估设备10步 = 24步
        step_counter = [0]

        try:
            result = process_single_report(form_data, files_data, None, company_name, staff_excel, total_steps, step_counter, is_single_report=True, rdps_file=rdps_file)
            if isinstance(result, str):
                # 记录处理失败的错误
                log_user_error(username, '单项报告生成', result, request.remote_addr)
                return result, 400
        except Exception as e:
            # 记录详细的API错误信息
            log_user_error(username, '单项报告生成', f'报告生成失败: {str(e)[:200]}', request.remote_addr, original_error=e)
            return jsonify({'success': False, 'message': f'报告生成失败: {str(e)[:100]}'}), 500

        # 收集生成的文件
        files_to_zip = {}
        saved_files = []
        if isinstance(result, dict) and 'files' in result:
            for file_name, file_path in result['files'].items():
                if os.path.exists(file_path):
                    base_filename = os.path.basename(file_path)

                    # 确保文件名唯一性 - 如果文件名已存在，添加序号
                    unique_filename = base_filename
                    counter = 1
                    while unique_filename in files_to_zip:
                        name, ext = os.path.splitext(base_filename)
                        unique_filename = f"{name}-副本{counter}{ext}"
                        counter += 1

                    files_to_zip[unique_filename] = file_path
                    logger.debug(f"添加文件: {unique_filename}")

            # 添加 RD_PS 表
            if 'project_info' in result and 'rd_number' in result['project_info']:
                rd_number = result['project_info']['rd_number']
                project_name = result['project_info']['project_name']

                # 生成 RD_PS 表
                step_counter[0] += 1
                progress = int((step_counter[0] / total_steps) * 100)
                socketio.emit('progress', {'progress': progress, 'message': "正在生成 RD_PS 表"})

                # 使用 project_info 生成 RD_PS 表
                rd_ps_file_path = save_rd_ps_table([result['project_info']], company_name, temp_dir)
                if os.path.exists(rd_ps_file_path):
                    # 使用文件的实际名称
                    rd_ps_filename = os.path.basename(rd_ps_file_path)
                    files_to_zip[rd_ps_filename] = rd_ps_file_path

                # 生成立项通知
                step_counter[0] += 1
                progress = int((step_counter[0] / total_steps) * 100)
                socketio.emit('progress', {'progress': progress, 'message': "正在生成项目立项通知"})

                year = int(result['project_info']['start_date'][:4])
                approval_file_path = save_project_approval_notice_to_docx(year, [result['project_info']], company_name, temp_dir)
                if os.path.exists(approval_file_path):
                    # 使用文件的实际名称
                    approval_filename = os.path.basename(approval_file_path)
                    files_to_zip[approval_filename] = approval_file_path

                # 生成研发活动一览表
                step_counter[0] += 1
                progress = int((step_counter[0] / total_steps) * 100)
                socketio.emit('progress', {'progress': progress, 'message': "正在生成研发活动一览表"})

                summary_file_path = save_project_summary_table([result['project_info']], company_name, temp_dir)
                if os.path.exists(summary_file_path):
                    # 使用文件的实际名称
                    summary_filename = os.path.basename(summary_file_path)
                    files_to_zip[summary_filename] = summary_file_path

        # 如果是本地IP且用户指定了保存路径，则直接保存到指定路径
        logger.info(f"检查是否可以保存到本地路径: is_local={is_local}, user_output_path={user_output_path}")

        # 检查路径是否存在，如果不存在则尝试创建
        if is_local and user_output_path:
            if not os.path.exists(user_output_path):
                try:
                    logger.info(f"尝试创建目录: {user_output_path}")
                    os.makedirs(user_output_path, exist_ok=True)
                except Exception as e:
                    logger.error(f"创建目录失败: {e}")

            # 再次检查路径是否存在
            if os.path.isdir(user_output_path):
                try:
                    logger.info(f"开始复制文件到: {user_output_path}")
                    # 确保输出目录存在
                    os.makedirs(user_output_path, exist_ok=True)

                    # 复制所有文件到用户指定的路径
                    for file_name, file_path in files_to_zip.items():
                        dest_path = os.path.join(user_output_path, os.path.basename(file_name))
                        logger.info(f"复制文件: {file_path} -> {dest_path}")
                        shutil.copy2(file_path, dest_path)
                        saved_files.append(dest_path)

                    # 返回成功消息
                    logger.info(f"文件已成功保存到本地路径: {user_output_path}")
                    return jsonify({
                        'success': True,
                        'message': f"文件已成功保存到 {user_output_path}",
                        'files': saved_files
                    })
                except Exception as e:
                    logger.error(f"保存文件到用户指定路径失败: {e}")
                    # 如果保存失败，回退到压缩包下载
            else:
                logger.error(f"指定的路径不是有效目录: {user_output_path}")
        else:
            logger.info(f"不满足本地保存条件，将创建ZIP文件供下载")

        # 如果不是本地IP或没有指定保存路径或保存失败，则创建ZIP文件供下载
        memory_file = create_download_zip(files_to_zip)

        # 成功创建ZIP下载包时，扣减用户报告配额
        username = session.get('username')
        if username:
            try:
                # 扣减1个报告配额（单个报告算1个）
                success = consume_user_quota(username, 1)
                if success:
                    logger.info(f"用户 {username} 生成完整报告包，扣减配额 1个")
                else:
                    logger.warning(f"用户 {username} 配额扣减失败")
            except Exception as e:
                logger.error(f"扣减用户 {username} 配额时出错: {e}")

        # 返回ZIP文件供下载
        return send_zip_file(memory_file, "项目资料")
    finally:
        # 清理临时目录
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            logger.error(f"清理临时目录失败: {e}")

@app.route('/upload_multiple', methods=['POST'])
@login_required
def upload_multiple():
    # 防重复处理检查
    if hasattr(upload_multiple, '_processing') and upload_multiple._processing:
        error_msg = log_and_emit_error('permission_denied', '检查批量处理状态', ValueError("批量处理已在进行中"))
        return error_msg, 429

    try:
        # 标记为处理中
        upload_multiple._processing = True

        # 清理设备描述缓存，避免跨批次污染
        cache_size_before = len(EQUIPMENT_DESCRIPTION_CACHE)
        EQUIPMENT_DESCRIPTION_CACHE.clear()
        logger.debug(f"清理设备缓存 ({cache_size_before}项)")

        form_data = request.form.to_dict()
        files_data = request.files
        company_name = form_data.get('company_name', '').strip()

        # 获取共享的项目编号和验收报告编号
        shared_project_number = form_data.get('project_number', '').strip()
        shared_acceptance_number = form_data.get('acceptance_number', '').strip()

        # 获取用户指定的保存路径
        user_output_path = form_data.get('output_path', '').strip()

        # 获取客户端IP地址
        client_ip = request.remote_addr
        is_local = is_local_ip(client_ip)

        staff_excel = files_data.get('staff_excel')
        rdps_file = files_data.get('rdps_file')  # 获取RDPS表文件

        # 创建临时目录用于存储生成的文件
        temp_dir = tempfile.mkdtemp()
        try:
            # 使用临时目录作为输出路径
            form_data['output_path'] = temp_dir

            report_count = 0
            report_indices = []
            for i in range(1, 21): # <--- 修改后的行：从9改为21，以支持最多20个报告
                project_name = form_data.get(f'project_name_{i}', '').strip()
                rd_number = form_data.get(f'rd_number_{i}', '')
                # 检查该报告的必填字段是否都存在，以及是否有对应的技术文件（如果需要）
                # 这里简化为只要项目名和RD序号存在就认为是一个有效的报告条目
                if project_name and rd_number:
                    # 你可能还需要检查是否有文件上传 fileX_i，这里省略以保持简洁
                    # 并且确保 form_data 中其他必须的字段如 start_date_i, end_date_i 也存在
                    # 此处仅基于项目名和RD号来计数，具体验证逻辑可能需要根据你的需求细化
                    report_count += 1
                    report_indices.append(i)

            if report_count == 0:
                error_msg = log_and_emit_error('invalid_data', '检查报告参数', ValueError("请至少填写一个报告的必填字段"))
                return error_msg, 400

            # 动态计算总步骤数：
            # 每个报告基础步骤：开始处理(1) + 技术文件处理(5) + 辅助账处理(1) + 生成报告(1) + 设备处理(1) + 保存计划书(1) + 保存验收报告(1) = 11步
            # 设备API调用：预估每个报告最多5个设备 = 5步
            # 每个报告总计：16步

            # 额外步骤：
            # 1. 立项通知：按年份生成，预估最多3个年份 = 3步
            # 2. 汇总表：1步
            # 3. RD_PS表：1步
            # 额外总计：5步

            total_steps = report_count * 16 + 5  # 每个报告16步 + 额外5步
            step_counter = [0]

            results = []
            projects = []
            files_to_zip = {}
            saved_files = []

            for report_index in report_indices:
                # 为每个报告添加共享的项目编号和验收报告编号
                if shared_project_number:
                    form_data[f'project_number_{report_index}'] = shared_project_number
                if shared_acceptance_number:
                    form_data[f'acceptance_number_{report_index}'] = shared_acceptance_number

                result = process_single_report(form_data, files_data, report_index, company_name, staff_excel, total_steps, step_counter, is_single_report=False, rdps_file=rdps_file)
                if isinstance(result, str):
                    error_msg = log_and_emit_error('file_processing', f'处理报告{report_index}', ValueError(result))
                    return error_msg, 400
                results.append(result['result'])
                projects.append(result['project_info'])

                # 收集每个报告的文件
                if 'files' in result:
                    for file_name, file_path in result['files'].items():
                        if os.path.exists(file_path):
                            base_filename = os.path.basename(file_path)

                            # 确保文件名唯一性 - 如果文件名已存在，添加序号
                            unique_filename = base_filename
                            counter = 1
                            while unique_filename in files_to_zip:
                                name, ext = os.path.splitext(base_filename)
                                unique_filename = f"{name}-副本{counter}{ext}"
                                counter += 1

                            files_to_zip[unique_filename] = file_path
                            logger.debug(f"添加文件: {unique_filename}")

            # 收集所有唯一的项目启动年份
            start_years = sorted(set(int(p['start_date'][:4]) for p in projects))

            # 为每个启动年份生成立项通知（防重复生成）
            approval_file_paths = []
            generated_years = set()  # 记录已生成的年份

            for year in start_years:
                # 防重复生成检查
                if year in generated_years:
                    logger.warning(f"年份 {year} 的立项通知已生成，跳过重复生成")
                    continue

                step_counter[0] += 1
                # 智能进度控制：确保不超过95%
                progress = min(int((step_counter[0] / total_steps) * 95), 95)
                socketio.emit('progress', {'progress': progress, 'message': f"生成 {year} 年项目立项通知"})

                try:
                    approval_file_path = save_project_approval_notice_to_docx(year, projects, company_name, temp_dir)
                    approval_file_paths.append(approval_file_path)
                    generated_years.add(year)  # 标记为已生成

                    if os.path.exists(approval_file_path):
                        # 使用文件的实际名称
                        approval_filename = os.path.basename(approval_file_path)
                        # 防重复文件名
                        unique_filename = approval_filename
                        counter = 1
                        while unique_filename in files_to_zip:
                            name, ext = os.path.splitext(approval_filename)
                            unique_filename = f"{name}-副本{counter}{ext}"
                            counter += 1
                        files_to_zip[unique_filename] = approval_file_path
                        logger.debug(f"添加立项通知: {unique_filename}")
                    else:
                        logger.error(f"立项通知文件不存在: {approval_file_path}")
                except Exception as e:
                    logger.error(f"生成 {year} 年立项通知失败: {e}")
                    continue

            step_counter[0] += 1
            progress = min(int((step_counter[0] / total_steps) * 97), 97)
            socketio.emit('progress', {'progress': progress, 'message': "生成研发活动一览表"})

            summary_file_path = save_project_summary_table(projects, company_name, temp_dir)
            if os.path.exists(summary_file_path):
                # 使用文件的实际名称
                summary_filename = os.path.basename(summary_file_path)
                                # 防重复文件名
                unique_filename = summary_filename
                counter = 1
                while unique_filename in files_to_zip:
                    name, ext = os.path.splitext(summary_filename)
                    unique_filename = f"{name}-副本{counter}{ext}"
                    counter += 1
                files_to_zip[unique_filename] = summary_file_path
                logger.debug(f"添加研发活动一览表: {unique_filename}")

            step_counter[0] += 1
            progress = min(int((step_counter[0] / total_steps) * 99), 99)
            socketio.emit('progress', {'progress': progress, 'message': "生成 RD,PS 表"})

            rd_ps_file_path = save_rd_ps_table(projects, company_name, temp_dir)
            if os.path.exists(rd_ps_file_path):
                # 使用文件的实际名称
                rd_ps_filename = os.path.basename(rd_ps_file_path)
                # 防重复文件名
                unique_filename = rd_ps_filename
                counter = 1
                while unique_filename in files_to_zip:
                    name, ext = os.path.splitext(rd_ps_filename)
                    unique_filename = f"{name}-副本{counter}{ext}"
                    counter += 1
                files_to_zip[unique_filename] = rd_ps_file_path
                logger.debug(f"添加RD_PS表: {unique_filename}")

            # 最终完成
            progress = 100
            cache_size_final = len(EQUIPMENT_DESCRIPTION_CACHE)
            logger.info(f"批量处理完成 (缓存{cache_size_final}个设备)")
            socketio.emit('progress', {'progress': progress, 'message': f"所有报告生成完成，准备下载 (缓存了{cache_size_final}个设备描述)"})

            # 如果是本地IP且用户指定了保存路径，则直接保存到指定路径
            logger.debug(f"检查本地保存: local={is_local}, path={user_output_path}")

            # 检查路径是否存在，如果不存在则尝试创建
            if is_local and user_output_path:
                if not os.path.exists(user_output_path):
                    try:
                        logger.debug(f"创建目录: {user_output_path}")
                        os.makedirs(user_output_path, exist_ok=True)
                    except Exception as e:
                        logger.error(f"创建目录失败: {e}")

                # 再次检查路径是否存在
                if os.path.isdir(user_output_path):
                    try:
                        logger.debug(f"复制文件到: {user_output_path}")
                        # 确保输出目录存在
                        os.makedirs(user_output_path, exist_ok=True)

                        # 复制所有文件到用户指定的路径
                        for file_name, file_path in files_to_zip.items():
                            dest_path = os.path.join(user_output_path, os.path.basename(file_name))
                            logger.debug(f"复制: {os.path.basename(file_path)}")
                            shutil.copy2(file_path, dest_path)
                            saved_files.append(dest_path)

                        # 返回成功消息
                        logger.info(f"文件已保存到: {user_output_path}")
                        return jsonify({
                            'success': True,
                            'message': f"文件已成功保存到 {user_output_path}",
                            'files': saved_files
                        })
                    except Exception as e:
                        logger.error(f"保存文件到用户指定路径失败: {e}")
                        # 如果保存失败，回退到压缩包下载
                else:
                    logger.error(f"指定的路径不是有效目录: {user_output_path}")
            else:
                logger.debug("创建ZIP文件供下载")

            # 创建ZIP文件
            memory_file = create_download_zip(files_to_zip)

            # 成功创建ZIP下载包时，扣减用户报告配额
            username = session.get('username')
            if username:
                try:
                    # 扣减配额数量等于生成的报告数量
                    success = consume_user_quota(username, report_count)
                    if success:
                        logger.info(f"用户 {username} 生成批量报告包({report_count}个报告)，扣减配额 {report_count}个")
                    else:
                        logger.warning(f"用户 {username} 配额扣减失败")
                except Exception as e:
                    logger.error(f"扣减用户 {username} 配额时出错: {e}")

            # 返回ZIP文件供下载
            return send_zip_file(memory_file, "项目资料")
        finally:
            # 清理临时目录
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception as e:
                logger.error(f"清理临时目录失败: {e}")
    except Exception as e:
        # 捕获所有未预期的错误
        error_msg = log_and_emit_error('unknown', '批量处理', e)
        return error_msg, 500
    finally:
        # 清理处理状态，发送完成信号
        if hasattr(upload_multiple, '_processing'):
            upload_multiple._processing = False
            logger.debug("清理处理状态")
        socketio.emit('generate_complete', {})

# 全局设备描述缓存，避免重复API调用
EQUIPMENT_DESCRIPTION_CACHE = {}

import pandas as pd
import os
import re
from datetime import datetime
import logging
import zipfile
import io
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Alignment, Font, Border, Side

def save_achievements_table(df, company_name, patent_files, output_path):
    """
    生成成果转化表，基于研发诊断表，首先按 RD序号 分组。
    如果不同 RD序号 的分组有相同的 PS名称，则合并为一组。
    合并后的组按 PS序号 排序（PS01, PS02, ...），样机/样品组排在最后。
    返回生成的 DataFrame、文件路径和分组信息，以便复用。
    """
    # 检查列名是否异常
    if any(col.startswith('Unnamed') for col in df.columns) or 'R&D诊断表' in df.columns:
        df.columns = df.iloc[0].astype(str).str.strip().replace('\uFEFF', '').replace('\r', '').replace('\n', '')
        df = df.drop(0).reset_index(drop=True)
    else:
        df.columns = [str(col).strip().replace('\uFEFF', '').replace('\r', '').replace('\n', '') for col in df.columns]

    logger.debug(f"Excel列名: {list(df.columns)}")
    logger.debug(f"Excel数据预览:\n{df.head(2).to_string()}")

    # 定义输入列名
    input_columns = {
        'rd_number': 'RD序号',
        'ps_name': 'PS名称',
        'patent_name': '专利名称',
        'patent_number': '专利号',
        'patent_type': '专利类型'
    }

    # 检查必要的列
    missing_columns = []
    actual_columns = {col.lower(): col for col in df.columns}
    for key, col in input_columns.items():
        if col not in df.columns:
            found = False
            for actual_col in df.columns:
                if col.lower() in actual_col.lower():
                    input_columns[key] = actual_col
                    found = True
                    break
            if not found:
                missing_columns.append(col)
    if missing_columns:
        logger.error(f"研发诊断表缺少以下列: {missing_columns}")
        return f"研发诊断表缺少以下列: {missing_columns}", 400, None, None

    # 初始化成果转化表
    achievements_data = {
        '序号': [],
        '科技成果名称': [],
        '技术成果类别': [],
        '产品/服务': [],
        '工艺': [],
        '样品/样机': [],
        '其他': [],
        '转让/许可': [],
        '合作': [],
        '证明材料': [],
        '备注': []
    }

    # 第一步：按 RD序号 分组
    rd_groups = {}
    current_rd_number = None
    current_ps_name = ''
    last_ps_name = ''

    # 处理 RD序号 列的空值，填充为上一个非空值
    df[input_columns['rd_number']] = df[input_columns['rd_number']].ffill()

    for index, row in df.iterrows():
        rd_number = str(row[input_columns['rd_number']]).strip() if input_columns['rd_number'] in df.columns else ''
        patent_name = str(row[input_columns['patent_name']]).strip()
        patent_number = str(row[input_columns['patent_number']]).strip()
        patent_type = str(row[input_columns['patent_type']]).strip()
        ps_name = str(row[input_columns['ps_name']]).strip()

        if not patent_name or patent_name.lower() == 'nan':
            logger.debug(f"跳过行 {index}：专利名称为空")
            continue

        # 分组逻辑：基于 RD序号
        if rd_number != current_rd_number:
            current_rd_number = rd_number
            current_ps_name = ps_name if ps_name.lower() != 'nan' else ''
            last_ps_name = current_ps_name
            if current_rd_number not in rd_groups:
                rd_groups[current_rd_number] = {
                    'ps_name': current_ps_name,
                    'patents': [],
                    'types': set(),
                    'remarks': set()
                }
        else:
            # 如果 RD序号 相同，检查 PS名称 是否有新值
            current_ps_name = ps_name if ps_name.lower() != 'nan' else last_ps_name
            last_ps_name = current_ps_name
            rd_groups[current_rd_number]['ps_name'] = current_ps_name

        # 添加数据到 RD分组
        rd_groups[current_rd_number]['patents'].append({
            'patent_name': patent_name,
            'patent_number': patent_number,
            'patent_type': patent_type
        })
        rd_groups[current_rd_number]['types'].add(patent_type)
        if patent_number and patent_number.lower() != 'nan':
            rd_groups[current_rd_number]['remarks'].add(patent_number)

    if not rd_groups:
        logger.error("研发诊断表中没有有效的专利名称数据")
        return "研发诊断表中没有有效的专利名称数据", 400, None, None

    # 第二步：按 PS名称 合并分组
    ps_name_to_rd_groups = {}
    for rd_number, group in rd_groups.items():
        ps_name = group['ps_name']
        if ps_name not in ps_name_to_rd_groups:
            ps_name_to_rd_groups[ps_name] = {
                'rd_numbers': [],
                'patents': [],
                'types': set(),
                'remarks': set()
            }
        ps_name_to_rd_groups[ps_name]['rd_numbers'].append(rd_number)
        ps_name_to_rd_groups[ps_name]['patents'].extend(group['patents'])
        ps_name_to_rd_groups[ps_name]['types'].update(group['types'])
        ps_name_to_rd_groups[ps_name]['remarks'].update(group['remarks'])

    # 第三步：分配 PS序号 并排序
    # 区分普通产品、样机/样品和空值组
    normal_ps_groups = {k: v for k, v in ps_name_to_rd_groups.items() if k and not k.startswith(('样机：', '样品：'))}
    sample_ps_groups = {k: v for k, v in ps_name_to_rd_groups.items() if k.startswith(('样机：', '样品：'))}
    empty_ps_groups = {k: v for k, v in ps_name_to_rd_groups.items() if not k}

    # 为普通产品分配 PS序号
    sorted_normal_ps = sorted(normal_ps_groups.keys())
    ps_name_to_ps_number = {}
    for idx, ps_name in enumerate(sorted_normal_ps, 1):
        ps_name_to_ps_number[ps_name] = f"PS{idx:02d}"

    # 样机/样品和空值组不分配 PS序号
    for ps_name in sample_ps_groups.keys():
        ps_name_to_ps_number[ps_name] = ps_name  # 保持原样
    for ps_name in empty_ps_groups.keys():
        ps_name_to_ps_number[ps_name] = ''  # 空值

    # 按 PS序号 排序分组（样机/样品和空值组排在最后）
    sorted_ps_names = (
        sorted(sorted_normal_ps, key=lambda x: ps_name_to_ps_number[x]) +
        sorted(sample_ps_groups.keys()) +
        sorted(empty_ps_groups.keys())
    )

    # 第四步：生成成果转化表数据，按排序后的 PS名称 组织
    current_row = 0
    ps_name_ranges = []
    for ps_name in sorted_ps_names:
        group = ps_name_to_rd_groups[ps_name]
        start_row = current_row + 1
        for patent in group['patents']:
            current_row += 1
            achievements_data['序号'].append(current_row)
            achievements_data['科技成果名称'].append(patent['patent_name'])
            achievements_data['技术成果类别'].append(patent['patent_type'])
            achievements_data['产品/服务'].append(ps_name)
            achievements_data['工艺'].append('')
            achievements_data['样品/样机'].append('')
            achievements_data['其他'].append('')
            achievements_data['转让/许可'].append('')
            achievements_data['合作'].append('')
            achievements_data['证明材料'].append('')
            achievements_data['备注'].append(patent['patent_number'])
        # 记录分组范围
        ps_name_ranges.append({
            'group_key': ps_name,
            'ps_name': ps_name,
            'start_row': start_row,
            'end_row': current_row,
            'rd_numbers': group['rd_numbers']
        })

    # 创建 DataFrame
    achievements_df = pd.DataFrame(achievements_data)
    columns_to_keep = [
        '序号', '科技成果名称', '技术成果类别', '产品/服务',
        '工艺', '样品/样机', '其他', '转让/许可', '合作',
        '证明材料', '备注'
    ]
    achievements_df = achievements_df[columns_to_keep]

    # 创建 Excel 文件
    output_filename = f"{company_name}-成果转化表-{datetime.now().strftime('%Y%m%d')}.xlsx"
    if output_path and os.path.isdir(output_path):
        output_file_path = os.path.join(output_path, output_filename)
    else:
        output_file_path = output_filename

    wb = Workbook()
    ws = wb.active
    ws.title = "成果转化表"

    ws.page_setup.orientation = ws.ORIENTATION_LANDSCAPE
    ws.page_setup.paperSize = ws.PAPERSIZE_A4
    ws.page_setup.horizontalCentered = True
    ws.page_setup.verticalCentered = True

    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                         top=Side(style='thin'), bottom=Side(style='thin'))

    ws.append(["企业近三年科技成果转化汇总表"])
    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(columns_to_keep))
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws['A1'].font = Font(size=14, bold=True)
    ws.row_dimensions[1].height = 40

    ws.append(columns_to_keep)
    ws.row_dimensions[2].height = 33
    for col in range(1, len(columns_to_keep) + 1):
        cell = ws.cell(row=2, column=col)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.font = Font(bold=True)
        cell.border = thin_border

    for r_idx, row in enumerate(dataframe_to_rows(achievements_df, index=False, header=False), 3):
        ws.append(row)
        ws.row_dimensions[r_idx].height = 28.35
        for c_idx in range(1, len(columns_to_keep) + 1):
            cell = ws.cell(row=r_idx, column=c_idx)
            if c_idx == 2:
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            elif c_idx == 4:
                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            else:
                cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = thin_border

    product_service_col = columns_to_keep.index('产品/服务') + 1
    proof_col = columns_to_keep.index('证明材料') + 1
    for ps_range in ps_name_ranges:
        start_row = ps_range['start_row'] + 2
        end_row = ps_range['end_row'] + 2
        ps_name = ps_range['ps_name']
        group = ps_name_to_rd_groups[ps_name]
        if start_row < end_row:
            ws.merge_cells(start_row=start_row, start_column=product_service_col,
                          end_row=end_row, end_column=product_service_col)
            ws.cell(row=start_row, column=product_service_col).value = ps_name
            ws.cell(row=start_row, column=product_service_col).alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            ws.cell(row=start_row, column=product_service_col).border = thin_border
        elif start_row == end_row:
            ws.cell(row=start_row, column=product_service_col).value = ps_name
            ws.cell(row=start_row, column=product_service_col).alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            ws.cell(row=start_row, column=product_service_col).border = thin_border
        proof_items = []
        remarks = group['remarks']
        types = group['types']
        has_ps_content = bool(ps_name)
        if has_ps_content:
            proof_items.extend(['专利证书', '专利摘要', '检测报告', '销售合同', '销售发票'])
        if any('sr' in str(remark).lower() for remark in remarks):
            if not proof_items:
                proof_items.extend(['专利证书', '软著证书', '专利摘要', '检测报告', '销售合同', '销售发票'])
            else:
                proof_items.insert(1, '软著证书')
        if any(t in ['受理实用', '受理发明'] for t in types):
            if not proof_items:
                proof_items.extend(['专利证书', '专利摘要', '检测报告', '销售合同', '销售发票'])
            proof_items.insert(2 if '软著证书' in proof_items else 1, '受理通知书')
        proof_text = '，'.join(proof_items) if proof_items else ''
        ws.cell(row=start_row, column=proof_col).value = proof_text
        if start_row < end_row:
            ws.merge_cells(start_row=start_row, start_column=proof_col,
                          end_row=end_row, end_column=proof_col)
            ws.cell(row=start_row, column=proof_col).alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            ws.cell(row=start_row, column=proof_col).border = thin_border

    ws.column_dimensions['C'].width = 12
    ws.column_dimensions['J'].width = 10
    for col_idx in range(1, len(columns_to_keep) + 1):
        if col_idx in [3, 10]:
            continue
        max_length = 0
        for row_idx in range(2, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            try:
                if cell.value:
                    cell_length = len(str(cell.value))
                    max_length = max(max_length, cell_length)
            except:
                pass
        adjusted_width = (max_length + 2) * 1.2
        ws.column_dimensions[ws.cell(row=2, column=col_idx).column_letter].width = adjusted_width

    wb.save(output_file_path)
    logger.info(f"成果转化表已保存到 {output_file_path}")
    return output_file_path, 200, ps_name_ranges, achievements_df

def save_achievements_summary_docx(achievements_df, company_name, output_path, ps_name_ranges, revenue_2024=None, percentage_2024=None):
    """
    生成成果转化总体情况说明文档，基于成果转化表数据和分组信息。
    确保成果说明、专利号/软件著作登记号、内部转化为产品/样品与成果转化汇总表一致。
    修改：
    1. 直接使用 revenue_2024 和 percentage_2024，不计算占比。
    2. 修正表格和后续文字中 PS 序号重复问题。
    3. 确保"693.63万元"和"70.59%"底色为红色（仅对默认值）。
    4. 确保样机/样品数量正确显示。
    """
    from docx import Document
    from docx.shared import Pt, Cm, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_ALIGN_VERTICAL
    from docx.oxml.ns import qn
    from docx.oxml import OxmlElement
    import pandas as pd
    import os
    from datetime import datetime
    import logging
    import re

    logger = logging.getLogger(__name__)

    # 验证列名
    required_columns = ['序号', '科技成果名称', '技术成果类别', '产品/服务', '样品/样机', '备注']
    missing_columns = [col for col in required_columns if col not in achievements_df.columns]
    if missing_columns:
        logger.error(f"成果转化表缺少以下列: {missing_columns}")
        raise ValueError(f"成果转化表缺少以下列: {missing_columns}")

    doc = Document()

    for section in doc.sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

    # 标题
    p = doc.add_heading("科技成果转化总体情况说明", level=1)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        run.font.name = '黑体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
        run.font.size = Pt(16)
        run.bold = True

    # 标题后加一个空行
    doc.add_paragraph()

    # 第一段：引言
    intro_text = (
        f"{company_name}是一家专注于高新技术产品研发的公司，"
        "根据国家科技成果转化的相关法律法规，结合实际情况，"
        "制定了《科技成果转化管理制度》和《科技成果转化激励制度》。"
        "从机制上确保技术创新能力，促进科技成果转化，激发研发人员的积极性和创造性。"
    )
    p = doc.add_paragraph(intro_text)
    p.paragraph_format.line_spacing = 1.5
    p.paragraph_format.first_line_indent = Pt(28)
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    # 统计专利类型
    utility_patents = 0  # 实用新型
    invention_patents = 0  # 发明专利
    accepted_patents = 0  # 受理专利
    software_copyrights = 0  # 软件著作权

    for remark in achievements_df['备注']:
        if pd.isna(remark):
            continue
        remark = str(remark).strip().upper()
        if 'ZL' in remark:
            match = re.search(r'ZL(\d+)', remark)
            if match:
                number = match.group(1)
                if len(number) >= 5:
                    fifth_digit = number[4]
                    if fifth_digit == '2':
                        utility_patents += 1
                    elif fifth_digit == '1':
                        invention_patents += 1
        elif 'SR' in remark:
            software_copyrights += 1
        else:
            accepted_patents += 1

    total_achievements = len(achievements_df)
    patent_summary = []
    if utility_patents > 0:
        patent_summary.append(f"实用新型{utility_patents}项")
    if invention_patents > 0:
        patent_summary.append(f"发明专利{invention_patents}项")
    if accepted_patents > 0:
        patent_summary.append(f"受理专利{accepted_patents}项")
    if software_copyrights > 0:
        patent_summary.append(f"软件著作权{software_copyrights}项")
    patent_summary_text = "，".join(patent_summary) + "，详见下表："

    # 第二段：成果统计
    p = doc.add_paragraph(
        f"近年来，公司通过自主研发形成科技成果共计{total_achievements}项，其中：{patent_summary_text}"
    )
    p.paragraph_format.line_spacing = 1.5
    p.paragraph_format.first_line_indent = Pt(28)
    for run in p.runs:
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)

    # 构建表格数据，包含所有成果
    grouped_data = []
    for ps_range in ps_name_ranges:
        start_idx = ps_range['start_row'] - 1
        end_idx = ps_range['end_row'] - 1
        if start_idx < len(achievements_df):
            for idx in range(start_idx, min(end_idx + 1, len(achievements_df))):
                row = achievements_df.iloc[idx]
                grouped_data.append({
                    '序号': len(grouped_data) + 1,
                    '科技成果名称': row['科技成果名称'],
                    '备注': row['备注'] if pd.notna(row['备注']) else '',
                    '产品/服务': ps_range['ps_name'] if ps_range['ps_name'] else ''
                })

    logger.info(f"表格中成果数量: {len(grouped_data)}")

    # 表格
    table = doc.add_table(rows=len(grouped_data) + 1, cols=4)
    table.style = 'Table Grid'
    table.autofit = False
    table.alignment = WD_ALIGN_PARAGRAPH.CENTER
    table.columns[0].width = Cm(1.3)
    table.columns[1].width = Cm(7.2)
    table.columns[2].width = Cm(3.5)
    table.columns[3].width = Cm(4.0)

    # 表头
    headers = ["序号", "成果名称", "专利号/软件著作登记号", "内部转化为产品/样品"]
    hdr_cells = table.rows[0].cells
    for idx, header in enumerate(headers):
        p = hdr_cells[idx].paragraphs[0]
        p.text = header
        p.paragraph_format.line_spacing = 1.0
        p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        for run in p.runs:
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(10)
            run.bold = True

    # 表格内容
    for idx, row in enumerate(grouped_data):
        row_cells = table.rows[idx + 1].cells
        row_cells[0].text = str(row['序号'])
        row_cells[1].text = row['科技成果名称']
        row_cells[2].text = row['备注']
        for cell_idx, cell in enumerate(row_cells):
            for paragraph in cell.paragraphs:
                paragraph.paragraph_format.line_spacing = 1.0
                if cell_idx == 1:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                else:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(10)

    # 合并"内部转化为产品/样品"列并分配 PS 序号
    normal_ps_names = [ps['ps_name'] for ps in ps_name_ranges if ps['ps_name'] and not ps['ps_name'].startswith(('样机：', '样品：'))]
    normal_ps_names = sorted(normal_ps_names)
    ps_name_to_ps_number = {ps_name: f"PS{i+1:02d}" for i, ps_name in enumerate(normal_ps_names)}

    for ps_range in ps_name_ranges:
        start_idx = ps_range['start_row'] - 1
        end_idx = ps_range['end_row'] - 1
        if start_idx < len(grouped_data):
            if start_idx < end_idx:
                table.cell(start_idx + 1, 3).merge(table.cell(end_idx + 1, 3))
            merged_cell = table.cell(start_idx + 1, 3)
            ps_name = ps_range['ps_name']
            ps_name_clean = re.sub(r'^PS\d+[:：]\s*', '', ps_name)
            if ps_name and not ps_name.startswith(('样机：', '样品：')):
                ps_number = ps_name_to_ps_number.get(ps_name, '')
                merged_cell.text = f"{ps_number}: {ps_name_clean}" if ps_name else ""
            else:
                merged_cell.text = ps_name if ps_name else ""
            for paragraph in merged_cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                paragraph.paragraph_format.line_spacing = 1.0
                merged_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                for run in paragraph.runs:
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(10)

    # 表后跳一行
    doc.add_paragraph()

    # 计算高新技术产品数量和样机/样品数量
    converted_products = len([ps for ps in ps_name_ranges if ps['ps_name'] and not ps['ps_name'].startswith(('样机：', '样品：'))])
    sample_prototype_from_groups = len([ps for ps in ps_name_ranges if ps['ps_name'] and ps['ps_name'].startswith(('样机：', '样品：'))])
    sample_prototype_from_df = len([x for x in achievements_df['样品/样机'] if pd.notna(x) and x.strip() != ''])
    sample_prototype_count = max(sample_prototype_from_groups, sample_prototype_from_df)  # 确保数量准确

    # 动态生成 PS 名称列表
    ps_names = [ps['ps_name'] for ps in ps_name_ranges if ps['ps_name'] and not ps['ps_name'].startswith(('样机：', '样品：'))]
    ps_names = sorted(ps_names)
    sample_names = [ps['ps_name'] for ps in ps_name_ranges if ps['ps_name'] and ps['ps_name'].startswith(('样机：', '样品：'))]

    ps_text = ""
    for i, ps_name in enumerate(ps_names, 1):
        ps_number = f"PS{i:02d}"
        ps_name_clean = re.sub(r'^PS\d+[:：]\s*', '', ps_name)
        ps_text += f"{ps_number}：{ps_name_clean}"
        if i < len(ps_names):
            ps_text += "，"
    if sample_names:
        if ps_text:
            ps_text += "，"
        ps_text += "，".join(sample_names)

    # 后续文字
    effect_text = (
        f"上表中{total_achievements}项科技成果均已成功实施转化。"
        f"我公司通过内部转化形式，形成了{converted_products}项高新技术产品，"
    )

    if sample_prototype_count > 0:
        effect_text += f"产生了{sample_prototype_count}项样机/样品，"

    effect_text += (
        f"{ps_text}，产品已通过质量检测。"
        f"我公司产品被各大客户认可，并带来了良好的收益。"
    )

    # 处理收入和百分比
    if revenue_2024 is not None and percentage_2024 is not None:
        revenue_part = f"{revenue_2024:.2f}"
        percentage_part = f"{percentage_2024:.2f}"
    else:
        revenue_part = "693.63"
        percentage_part = "70.59"
    revenue_with_unit = f"{revenue_part}万元"
    percentage_with_unit = f"{percentage_part}%"
    effect_text += f"2024年实现高新技术产品收入{revenue_with_unit}，占全年公司总收入的{percentage_with_unit}。"

    effect_text += "公司成果转化制度完善，科技成果转化能力强，有效提升了公司的市场竞争力。"

    p = doc.add_paragraph("")
    p.paragraph_format.line_spacing = 1.5
    p.paragraph_format.first_line_indent = Pt(28)

    # 分段处理文本，设置底纹色
    parts = effect_text.split(revenue_with_unit)
    for i, part in enumerate(parts):
        run = p.add_run(part)
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)
        if i < len(parts) - 1:
            run = p.add_run(revenue_with_unit)
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(12)
            if revenue_part == "693.63":  # 仅对默认值设置红色底色
                shading_elm = OxmlElement('w:shd')
                shading_elm.set(qn('w:fill'), "FF0000")  # 红色底纹
                shading_elm.set(qn('w:val'), "clear")
                run._element.get_or_add_rPr().append(shading_elm)

    # 重新获取 effect_text 以处理 percentage_with_unit
    effect_text = ''.join(run.text for run in p.runs)
    p.clear()
    parts = effect_text.split(percentage_with_unit)
    for i, part in enumerate(parts):
        run = p.add_run(part)
        run.font.name = '宋体'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        run.font.size = Pt(12)
        if i < len(parts) - 1:
            run = p.add_run(percentage_with_unit)
            run.font.name = '宋体'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            run.font.size = Pt(12)
            if percentage_part == "70.59":  # 仅对默认值设置红色底色
                shading_elm = OxmlElement('w:shd')
                shading_elm.set(qn('w:fill'), "FF0000")  # 红色底纹
                shading_elm.set(qn('w:val'), "clear")
                run._element.get_or_add_rPr().append(shading_elm)

    output_filename = f"成果转化总体情况说明-{datetime.now().strftime('%Y%m%d')}.docx"
    if output_path and os.path.isdir(output_path):
        output_file_path = os.path.join(output_path, output_filename)
    else:
        output_file_path = output_filename

    doc.save(output_file_path)
    logger.info(f"成果转化总体情况说明已保存到 {output_file_path}")
    return output_file_path

def set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0)):
    """为单元格设置边框：类型、宽度（twips）、颜色"""
    try:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()

        # 创建 w:tcBorders 元素
        tcBorders = OxmlElement('w:tcBorders')
        tcPr.append(tcBorders)

        # 使用黑色 (000000)，因为需求为黑色边框
        color_hex = "000000"
        logger.debug(f"使用颜色: {color_hex} (RGBColor={color})")

        # 为每个边框方向（top, left, bottom, right）设置属性
        for edge in ['top', 'left', 'bottom', 'right']:
            edge_element = OxmlElement(f'w:{edge}')
            edge_element.set(qn('w:val'), border_type)
            edge_element.set(qn('w:sz'), str(size))  # 4 twips = 0.5 pt
            edge_element.set(qn('w:space'), '0')
            edge_element.set(qn('w:color'), color_hex)
            tcBorders.append(edge_element)
            logger.debug(f"边框设置: 单元格边框 {edge} 设置为 {border_type}, 宽度={size} twips, 颜色={color_hex}")

        logger.debug("单元格边框设置完成")
    except Exception as e:
        logger.error(f"设置单元格边框失败: {e}")
        raise

def determine_patent_type(patent_number):
    """根据专利号判断专利类型"""
    if not patent_number:
        return "未知", patent_number, "未知"
    patent_number = str(patent_number).strip()
    if patent_number.startswith('ZL'):
        patent_number_clean = patent_number.replace('ZL', '')
        if len(patent_number_clean) >= 5 and patent_number_clean[4] == '2':
            return "实用新型专利", patent_number, "专利证书号"
        elif len(patent_number_clean) >= 5 and patent_number_clean[4] == '1':
            return "发明专利", patent_number, "专利证书号"
        else:
            return "受理专利", patent_number, "受理号"
    elif 'SR' in patent_number:
        return "软件著作权", patent_number, "软件著作权登记号"
    else:
        return "受理专利", patent_number, "受理号"

def get_proof_materials(patent_type):
    """根据专利类型返回证明材料"""
    if patent_type in ["实用新型专利", "发明专利"]:
        return "专利证书，专利摘要"
    elif patent_type == "软件著作权":
        return "软件登记证书，软件说明"
    else:
        return "受理通知书，专利说明"

def save_achievements_details_docx(achievements_df, patent_files, company_name, output_path, ps_name_ranges):
    # 验证必要的列
    required_columns = ['序号', '科技成果名称', '技术成果类别', '产品/服务', '备注']
    missing_columns = [col for col in required_columns if col not in achievements_df.columns]
    if missing_columns:
        logger.error(f"成果转化表缺少以下列: {missing_columns}")
        raise ValueError(f"成果转化表缺少以下列: {missing_columns}")

    # 检查并移除重复的专利号记录
    duplicates = achievements_df[achievements_df.duplicated(subset=['备注'], keep=False)]
    if not duplicates.empty:
        logger.warning(f"发现重复的专利号记录：\n{duplicates}")
    achievements_df = achievements_df.drop_duplicates(subset=['备注']).reset_index(drop=True)
    logger.info(f"处理后的成果转化表行数: {len(achievements_df)}")
    logger.debug(f"成果转化表数据：\n{achievements_df.to_string()}")
    logger.debug(f"成果转化表空值统计：\n{achievements_df.isna().sum()}")

    # 初始化文档
    doc = Document()
    for section in doc.sections:
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
        section.page_width = Cm(29.7)  # A4 横向
        section.page_height = Cm(21.0)  # A4 横向
        section.orientation = 1  # 横向
    logger.info("文档页面设置完成: A4 横向，边距 2.5cm/2.54cm，页面有效宽度=%.2f cm", 29.7 - 2.5 - 2.5)

    # 添加标题
    p = doc.add_heading("科技成果转化逐项说明", level=1)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    for run in p.runs:
        run.font.name = 'SimHei'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
        run._element.rPr.rFonts.set(qn('w:ascii'), 'Arial')  # 备用字体
        run.font.size = Pt(14)
        run.bold = True
        logger.debug(f"标题字体实际应用: {run.font.name}")
    logger.info("标题设置完成: SimHei，14号，居中")

    # 添加 1.5 倍行距的空行
    p = doc.add_paragraph()
    p.paragraph_format.line_spacing = 1.5
    logger.info("空行添加完成: 1.5倍行距")

    # 提取专利号和摘要的映射
    pdf_abstracts = {}
    tech_files_zip = request.files.get('tech_files_zip')
    if tech_files_zip and patent_files:
        try:
            tech_files_zip.seek(0)
            zip_content = io.BytesIO(tech_files_zip.read())
            with zipfile.ZipFile(zip_content, 'r') as zip_ref:
                for file_name in patent_files:
                    match = re.search(r'(\d{12}\.\d)|(20\d{2}SR\d{7})', file_name)
                    if match:
                        patent_number = match.group(1) if match.group(1) else match.group(2)
                        with zip_ref.open(file_name) as file:
                            if file_name.lower().endswith('.pdf'):
                                try:
                                    pdf_reader = pypdf.PdfReader(file)
                                    text = ""
                                    for page in pdf_reader.pages:
                                        extracted_text = page.extract_text() or ""
                                        text += extracted_text
                                    abstract_content = ""
                                    if not text or len(text) < 100 or "摘要" not in text:
                                        logger.warning(f"PDF 文件 {file_name} 提取文本不足或未找到摘要，尝试使用 API 重新解析")
                                        file.seek(0)
                                        response = client.files().create(file=(file_name, file), purpose="assistants")
                                        file_id = response.get('id')
                                        if not file_id:
                                            logger.error(f"文件 {file_name} 上传失败，跳过")
                                            pdf_abstracts[patent_number] = ""
                                            continue
                                        prompt = (
                                            "请从以下 PDF 文件内容中提取'摘要'部分，从'摘要'后的第 57 个字符开始，"
                                            "直到第一个双换行符（\\n\\n）或文件末尾。如果没有'摘要'，返回空字符串。"
                                            "不要输出无关信息，仅返回提取的摘要内容。"
                                        )
                                        response = client.chat().create_completion(
                                            model="moonshot-v1-32k",
                                            messages=[
                                                {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长中文文本提取。"},
                                                {"role": "user", "content": prompt},
                                                {"role": "system", "content": f"文件 ID: {file_id}"}
                                            ],
                                            temperature=0.3
                                        )
                                        abstract_content = response['choices'][0]['message']['content'].strip()
                                        logger.info(f"专利号 {patent_number} 使用 API 提取摘要成功")
                                    else:
                                        abstract_match = re.search(r'摘要', text)
                                        if abstract_match:
                                            start_pos = abstract_match.start()
                                            abstract_text = text[start_pos + 57:]
                                            end_match = re.search(r'\n\n|\r\n\r\n|$', abstract_text)
                                            if end_match:
                                                abstract_content = abstract_text[:end_match.start()].strip()
                                            else:
                                                abstract_content = abstract_text.strip()
                                            logger.info(f"专利号 {patent_number} 摘要提取成功")
                                        else:
                                            abstract_content = ""
                                            logger.warning(f"PDF 文件 {file_name} 未找到 '摘要'")
                                    if abstract_content:
                                        tech_name = achievements_df[achievements_df['备注'].str.replace('ZL', '') == patent_number]['科技成果名称'].iloc[0] if patent_number in achievements_df['备注'].str.replace('ZL', '') else "该技术"
                                        prompt = (
                                            f"请根据以下摘要内容，撰写一段严格控制在110字以内的描述，必须以'通过{tech_name}的应用，达到了'开头，后接具体效果：\n"
                                            f"摘要内容：{abstract_content}\n"
                                            "描述需突出技术应用效果，语言简洁流畅，适合正式报告。"
                                        )
                                        response = client.chat().create_completion(
                                            model="moonshot-v1-32k",
                                            messages=[
                                                {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长撰写正式报告内容。"},
                                                {"role": "user", "content": prompt}
                                            ],
                                            temperature=0.3
                                        )
                                        effect_description = response['choices'][0]['message']['content'].strip()
                                        pdf_abstracts[patent_number] = effect_description
                                        logger.info(f"专利号 {patent_number} 生成效果描述成功: {effect_description} (字数: {len(effect_description)})")
                                    else:
                                        pdf_abstracts[patent_number] = ""
                                        logger.warning(f"专利号 {patent_number} 无摘要内容，效果描述为空")
                                except Exception as e:
                                    pdf_abstracts[patent_number] = ""
                                    logger.error(f"PDF 文件 {file_name} 解析失败: {e}")
        except zipfile.BadZipFile as e:
            logger.error(f"处理专利文件摘要失败: {e}")
            pdf_abstracts = {}
        except Exception as e:
            logger.error(f"处理专利文件摘要失败: {e}")
            pdf_abstracts = {}
    logger.info(f"专利摘要提取完成，提取到的专利号和效果描述: {pdf_abstracts}")

    # 创建表格
    table = doc.add_table(rows=len(achievements_df) + 1, cols=5)
    table.style = None  # 移除默认表格样式
    table.autofit = False
    table.allow_autofit = False
    table.width = Cm(24.96)  # 调整表格总宽度

    # 设置表格 XML 属性，禁用自动调整
    tbl = table._tbl
    tbl_pr = tbl.tblPr
    tbl_w = OxmlElement('w:tblW')
    tbl_w.set(qn('w:w'), str(int(24.96 * 360000)))  # 24.96 cm in twips
    tbl_w.set(qn('w:type'), 'dxa')
    tbl_pr.append(tbl_w)
    tbl_layout = OxmlElement('w:tblLayout')
    tbl_layout.set(qn('w:type'), 'fixed')
    tbl_pr.append(tbl_layout)

    # 设置表格网格列宽
    tbl_grid = OxmlElement('w:tblGrid')
    column_widths = [1.3, 4.6, 2.14, 2.34, 14.58]
    for width in column_widths:
        grid_col = OxmlElement('w:gridCol')
        grid_col.set(qn('w:w'), str(int(width * 360000)))  # cm to twips
        tbl_grid.append(grid_col)
    tbl.append(tbl_grid)

    logger.info("表格创建完成: 5列，行数=%d（含表头），总宽度=24.96 cm", len(achievements_df) + 1)
    logger.info(f"列宽设置完成: {', '.join([f'{w:.2f} cm' for w in column_widths])}")

    # 设置表头
    headers = ['序号', '科技成果名称', '成果类型', '转化的产品/样品', '应用成效']
    for col_idx, header in enumerate(headers):
        cell = table.cell(0, col_idx)
        cell.text = header
        cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        paragraph.paragraph_format.line_spacing = Pt(14)  # 0.5 cm
        paragraph.paragraph_format.space_before = Pt(0)
        paragraph.paragraph_format.space_after = Pt(0)
        run = paragraph.runs[0]
        run.font.name = 'SimHei'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
        run._element.rPr.rFonts.set(qn('w:ascii'), 'Arial')  # 备用字体
        run.font.size = Pt(10)
        run.bold = True
        set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0))
        logger.debug(f"表头单元格 {header} 字体实际应用: {run.font.name}")
        logger.debug(f"表头单元格 {header} 设置完成: SimHei，10号，居中，行高=0.5cm，垂直居中，实线边框")
    logger.info("表头设置完成")

    # 填充表格内容
    for idx, row in achievements_df.iterrows():
        serial_number = str(row['序号']) if pd.notna(row['序号']) else ""
        achievement_name = str(row['科技成果名称']) if pd.notna(row['科技成果名称']) else ""
        achievement_type = str(row['技术成果类别']) if pd.notna(row['技术成果类别']) else ""
        product = str(row['产品/服务']) if pd.notna(row['产品/服务']) else ""
        patent_number = str(row['备注']) if pd.notna(row['备注']) else ""

        if not all([serial_number, achievement_name, achievement_type, product]):
            logger.warning(f"行 {idx+1} 存在空值: 序号={serial_number}, 科技成果名称={achievement_name}, 成果类型={achievement_type}, 产品/服务={product}")

        normalized_patent_number = patent_number.replace('ZL', '') if patent_number else ''
        application_effect = pdf_abstracts.get(normalized_patent_number, "")
        if not application_effect:
            logger.warning(f"专利号 {normalized_patent_number} 未找到效果描述，应用成效列为空")
        else:
            logger.debug(f"应用成效内容 (行={idx+1}): {application_effect} (字数: {len(application_effect)})")

        table_row = table.rows[idx + 1]
        cells = table_row.cells
        cells[0].text = serial_number
        cells[1].text = achievement_name
        cells[2].text = achievement_type
        cells[3].text = product
        cells[4].text = application_effect

        # 设置垂直对齐为居中
        for cell_idx, cell in enumerate(cells):
            cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT if cell_idx in (1, 4) else WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = Pt(14)  # 0.5 cm
            paragraph.paragraph_format.space_before = Pt(0)
            paragraph.paragraph_format.space_after = Pt(0)
            run = paragraph.runs[0]
            run.font.name = 'SimSun'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
            run._element.rPr.rFonts.set(qn('w:ascii'), 'Arial')  # 备用字体
            run.font.size = Pt(10)
            set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0))
            logger.debug(f"单元格 (行={idx+1}, 列={cell_idx}) 字体实际应用: {run.font.name}")
            logger.debug(f"单元格 (行={idx+1}, 列={cell_idx}) 内容: {cell.text}")
            logger.debug(f"单元格 (行={idx+1}, 列={cell_idx}) 设置完成: 字体=SimSun, 字号=10号, 对齐={'左' if cell_idx in (1, 4) else '居中'}, 行高=0.5cm, 垂直居中, 实线边框")
        logger.info(f"表格行 {idx+1} 填充完成: 序号={serial_number}, 专利号={normalized_patent_number}")

    # 合并"转化的产品/样品"列
    logger.debug(f"ps_name_ranges: {ps_name_ranges}")
    for range_item in ps_name_ranges:
        if not isinstance(range_item, dict):
            logger.warning(f"无效的合并范围格式: {range_item}")
            continue
        start = range_item.get('start_row')
        end = range_item.get('end_row')
        if start is None or end is None:
            logger.warning(f"合并范围缺少 start_row 或 end_row: {range_item}")
            continue
        start = int(start) - 1
        end = int(end) - 1
        if start < 0 or end >= len(achievements_df) or start >= end:
            logger.warning(f"合并范围无效: start={start+2}, end={end+2}, 有效行数={len(achievements_df)}")
            continue
        product_content = str(achievements_df.iloc[start]['产品/服务']) if pd.notna(achievements_df.iloc[start]['产品/服务']) else ""
        logger.debug(f"合并前产品/样品列 (行范围=[{start+2}, {end+2}]) 内容: {product_content}")
        for row_idx in range(start + 1, end + 2):
            table.cell(row_idx, 3).text = ''
        table.cell(start + 1, 3).merge(table.cell(end + 1, 3))
        merged_cell = table.cell(start + 1, 3)
        merged_cell.text = product_content
        merged_cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        paragraph = merged_cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        paragraph.paragraph_format.line_spacing = Pt(14)  # 0.5 cm
        paragraph.paragraph_format.space_before = Pt(0)
        paragraph.paragraph_format.space_after = Pt(0)
        run = paragraph.runs[0]
        run.font.name = 'SimSun'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
        run._element.rPr.rFonts.set(qn('w:ascii'), 'Arial')  # 备用字体
        run.font.size = Pt(10)
        set_cell_border(merged_cell, border_type="single", size=4, color=RGBColor(0, 0, 0))
        logger.debug(f"合并单元格 (行范围=[{start+2}, {end+2}], 列=3) 字体实际应用: {run.font.name}")
        logger.debug(f"合并单元格 (行范围=[{start+2}, {end+2}], 列=3) 内容: {merged_cell.text}")
        logger.debug(f"合并单元格 (行范围=[{start+2}, {end+2}], 列=3) 设置完成: 字体=SimSun, 字号=10号, 对齐=居中, 行高=0.5cm, 垂直居中, 实线边框")
        logger.info(f"合并单元格完成: 产品/样品列, 行范围=[{start+2}, {end+2}], 内容={merged_cell.text}")

        # 重新设置合并后产品/样品列的宽度
        table.columns[3].width = Cm(2.34)
        logger.debug(f"合并后重新设置产品/样品列宽度: 2.34 cm")

    # 再次设置列宽，确保合并后正确
    for col_idx, width in enumerate(column_widths):
        table.columns[col_idx].width = Cm(width)
        logger.debug(f"最终列 {col_idx+1} 宽度设置为: {width:.2f} cm")

    logger.info("表格边框设置完成: 实线，0.5pt，黑色")

    # 保存文档
    output_filename = f"科技成果转化逐项说明-{datetime.now().strftime('%Y%m%d')}.docx"
    if output_path and os.path.isdir(output_path):
        output_file_path = os.path.join(output_path, output_filename)
    else:
        output_file_path = output_filename

    doc.save(output_file_path)
    logger.info(f"文档保存完成: {output_file_path}")
    return output_file_path

def save_achievement_separate_pages(achievements_df, pdf_abstracts, ps_name_ranges, company_name, output_path):
    """生成成果隔页文档"""
    # 创建以"成果隔页-时间"命名的文件夹
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    separate_folder = os.path.join(output_path, f"成果隔页-{timestamp}")
    os.makedirs(separate_folder, exist_ok=True)
    logger.info(f"创建成果隔页文件夹: {separate_folder}")

    output_files = []
    for idx, row in achievements_df.iterrows():
        serial_number = str(row['序号']) if pd.notna(row['序号']) else f"{idx + 1}"
        achievement_name = str(row['科技成果名称']) if pd.notna(row['科技成果名称']) else f"成果{idx + 1}"
        patent_number = str(row['备注']) if pd.notna(row['备注']) else ""
        product = str(row['产品/服务']) if pd.notna(row['产品/服务']) else ""
        normalized_patent_number = patent_number.replace('ZL', '') if patent_number else ''
        application_effect = pdf_abstracts.get(normalized_patent_number, "")

        # 判断专利类型
        patent_type, patent_number_display, number_type = determine_patent_type(patent_number)
        proof_materials = get_proof_materials(patent_type)
        if patent_type in ["实用新型专利", "发明专利"]:
            proof_materials += ", 专利证书"  # 添加专利证书到证明材料

        # 查找对应的 PS 编号和转化形式
        ps_number = ""
        ps_name = ""
        conversion_type = "产品"
        group_first_serial = serial_number  # 默认组内第一个为当前成果
        for range_item in ps_name_ranges:
            start = int(range_item.get('start_row', 0)) - 1
            end = int(range_item.get('end_row', 0)) - 1
            if start <= idx <= end:
                ps_name = range_item.get('ps_name', '')
                ps_number_match = re.match(r'PS(\d+)([:：]\s*)?', ps_name)
                ps_number = f"PS{ps_number_match.group(1)}" if ps_number_match else ''
                if ps_name.startswith(('样机：', '样品：')):
                    conversion_type = "样品"
                # 获取组内第一个成果的序号
                group_first_serial = str(achievements_df.iloc[start]['序号']) if pd.notna(achievements_df.iloc[start]['序号']) else f"{start + 1}"
                break
        ps_name_clean = re.sub(r'^PS\d+[:：]\s*', '', ps_name) if ps_name and not ps_name.startswith(('样机：', '样品：')) else ps_name

        # 初始化文档
        doc = Document()
        section = doc.sections[0]
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)

        # 标题：黑体二号，居中
        p = doc.add_heading(f"科技成果{serial_number}转化证明材料", level=1)
        p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        for run in p.runs:
            run.font.name = 'SimHei'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
            run.font.size = Pt(22)  # 二号
            run.bold = True

        # 空行
        p = doc.add_paragraph()
        p.paragraph_format.line_spacing = 1.5

        # 一、科技成果类别
        p = doc.add_heading("一、科技成果类别", level=2)
        p.paragraph_format.line_spacing = 1.5
        for run in p.runs:
            run.font.name = 'SimHei'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
            run.font.size = Pt(12)  # 小四
            run.bold = True

        table = doc.add_table(rows=2, cols=3)
        table.style = None
        table.autofit = False
        table.width = Cm(16.84)  # 7.44 + 4.5 + 4.9
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置列宽
        table.columns[0].width = Cm(7.44)
        table.columns[1].width = Cm(4.5)
        table.columns[2].width = Cm(4.9)

        # 设置行高和边框
        for row in table.rows:
            row.height = Cm(1.0)
            for cell in row.cells:
                cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0))

        # 表头
        headers = [patent_type, number_type, "证明材料"]
        for col_idx, header in enumerate(headers):
            cell = table.cell(0, col_idx)
            cell.text = header
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = Pt(14)
            run = paragraph.runs[0]
            run.font.name = 'SimSun'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
            run.font.size = Pt(10)

        # 表格内容
        cells = table.rows[1].cells
        cells[0].text = achievement_name
        cells[1].text = patent_number_display
        cells[2].text = proof_materials
        for cell in cells:
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = Pt(14)
            run = paragraph.runs[0]
            run.font.name = 'SimSun'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
            run.font.size = Pt(10)

        # 空行
        p = doc.add_paragraph()
        p.paragraph_format.line_spacing = 1.5

        # 二、转化形式
        proof_header = "证明材料" if serial_number == group_first_serial else f"证明材料（与科技成果{group_first_serial}证明材料相同）"
        proof_content = "1. 检测报告\n2. 销售合同\n3. 销售发票" if conversion_type == "产品" else "1. 样品图片\n2. 自检报告"
        p = doc.add_heading(f"二、转化形式：内部转化为{conversion_type}", level=2)
        p.paragraph_format.line_spacing = 1.5
        for run in p.runs:
            run.font.name = 'SimHei'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
            run.font.size = Pt(12)  # 小四
            run.bold = True

        table = doc.add_table(rows=2, cols=2)
        table.style = None
        table.autofit = False
        table.width = Cm(16.75)  # 5.75 + 11
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置列宽
        table.columns[0].width = Cm(5.75)
        table.columns[1].width = Cm(11)

        # 设置行高和边框
        for row in table.rows:
            row.height = Cm(1.0)
            for cell in row.cells:
                cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0))

        # 表头
        table.cell(0, 0).text = f"高新技术产品（{ps_number}）" if conversion_type == "产品" and ps_number else "样品/样机"
        table.cell(0, 1).text = proof_header
        for cell in table.rows[0].cells:
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = Pt(14)
            run = paragraph.runs[0]
            run.font.name = 'SimSun'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
            run.font.size = Pt(10)

        # 表格内容
        cells = table.rows[1].cells
        cells[0].text = ps_name_clean
        cells[1].text = proof_content
        for cell in cells:
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            paragraph.paragraph_format.line_spacing = Pt(14)
            run = paragraph.runs[0]
            run.font.name = 'SimSun'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
            run.font.size = Pt(10)

        # 空行
        p = doc.add_paragraph()
        p.paragraph_format.line_spacing = 1.5

        # 三、应用成效逐项说明
        p = doc.add_heading("三、应用成效逐项说明", level=2)
        p.paragraph_format.line_spacing = 1.5
        for run in p.runs:
            run.font.name = 'SimHei'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimHei')
            run.font.size = Pt(12)  # 小四
            run.bold = True

        table = doc.add_table(rows=1, cols=1)
        table.style = None
        table.autofit = False
        table.width = Cm(16.75)
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置列宽和行高
        table.columns[0].width = Cm(16.75)
        table.rows[0].height = Cm(3.92)
        cell = table.cell(0, 0)
        cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        set_cell_border(cell, border_type="single", size=4, color=RGBColor(0, 0, 0))

        # 表格内容
        cell.text = application_effect
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
        paragraph.paragraph_format.line_spacing = Pt(14)
        run = paragraph.runs[0]
        run.font.name = 'SimSun'
        run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')
        run.font.size = Pt(10)

        # 保存文档到指定文件夹
        output_filename = f"成果{serial_number}-{achievement_name}.docx"
        output_file_path = os.path.join(separate_folder, output_filename)
        doc.save(output_file_path)
        output_files.append(output_file_path)
        logger.info(f"成果隔页保存完成: {output_file_path}")

    return output_files, separate_folder, timestamp

def extract_pdf_first_page(patent_files, achievements_df, output_path, separate_folder, timestamp):
    """提取专利 PDF 的第一页作为摘要，并记录日志"""
    output_files = []
    unmatched_patents = []
    abstract_log = []
    tech_files_zip = request.files.get('tech_files_zip')
    if not tech_files_zip or not patent_files:
        log_entry = "未提供技术文件 ZIP 或专利文件，跳过 PDF 摘要提取"
        abstract_log.append(log_entry)
        logger.warning(log_entry)
        return output_files, unmatched_patents, abstract_log

    try:
        tech_files_zip.seek(0)
        zip_content = io.BytesIO(tech_files_zip.read())
        with zipfile.ZipFile(zip_content, 'r') as zip_ref:
            for idx, row in achievements_df.iterrows():
                serial_number = str(row['序号']) if pd.notna(row['序号']) else f"{idx + 1}"
                achievement_name = str(row['科技成果名称']) if pd.notna(row['科技成果名称']) else f"成果{idx + 1}"
                patent_number = str(row['备注']) if pd.notna(row['备注']) else ""
                if not patent_number:
                    log_entry = f"成果{serial_number} 无专利号，跳过 PDF 摘要提取"
                    unmatched_patents.append(f"成果{serial_number}（无专利号）")
                    abstract_log.append(log_entry)
                    logger.warning(log_entry)
                    continue

                normalized_patent_number = patent_number.replace('ZL', '').strip()
                is_software_copyright = 'SR' in normalized_patent_number
                patent_pattern = normalized_patent_number if is_software_copyright else normalized_patent_number.replace('.X', '')

                matched = False
                for file_name in patent_files:
                    match = re.search(r'(\d{12}\.\d)|(20\d{2}SR\d{7})', file_name)
                    if not match:
                        continue
                    file_patent_number = match.group(1) if match.group(1) else match.group(2)
                    normalized_file_number = file_patent_number.replace('.X', '') if not is_software_copyright else file_patent_number

                    if normalized_file_number == patent_pattern:
                        with zip_ref.open(file_name) as file:
                            if file_name.lower().endswith('.pdf'):
                                try:
                                    pdf_reader = pypdf.PdfReader(file)
                                    pdf_writer = pypdf.PdfWriter()
                                    pdf_writer.add_page(pdf_reader.pages[0])
                                    output_filename = f"成果{serial_number}-摘要.pdf"
                                    output_file_path = os.path.join(separate_folder, output_filename)
                                    with open(output_file_path, 'wb') as output_file:
                                        pdf_writer.write(output_file)
                                    output_files.append(output_file_path)
                                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）摘要提取成功: {output_file_path}"
                                    abstract_log.append(log_entry)
                                    logger.info(log_entry)
                                    matched = True
                                except Exception as e:
                                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）PDF 摘要提取失败: {e}"
                                    abstract_log.append(log_entry)
                                    logger.error(log_entry)
                        break

                if not matched:
                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）未找到匹配的 PDF 文件"
                    unmatched_patents.append(f"成果{serial_number}（专利号: {normalized_patent_number}）")
                    abstract_log.append(log_entry)
                    logger.warning(log_entry)

    except zipfile.BadZipFile as e:
        log_entry = f"处理 ZIP 文件失败: {e}"
        abstract_log.append(log_entry)
        logger.error(log_entry)
    except Exception as e:
        log_entry = f"提取 PDF 摘要失败: {e}"
        abstract_log.append(log_entry)
        logger.error(log_entry)

    # 保存摘要提取日志到 TXT 文件
    log_filename = f"摘要提取日志-{timestamp}.txt"
    log_file_path = os.path.join(separate_folder, log_filename)
    with open(log_file_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(abstract_log))
    logger.info(f"摘要提取日志保存完成: {log_file_path}")

    return output_files, unmatched_patents, abstract_log

def extract_patent_cert_pdf(patent_cert_files, achievements_df, output_path, separate_folder, timestamp):
    """提取专利证书 PDF 并保存为成果X-证书.pdf，并记录日志"""
    output_files = []
    unmatched_patents = []
    cert_log = []
    patent_cert_zip = request.files.get('patent_cert_zip')
    if not patent_cert_zip or not patent_cert_files:
        log_entry = "未提供专利证书 ZIP 或专利证书文件，跳过专利证书提取"
        cert_log.append(log_entry)
        logger.warning(log_entry)
        return output_files, unmatched_patents, cert_log

    try:
        patent_cert_zip.seek(0)
        zip_content = io.BytesIO(patent_cert_zip.read())
        with zipfile.ZipFile(zip_content, 'r') as zip_ref:
            for idx, row in achievements_df.iterrows():
                serial_number = str(row['序号']) if pd.notna(row['序号']) else f"{idx + 1}"
                achievement_name = str(row['科技成果名称']) if pd.notna(row['科技成果名称']) else f"成果{idx + 1}"
                patent_number = str(row['备注']) if pd.notna(row['备注']) else ""
                if not patent_number:
                    log_entry = f"成果{serial_number} 无专利号，跳过专利证书提取"
                    unmatched_patents.append(f"成果{serial_number}（无专利号）")
                    cert_log.append(log_entry)
                    logger.warning(log_entry)
                    continue

                normalized_patent_number = patent_number.replace('ZL', '').strip()
                is_software_copyright = 'SR' in normalized_patent_number
                patent_pattern = normalized_patent_number if is_software_copyright else normalized_patent_number.replace('.X', '')

                matched = False
                for file_name in patent_cert_files:
                    match = re.search(r'(\d{12}\.\d)|(20\d{2}SR\d{7})', file_name)
                    if not match:
                        continue
                    file_patent_number = match.group(1) if match.group(1) else match.group(2)
                    normalized_file_number = file_patent_number.replace('.X', '') if not is_software_copyright else file_patent_number

                    if normalized_file_number == patent_pattern:
                        with zip_ref.open(file_name) as file:
                            if file_name.lower().endswith('.pdf'):
                                try:
                                    output_filename = f"成果{serial_number}-证书.pdf"
                                    output_file_path = os.path.join(separate_folder, output_filename)
                                    with open(output_file_path, 'wb') as output_file:
                                        output_file.write(file.read())
                                    output_files.append(output_file_path)
                                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）专利证书提取成功: {output_file_path}"
                                    cert_log.append(log_entry)
                                    logger.info(log_entry)
                                    matched = True
                                except Exception as e:
                                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）专利证书提取失败: {e}"
                                    cert_log.append(log_entry)
                                    logger.error(log_entry)
                        break

                if not matched:
                    log_entry = f"成果{serial_number}（专利号: {normalized_patent_number}）未找到匹配的专利证书 PDF 文件"
                    unmatched_patents.append(f"成果{serial_number}（专利号: {normalized_patent_number}）")
                    cert_log.append(log_entry)
                    logger.warning(log_entry)

    except zipfile.BadZipFile as e:
        log_entry = f"处理专利证书 ZIP 文件失败: {e}"
        cert_log.append(log_entry)
        logger.error(log_entry)
    except Exception as e:
        log_entry = f"提取专利证书 PDF 失败: {e}"
        cert_log.append(log_entry)
        logger.error(log_entry)

    # 保存专利证书提取日志到 TXT 文件
    log_filename = f"专利证书提取日志-{timestamp}.txt"
    log_file_path = os.path.join(separate_folder, log_filename)
    with open(log_file_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(cert_log))
    logger.info(f"专利证书提取日志保存完成: {log_file_path}")

    return output_files, unmatched_patents, cert_log

@app.route('/parse_rdps', methods=['POST'])
@login_required
def parse_rdps():
    """解析RDPS表，返回项目信息用于自动填充批量报告表单"""
    try:
        if 'rdps_file' not in request.files:
            return jsonify({'error': '未上传RDPS表文件'}), 400
        
        rdps_file = request.files['rdps_file']
        if not rdps_file or rdps_file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        # 检查文件类型
        if not rdps_file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({'error': '请上传Excel格式的RDPS表'}), 400
        
        # 提取项目信息
        project_info_list = extract_project_info_from_rdps(rdps_file)
        
        if not project_info_list:
            return jsonify({'error': '未能从RDPS表中提取到项目信息，请检查表格格式'}), 400
        
        logger.info(f"成功解析RDPS表，提取到{len(project_info_list)}个项目")
        
        return jsonify({
            'success': True,
            'projects': project_info_list,
            'message': f'成功解析到{len(project_info_list)}个项目'
        })
        
    except Exception as e:
        logger.error(f"解析RDPS表失败: {e}")
        return jsonify({'error': f'解析RDPS表失败: {str(e)}'}), 500

@app.route('/achievements', methods=['POST'])
@login_required
def achievements():
    form_data = request.form.to_dict()
    files_data = request.files

    logger.debug(f"Received form data: {form_data}")
    logger.debug(f"Received files: {list(files_data.keys())}")

    company_name = form_data.get('company_name', '').strip()

    # 获取用户指定的保存路径
    user_output_path = form_data.get('output_path', '').strip()

    output_path = form_data.get('output_path', '').strip()
    diagnostic_file = files_data.get('diagnostic_file')
    tech_files_zip = files_data.get('tech_files_zip')
    patent_cert_zip = files_data.get('patent_cert_zip')  # 新增专利证书 ZIP 文件

    # 创建临时目录用于存储生成的文件
    temp_dir = tempfile.mkdtemp()
    try:
        # 使用临时目录作为输出路径
        output_path = temp_dir

        try:
            revenue_2024 = float(form_data['revenue_2024']) if form_data.get('revenue_2024') else None
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing revenue_2024: {e}")
            revenue_2024 = None

        try:
            percentage_2024 = float(form_data['percentage_2024']) if form_data.get('percentage_2024') else None
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing percentage_2024: {e}")
            percentage_2024 = None

        total_steps = 10  # 增加一步用于专利证书提取
        step_counter = [0]

        if not company_name:
            error_msg = log_and_emit_error('invalid_data', '检查公司名称')
            return error_msg, 400
        if not diagnostic_file:
            error_msg = log_and_emit_error('file_upload', '检查RD表文件')
            return error_msg, 400

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "开始处理成果材料"})

        try:
            df = pd.read_excel(diagnostic_file, sheet_name=0)
            logger.debug(f"RD table data: \n{df.head().to_string()}")
            step_counter[0] += 1
            progress = int((step_counter[0] / total_steps) * 100)
            socketio.emit('progress', {'progress': progress, 'message': "已读取研发诊断表"})
        except Exception as e:
            error_msg = log_and_emit_error('excel_read', '读取研发诊断表', e)
            return error_msg, 400

        patent_files = []
        if tech_files_zip:
            try:
                tech_files_zip.seek(0)
                zip_content = io.BytesIO(tech_files_zip.read())
                with zipfile.ZipFile(zip_content, 'r') as zip_ref:
                    patent_files = [
                        f for f in zip_ref.namelist()
                        if re.search(r'\d{12}\.\d\.(pdf|doc|docx)$|(20\d{2}SR\d{7})\.(pdf|doc|docx)$', f, re.IGNORECASE)
                    ]
                logger.debug(f"Extracted patent files: {patent_files}")
                step_counter[0] += 1
                progress = int((step_counter[0] / total_steps) * 100)
                socketio.emit('progress', {'progress': progress, 'message': "已处理技术文件 ZIP 压缩包"})
            except zipfile.BadZipFile as e:
                error_msg = log_and_emit_error('zip_extract', '处理技术文件ZIP', e)
                return error_msg, 400
            except Exception as e:
                error_msg = log_and_emit_error('file_processing', '处理技术文件ZIP', e)
                return error_msg, 400

        patent_cert_files = []
        if patent_cert_zip:
            try:
                patent_cert_zip.seek(0)
                zip_content = io.BytesIO(patent_cert_zip.read())
                with zipfile.ZipFile(zip_content, 'r') as zip_ref:
                    patent_cert_files = [
                        f for f in zip_ref.namelist()
                        if re.search(r'\d{12}\.\d\.pdf$|(20\d{2}SR\d{7})\.pdf$', f, re.IGNORECASE)
                    ]
                logger.debug(f"Extracted patent certificate files: {patent_cert_files}")
                step_counter[0] += 1
                progress = int((step_counter[0] / total_steps) * 100)
                socketio.emit('progress', {'progress': progress, 'message': "已处理专利证书 ZIP 压缩包"})
            except zipfile.BadZipFile as e:
                error_msg = log_and_emit_error('zip_extract', '处理专利证书ZIP', e)
                return error_msg, 400
            except Exception as e:
                error_msg = log_and_emit_error('file_processing', '处理专利证书ZIP', e)
                return error_msg, 400

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在生成成果材料表"})
        result, status, ps_name_ranges, achievements_df = save_achievements_table(df, company_name, patent_files, output_path)
        if status != 200:
            error_msg = log_and_emit_error('file_processing', '生成成果材料表', ValueError(result))
            return error_msg, 400

        pdf_abstracts = {}
        if tech_files_zip and patent_files:
            try:
                tech_files_zip.seek(0)
                zip_content = io.BytesIO(tech_files_zip.read())
                with zipfile.ZipFile(zip_content, 'r') as zip_ref:
                    for file_name in patent_files:
                        match = re.search(r'(\d{12}\.\d)|(20\d{2}SR\d{7})', file_name)
                        if match:
                            patent_number = match.group(1) if match.group(1) else match.group(2)
                            with zip_ref.open(file_name) as file:
                                if file_name.lower().endswith('.pdf'):
                                    try:
                                        pdf_reader = pypdf.PdfReader(file)
                                        text = ""
                                        for page in pdf_reader.pages:
                                            extracted_text = page.extract_text() or ""
                                            text += extracted_text
                                        abstract_content = ""
                                        if not text or len(text) < 100 or "摘要" not in text:
                                            logger.warning(f"PDF 文件 {file_name} 提取文本不足或未找到摘要，尝试使用 API 重新解析")
                                            file.seek(0)
                                            # 增强的文件内容获取功能，添加重试机制和错误处理
                                            file_id = None
                                            abstract_content = ""
                                            max_retries = 3
                                            retry_count = 0

                                            # 尝试使用官方SDK方法获取文件内容
                                            while retry_count < max_retries:
                                                try:
                                                    logger.info(f"尝试上传文件 {file_name}，第 {retry_count + 1} 次尝试")
                                                    file.seek(0)
                                                    response = client.files().create(file=(file_name, file), purpose="assistants")
                                                    file_id = response.get('id')
                                                    if file_id:
                                                        logger.info(f"文件 {file_name} 上传成功，文件ID: {file_id}")
                                                        break
                                                    else:
                                                        logger.warning(f"文件 {file_name} 上传返回无效ID，尝试重试")
                                                        retry_count += 1
                                                        time.sleep(1)  # 等待1秒后重试
                                                except Exception as e:
                                                    logger.error(f"文件 {file_name} 上传失败: {e}，尝试重试")
                                                    retry_count += 1
                                                    time.sleep(1)  # 等待1秒后重试

                                            if not file_id:
                                                # 如果SDK方法失败，尝试使用直接HTTP请求
                                                try:
                                                    logger.info(f"SDK方法失败，尝试使用HTTP请求上传文件 {file_name}")
                                                    file.seek(0)
                                                    file_content = file.read()

                                                    # 使用requests直接发送HTTP请求
                                                    url = "https://api.moonshot.cn/v1/files"
                                                    headers = {"Authorization": f"Bearer {API_KEY}"}
                                                    files = {"file": (file_name, file_content), "purpose": "assistants"}

                                                    http_response = requests.post(url, headers=headers, files=files, timeout=30)
                                                    if http_response.status_code == 200:
                                                        response_data = http_response.json()
                                                        file_id = response_data.get('id')
                                                        logger.info(f"HTTP请求上传文件成功，文件ID: {file_id}")
                                                    else:
                                                        logger.error(f"HTTP请求上传文件失败: {http_response.status_code} - {http_response.text}")
                                                except Exception as e:
                                                    logger.error(f"HTTP请求上传文件失败: {e}")

                                            if not file_id:
                                                logger.error(f"文件 {file_name} 上传失败，跳过")
                                                pdf_abstracts[patent_number] = ""
                                                continue

                                            # 获取文件内容
                                            retry_count = 0
                                            while retry_count < max_retries:
                                                try:
                                                    prompt = (
                                                        "请从以下 PDF 文件内容中提取'摘要'部分，从'摘要'后的第 57 个字符开始，"
                                                        "直到第一个双换行符（\\n\\n）或文件末尾。如果没有'摘要'，返回空字符串。"
                                                    )
                                                    response = client.chat().create_completion(
                                                        model="moonshot-v1-32k",
                                                        messages=[
                                                            {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长中文文本提取。"},
                                                            {"role": "user", "content": prompt},
                                                            {"role": "system", "content": f"文件 ID: {file_id}"}
                                                        ],
                                                        temperature=0.3,
                                                        timeout=60  # 增加超时时间到60秒
                                                    )
                                                    abstract_content = response['choices'][0]['message']['content'].strip()
                                                    logger.info(f"成功获取文件 {file_name} 的内容")
                                                    break
                                                except requests.exceptions.Timeout:
                                                    logger.warning(f"获取文件内容超时，第 {retry_count + 1} 次尝试")
                                                    retry_count += 1
                                                    time.sleep(2)  # 等待2秒后重试
                                                except Exception as e:
                                                    logger.error(f"获取文件内容失败: {e}，第 {retry_count + 1} 次尝试")
                                                    retry_count += 1
                                                    time.sleep(2)  # 等待2秒后重试

                                            # 如果仍然失败，尝试使用直接HTTP请求获取文件内容
                                            if not abstract_content and file_id:
                                                try:
                                                    logger.info(f"尝试使用HTTP请求获取文件 {file_id} 的内容")
                                                    url = "https://api.moonshot.cn/v1/chat/completions"
                                                    headers = {
                                                        "Authorization": f"Bearer {API_KEY}",
                                                        "Content-Type": "application/json"
                                                    }
                                                    data = {
                                                        "model": "moonshot-v1-32k",
                                                        "messages": [
                                                            {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长中文文本提取。"},
                                                            {"role": "user", "content": prompt},
                                                            {"role": "system", "content": f"文件 ID: {file_id}"}
                                                        ],
                                                        "temperature": 0.3
                                                    }
                                                    http_response = requests.post(url, headers=headers, json=data, timeout=60)
                                                    if http_response.status_code == 200:
                                                        response_data = http_response.json()
                                                        abstract_content = response_data['choices'][0]['message']['content'].strip()
                                                        logger.info(f"HTTP请求获取文件内容成功")
                                                    else:
                                                        logger.error(f"HTTP请求获取文件内容失败: {http_response.status_code} - {http_response.text}")
                                                except Exception as e:
                                                    logger.error(f"HTTP请求获取文件内容失败: {e}")
                                            logger.info(f"专利号 {patent_number} 使用 API 提取摘要成功")
                                        else:
                                            abstract_match = re.search(r'摘要', text)
                                            if abstract_match:
                                                start_pos = abstract_match.start()
                                                abstract_text = text[start_pos + 57:]
                                                end_match = re.search(r'\n\n|\r\n\r\n|$', abstract_text)
                                                if end_match:
                                                    abstract_content = abstract_text[:end_match.start()].strip()
                                                else:
                                                    abstract_content = abstract_text.strip()
                                                logger.info(f"专利号 {patent_number} 摘要提取成功")
                                            else:
                                                abstract_content = ""
                                                logger.warning(f"PDF 文件 {file_name} 未找到 '摘要'")
                                        if abstract_content:
                                            tech_name = achievements_df[achievements_df['备注'].str.replace('ZL', '') == patent_number]['科技成果名称'].iloc[0] if patent_number in achievements_df['备注'].str.replace('ZL', '') else "该技术"
                                            prompt = (
                                                f"请根据以下摘要内容，撰写一段严格控制在110字以内的描述，必须以'通过{tech_name}的应用，达到了'开头，后接具体效果：\n"
                                                f"摘要内容：{abstract_content}\n"
                                                "描述需突出技术应用效果，语言简洁流畅，适合正式报告。"
                                            )
                                            response = client.chat().create_completion(
                                                model="moonshot-v1-32k",
                                                messages=[
                                                    {"role": "system", "content": "你是 Kimi，由 Moonshot AI 提供的人工智能助手，擅长撰写正式报告内容。"},
                                                    {"role": "user", "content": prompt}
                                                ],
                                                temperature=0.3
                                            )
                                            effect_description = response['choices'][0]['message']['content'].strip()
                                            pdf_abstracts[patent_number] = effect_description
                                            logger.info(f"专利号 {patent_number} 生成效果描述成功: {effect_description}")
                                        else:
                                            pdf_abstracts[patent_number] = ""
                                            logger.warning(f"专利号 {patent_number} 无摘要内容，效果描述为空")
                                    except Exception as e:
                                        pdf_abstracts[patent_number] = ""
                                        logger.error(f"PDF 文件 {file_name} 解析失败: {e}")
            except zipfile.BadZipFile as e:
                logger.error(f"处理专利文件摘要失败: {e}")
                pdf_abstracts = {}
            except Exception as e:
                logger.error(f"处理专利文件摘要失败: {e}")
                pdf_abstracts = {}
        logger.info(f"专利摘要提取完成，提取到的专利号和效果描述: {pdf_abstracts}")

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在生成成果转化总体情况说明"})
        summary_file_path = save_achievements_summary_docx(
            achievements_df, company_name, output_path, ps_name_ranges, revenue_2024, percentage_2024
        )

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在生成科技成果转化逐项说明"})
        details_file_path = save_achievements_details_docx(achievements_df, patent_files, company_name, output_path, ps_name_ranges)

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在生成成果隔页"})
        separate_page_files, separate_folder, timestamp = save_achievement_separate_pages(achievements_df, pdf_abstracts, ps_name_ranges, company_name, output_path)

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在提取 PDF 摘要"})
        pdf_abstract_files, unmatched_patents, abstract_log = extract_pdf_first_page(patent_files, achievements_df, output_path, separate_folder, timestamp)

        step_counter[0] += 1
        progress = int((step_counter[0] / total_steps) * 100)
        socketio.emit('progress', {'progress': progress, 'message': "正在提取专利证书 PDF"})
        pdf_cert_files, unmatched_cert_patents, cert_log = extract_patent_cert_pdf(patent_cert_files, achievements_df, output_path, separate_folder, timestamp)

        step_counter[0] += 1
        progress = 100
        socketio.emit('progress', {'progress': progress, 'message': "成果材料生成完成，准备下载"})

        # 用于收集生成的文件
        files_to_zip = {}

        # 添加成果材料表
        if os.path.exists(result):
            files_to_zip["成果材料表.xlsx"] = result

        # 添加成果转化总体情况说明
        if os.path.exists(summary_file_path):
            files_to_zip["成果转化总体情况说明.docx"] = summary_file_path

        # 添加科技成果转化逐项说明
        if os.path.exists(details_file_path):
            files_to_zip["科技成果转化逐项说明.docx"] = details_file_path

        # 添加成果隔页文件
        for file_path in separate_page_files:
            if os.path.exists(file_path):
                files_to_zip[f"成果隔页/{os.path.basename(file_path)}"] = file_path

        # 添加PDF摘要文件
        for file_path in pdf_abstract_files:
            if os.path.exists(file_path):
                files_to_zip[f"PDF摘要/{os.path.basename(file_path)}"] = file_path

        # 添加专利证书文件
        for file_path in pdf_cert_files:
            if os.path.exists(file_path):
                files_to_zip[f"专利证书/{os.path.basename(file_path)}"] = file_path

        # 添加日志文件
        abstract_log_path = os.path.join(separate_folder, f'摘要提取日志-{timestamp}.txt')
        cert_log_path = os.path.join(separate_folder, f'专利证书提取日志-{timestamp}.txt')

        if os.path.exists(abstract_log_path):
            files_to_zip["摘要提取日志.txt"] = abstract_log_path

        if os.path.exists(cert_log_path):
            files_to_zip["专利证书提取日志.txt"] = cert_log_path

        # 获取客户端IP地址
        client_ip = request.remote_addr
        is_local = is_local_ip(client_ip)

        # 如果是本地IP且用户指定了保存路径，则直接保存到指定路径
        logger.info(f"检查是否可以保存到本地路径: is_local={is_local}, user_output_path={user_output_path}")

        # 检查路径是否存在，如果不存在则尝试创建
        if is_local and user_output_path:
            if not os.path.exists(user_output_path):
                try:
                    logger.info(f"尝试创建目录: {user_output_path}")
                    os.makedirs(user_output_path, exist_ok=True)
                except Exception as e:
                    logger.error(f"创建目录失败: {e}")

            # 再次检查路径是否存在
            if os.path.isdir(user_output_path):
                try:
                    logger.info(f"开始复制文件到: {user_output_path}")
                    # 确保输出目录存在
                    os.makedirs(user_output_path, exist_ok=True)

                    # 复制所有文件到用户指定的路径
                    saved_files = []
                    for file_name, file_path in files_to_zip.items():
                        # 处理子目录
                        if '/' in file_name:
                            subdir = os.path.dirname(file_name)
                            subdir_path = os.path.join(user_output_path, subdir)
                            logger.info(f"创建子目录: {subdir_path}")
                            os.makedirs(subdir_path, exist_ok=True)

                        dest_path = os.path.join(user_output_path, file_name)
                        logger.info(f"复制文件: {file_path} -> {dest_path}")
                        shutil.copy2(file_path, dest_path)
                        saved_files.append(dest_path)

                    # 返回成功消息
                    logger.info(f"文件已成功保存到本地路径: {user_output_path}")
                    return jsonify({
                        'success': True,
                        'message': f"文件已成功保存到 {user_output_path}",
                        'files': saved_files
                    })
                except Exception as e:
                    logger.error(f"保存文件到用户指定路径失败: {e}")
                    # 如果保存失败，回退到压缩包下载
            else:
                logger.error(f"指定的路径不是有效目录: {user_output_path}")
        else:
            logger.info(f"不满足本地保存条件，将创建ZIP文件供下载")

        # 创建ZIP文件
        memory_file = create_download_zip(files_to_zip)

        # 成功创建ZIP下载包时，扣减用户报告配额
        username = session.get('username')
        if username:
            try:
                # 扣减1个报告配额（单个报告算1个）
                success = consume_user_quota(username, 1)
                if success:
                    logger.info(f"用户 {username} 生成完整报告包，扣减配额 1个")
                else:
                    logger.warning(f"用户 {username} 配额扣减失败")
            except Exception as e:
                logger.error(f"扣减用户 {username} 配额时出错: {e}")

        # 返回ZIP文件供下载
        return send_zip_file(memory_file, "项目资料")
    finally:
        # 清理临时目录
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            logger.error(f"清理临时目录失败: {e}")


def get_local_ip():
    """获取本机IP地址"""
    import socket
    try:
        # 创建一个socket连接来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def get_local_ipv6():
    """获取本机IPv6地址"""
    import socket
    try:
        # 创建IPv6 socket连接来获取本机IPv6地址
        s = socket.socket(socket.AF_INET6, socket.SOCK_DGRAM)
        s.connect(("2001:4860:4860::8888", 80))  # Google的IPv6 DNS
        local_ipv6 = s.getsockname()[0]
        s.close()
        return local_ipv6
    except:
        try:
            # 备用方法：获取主机名对应的IPv6地址
            hostname = socket.gethostname()
            ipv6_addrs = socket.getaddrinfo(hostname, None, socket.AF_INET6)
            for addr in ipv6_addrs:
                ipv6 = addr[4][0]
                if not ipv6.startswith('::1') and not ipv6.startswith('fe80'):
                    return ipv6
        except:
            pass
        return "::1"

def get_network_interfaces():
    """获取所有网络接口IP地址（IPv4和IPv6）"""
    import socket
    interfaces = []
    try:
        # 尝试导入netifaces模块
        import netifaces
        # 获取所有网络接口
        for interface in netifaces.interfaces():
            addrs = netifaces.ifaddresses(interface)
            interface_ips = []
            
            # 获取IPv4地址
            if netifaces.AF_INET in addrs:
                for addr in addrs[netifaces.AF_INET]:
                    ip = addr['addr']
                    if ip != '127.0.0.1' and not ip.startswith('169.254'):
                        interface_ips.append(f"IPv4: {ip}")
            
            # 获取IPv6地址
            if netifaces.AF_INET6 in addrs:
                for addr in addrs[netifaces.AF_INET6]:
                    ip = addr['addr']
                    # 过滤掉本地回环和链路本地地址
                    if not ip.startswith('::1') and not ip.startswith('fe80') and '%' not in ip:
                        interface_ips.append(f"IPv6: {ip}")
            
            # 如果接口有IP地址，添加到列表
            if interface_ips:
                interfaces.append(f"{interface}: {', '.join(interface_ips)}")
                
    except ImportError:
        # 如果netifaces未安装，使用简单方法
        try:
            hostname = socket.gethostname()
            # 获取IPv4地址
            local_ip = socket.gethostbyname(hostname)
            interfaces.append(f"主机 IPv4: {local_ip}")
            
            # 尝试获取IPv6地址
            try:
                ipv6_addrs = socket.getaddrinfo(hostname, None, socket.AF_INET6)
                for addr in ipv6_addrs:
                    ipv6 = addr[4][0]
                    if not ipv6.startswith('::1') and not ipv6.startswith('fe80') and '%' not in ipv6:
                        interfaces.append(f"主机 IPv6: {ipv6}")
                        break
            except:
                pass
        except:
            interfaces.append("主机: 127.0.0.1")
    except Exception as e:
        # 其他异常处理
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            interfaces.append(f"主机 IPv4: {local_ip}")
        except:
            interfaces.append("主机: 127.0.0.1")
    
    return interfaces

def display_startup_info():
    """显示服务器启动信息"""
    import datetime
    
    print("\n" + "="*60)
    print("🚀 Flask 服务器启动信息")
    print("="*60)
    
    # 服务器基本信息
    print(f"📅 启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 服务器端口: 9967")
    print(f"🔧 调试模式: 关闭")
    print(f"⚡ Socket.IO: 已启用")
    
    # 网络信息
    print(f"\n🌍 网络访问地址:")
    local_ip = get_local_ip()
    local_ipv6 = get_local_ipv6()
    
    print(f"   本机访问: http://127.0.0.1:9967")
    if local_ip != "127.0.0.1":
        print(f"   局域网IPv4: http://{local_ip}:9967")
    if local_ipv6 != "::1":
        print(f"   局域网IPv6: http://[{local_ipv6}]:9967")
    
    # 网络接口信息
    interfaces = get_network_interfaces()
    if interfaces:
        print(f"\n🖥️  网络接口:")
        for interface in interfaces:
            print(f"   {interface}")
    
    # 服务状态
    print(f"\n📊 服务器状态:")
    print(f"   状态: 🟢 运行中")
    print(f"   协议: HTTP + WebSocket")
    print(f"   主机: :: (IPv6 通配符)")
    
    print("\n💡 使用说明:")
    print("   - 本地访问请使用: http://127.0.0.1:9967")
    print("   - 局域网访问请使用上述局域网地址")
    print("   - 按 Ctrl+C 停止服务器")
    
    print("="*60)
    print("✅ 服务器已启动，等待连接...")
    print("="*60 + "\n")

# === 管理员功能部分 ===

# 会话管理功能
def create_session_id():
    """生成唯一会话ID"""
    return hashlib.md5(f"{datetime.now()}{os.urandom(16)}".encode()).hexdigest()

def update_session_activity(username, session_id):
    """更新会话活动时间"""
    # 先检查管理员会话
    if session_id in admin_sessions:
        admin_sessions[session_id]['last_activity'] = datetime.now()
        admin_sessions[session_id]['ip'] = request.remote_addr
    # 再检查普通用户会话
    elif session_id in active_sessions:
        active_sessions[session_id]['last_activity'] = datetime.now()
        active_sessions[session_id]['ip'] = request.remote_addr
    else:
        # 如果会话不存在，记录警告但不创建新会话
        logger.warning(f"尝试更新不存在的会话: 用户={username}, 会话ID={session_id[:8] if session_id else 'None'}...")
        
def create_session_record(username, session_id, is_admin=False):
    """创建新的会话记录（仅在登录时使用）"""
    current_time = datetime.now()
    session_data = {
        'username': username,
        'last_activity': current_time,
        'created': current_time,
        'ip': request.remote_addr
    }
    
    if is_admin:
        # 管理员会话管理
        # 先清理该管理员的所有现有会话
        sessions_to_remove = []
        for sid, session_info in admin_sessions.items():
            if session_info['username'] == username:
                sessions_to_remove.append(sid)
        
        for sid in sessions_to_remove:
            del admin_sessions[sid]
            logger.info(f"清理管理员 {username} 的旧会话: {sid[:8]}...")
        
        # 创建新管理员会话记录
        admin_sessions[session_id] = session_data
        logger.info(f"为管理员 {username} 创建新会话: {session_id[:8]}...")
    else:
        # 普通用户会话管理
        # 先清理该用户的所有现有会话
        sessions_to_remove = []
        for sid, session_info in active_sessions.items():
            if session_info['username'] == username:
                sessions_to_remove.append(sid)
        
        for sid in sessions_to_remove:
            del active_sessions[sid]
            logger.info(f"清理用户 {username} 的旧会话: {sid[:8]}...")
        
        # 创建新会话记录
        active_sessions[session_id] = session_data
        logger.info(f"为用户 {username} 创建新会话: {session_id[:8]}...")

def check_session_timeout():
    """检查会话超时"""
    current_time = datetime.now()
    expired_sessions = []
    expired_admin_sessions = []
    
    # 检查普通用户会话超时
    for session_id, session_info in active_sessions.items():
        if (current_time - session_info['last_activity']).seconds > SESSION_TIMEOUT * 60:
            expired_sessions.append(session_id)
    
    # 检查管理员会话超时（使用更长的超时时间）
    for session_id, session_info in admin_sessions.items():
        if (current_time - session_info['last_activity']).seconds > (SESSION_TIMEOUT * 2) * 60:  # 管理员超时时间加倍
            expired_admin_sessions.append(session_id)
    
    # 清理过期的普通用户会话
    for session_id in expired_sessions:
        username = active_sessions[session_id]['username']
        # 更新用户状态为离线
        users = load_users()
        if username in users:
            users[username]['status'] = 'offline'
            save_users(users)
        del active_sessions[session_id]
    
    # 清理过期的管理员会话
    for session_id in expired_admin_sessions:
        del admin_sessions[session_id]

def check_rate_limit(ip):
    """检查API频率限制"""
    current_time = datetime.now()
    minute_ago = current_time - timedelta(minutes=1)
    
    # 清理过期记录
    rate_limits[ip] = [t for t in rate_limits[ip] if t > minute_ago]
    
    # 检查是否超过限制
    if len(rate_limits[ip]) >= API_RATE_LIMIT:
        return False
    
    # 记录请求
    rate_limits[ip].append(current_time)
    return True

def check_login_attempts(ip):
    """检查登录尝试次数"""
    # 为本地IP提供登录豁免
    if is_local_ip(ip):
        return True
    
    current_time = datetime.now()
    block_time_ago = current_time - timedelta(minutes=LOGIN_BLOCK_TIME)
    
    # 清理过期记录
    if ip in login_attempts:
        login_attempts[ip] = [t for t in login_attempts[ip] if t > block_time_ago]
    else:
        login_attempts[ip] = []
    
    # 检查是否被封锁
    return len(login_attempts[ip]) < MAX_LOGIN_ATTEMPTS

def record_login_attempt(ip):
    """记录登录尝试"""
    # 为本地IP不记录登录失败
    if is_local_ip(ip):
        return
    
    if ip not in login_attempts:
        login_attempts[ip] = []
    login_attempts[ip].append(datetime.now())

def get_login_attempts_count(ip):
    """获取当前IP的登录失败次数"""
    if is_local_ip(ip):
        return {'current_attempts': 0, 'remaining_attempts': MAX_LOGIN_ATTEMPTS, 'is_blocked': False}
    
    current_time = datetime.now()
    block_time_ago = current_time - timedelta(minutes=LOGIN_BLOCK_TIME)
    
    # 清理过期记录
    if ip in login_attempts:
        login_attempts[ip] = [t for t in login_attempts[ip] if t > block_time_ago]
    else:
        login_attempts[ip] = []
    
    current_attempts = len(login_attempts[ip])
    remaining_attempts = max(0, MAX_LOGIN_ATTEMPTS - current_attempts)
    is_blocked = current_attempts >= MAX_LOGIN_ATTEMPTS
    
    return {
        'current_attempts': current_attempts,
        'remaining_attempts': remaining_attempts,
        'is_blocked': is_blocked
    }

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查管理员登录状态
        if not session.get('admin_logged_in'):
            # 如果是API请求，返回JSON错误
            if request.path and '/api/' in request.path:
                return jsonify({'error': '管理员权限验证失败', 'redirect': f'/{ADMIN_PATH}/login'}), 401
            return redirect(url_for('admin_login'))
        
        # 检查管理员会话是否有效
        session_id = session.get('admin_session_id')
        if session_id and session_id in admin_sessions:
            # 更新管理员活动时间（减少频率）
            try:
                current_time = datetime.now()
                last_activity = admin_sessions[session_id].get('last_activity', datetime.min)
                if (current_time - last_activity).total_seconds() > 300:  # 5分钟更新一次
                    admin_sessions[session_id]['last_activity'] = current_time
            except Exception as e:
                logger.error(f"更新管理员会话活动失败: {e}")
        elif session_id:
            # 管理员会话无效，清除会话
            logger.warning(f"管理员会话无效: {session_id[:8]}...")
            session.clear()
            if request.path and '/api/' in request.path:
                return jsonify({'error': '管理员会话已过期', 'redirect': f'/{ADMIN_PATH}/login'}), 401
            return redirect(url_for('admin_login'))
        
        return f(*args, **kwargs)
    return decorated_function

def rate_limit_required(f):
    """API频率限制装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not check_rate_limit(request.remote_addr):
            return jsonify({'error': '请求过于频繁，请稍后重试'}), 429
        return f(*args, **kwargs)
    return decorated_function

def log_user_error(username, operation, error_message, ip_address, original_error=None):
    """记录用户错误日志"""
    try:
        users = load_users()
        if username in users:
            # 构建详细的错误信息
            detailed_error = error_message
            if original_error:
                detailed_error += f" | 原始API错误: {str(original_error)}"
            
            error_log = {
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'operation': operation,
                'error': detailed_error,
                'ip': ip_address
            }
            if 'error_logs' not in users[username]:
                users[username]['error_logs'] = []
            users[username]['error_logs'].append(error_log)
            # 只保留最近50条错误日志
            users[username]['error_logs'] = users[username]['error_logs'][-50:]
            save_users(users)
    except Exception as e:
        logger.error(f"记录用户错误日志失败: {e}")

def check_user_permission(username, permission_type):
    """检查用户权限"""
    try:
        users = load_users()
        if username in users:
            return permission_type in users[username].get('permissions', [])
        # 用户不存在，返回False
        return False
    except Exception as e:
        logger.error(f"检查用户权限失败: {e}")
        return False

def check_user_quota(username):
    """检查用户报告额度"""
    try:
        users = load_users()
        if username in users:
            user_data = users[username]
            total_quota = user_data.get('report_quota', 0)
            used_reports = user_data.get('used_reports', 0)
            remaining = total_quota - used_reports
            return {
                'has_quota': remaining > 0,
                'total': total_quota,
                'used': used_reports,
                'remaining': remaining
            }
        return {'has_quota': False, 'total': 0, 'used': 0, 'remaining': 0}
    except Exception as e:
        logger.error(f"检查用户额度失败: {e}")
        return {'has_quota': False, 'total': 0, 'used': 0, 'remaining': 0}

def consume_user_quota(username, count=1):
    """消耗用户报告额度"""
    try:
        users = load_users()
        if username in users:
            user_data = users[username]
            current_used = user_data.get('used_reports', 0)
            user_data['used_reports'] = current_used + count
            save_users(users)
            return True
        return False
    except Exception as e:
        logger.error(f"消耗用户额度失败: {e}")
        return False

def get_user_info(username):
    """获取用户信息"""
    try:
        users = load_users()
        if username in users:
            user_data = users[username]
            quota_info = check_user_quota(username)
            return {
                'username': username,
                'permissions': user_data.get('permissions', []),
                'quota_info': quota_info,
                'has_single_permission': 'single' in user_data.get('permissions', []),
                'has_batch_permission': 'batch' in user_data.get('permissions', []),
                'has_achievement_permission': 'achievement' in user_data.get('permissions', []),
                'last_login_time': user_data.get('last_login_time', '从未登录'),
                'last_login_ip': user_data.get('last_login_ip', '未知'),
                'status': user_data.get('status', 'offline')
            }
        return None
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return None

# 管理员路由
@app.route(f'/{ADMIN_PATH}')
@admin_required
def admin_dashboard():
    """管理员仪表板"""
    with open('admin.html', encoding='utf-8') as f:
        html_content = f.read()
    
    # 注入管理员路径到HTML中
    html_content = html_content.replace('{{ admin_path or "admin" }}', ADMIN_PATH)
    
    return render_template_string(html_content, admin_path=ADMIN_PATH)

# 管理员登录页面
@app.route(f'/{ADMIN_PATH}/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        # 检查登录尝试次数
        if not check_login_attempts(request.remote_addr):
            return jsonify({'error': f'登录尝试过多，请{LOGIN_BLOCK_TIME}分钟后重试'}), 429
        
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == app.config['ADMIN_USERNAME'] and password == app.config['ADMIN_PASSWORD']:
            admin_session_id = create_session_id()
            session['admin_logged_in'] = True
            session['admin_login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            session['admin_session_id'] = admin_session_id
            session.permanent = False
            
            # 创建管理员会话记录
            create_session_record('admin', admin_session_id, is_admin=True)
            return redirect(url_for('admin_dashboard'))
        else:
            # 记录失败的登录尝试
            record_login_attempt(request.remote_addr)
            
            # 获取当前失败次数信息
            attempts_info = get_login_attempts_count(request.remote_addr)
            
            # 构建错误信息
            if attempts_info['remaining_attempts'] > 0:
                error_msg = f"用户名或密码错误。您还有 {attempts_info['remaining_attempts']} 次尝试机会。"
            else:
                error_msg = f"登录失败次数过多，请{LOGIN_BLOCK_TIME}分钟后重试。"
            
            return render_template_string(f'''
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>管理员登录</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }}
                    .login-form {{ max-width: 400px; margin: 100px auto; padding: 40px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                    input {{ width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }}
                    button {{ width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }}
                    button:hover {{ background: #0056b3; }}
                    .error {{ color: red; margin-bottom: 10px; font-size: 14px; }}
                    .warning {{ color: #ff8c00; margin-bottom: 10px; font-size: 12px; }}
                    h2 {{ text-align: center; color: #333; }}
                </style>
            </head>
            <body>
                <div class="login-form">
                    <h2>管理员登录</h2>
                    <div class="error">{error_msg}</div>
                    <div class="warning">已失败 {attempts_info['current_attempts']} 次，最多允许 {MAX_LOGIN_ATTEMPTS} 次</div>
                    <form method="post">
                        <input type="text" name="username" placeholder="管理员用户名" required>
                        <input type="password" name="password" placeholder="密码" required>
                        <button type="submit">登录</button>
                    </form>
                    <p style="text-align: center; margin-top: 20px;">
                        <a href="/login">返回普通用户登录</a>
                    </p>
                </div>
            </body>
            </html>
            ''')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>管理员登录</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
            .login-form { max-width: 400px; margin: 100px auto; padding: 40px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            input { width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
            button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #0056b3; }
            h2 { text-align: center; color: #333; }
        </style>
    </head>
    <body>
        <div class="login-form">
            <h2>管理员登录</h2>
            <form method="post">
                <input type="text" name="username" placeholder="管理员用户名" required>
                <input type="password" name="password" placeholder="密码" required>
                <button type="submit">登录</button>
            </form>
            <p style="text-align: center; margin-top: 20px;">
                <a href="{{ url_for('login') }}">返回普通用户登录</a>
            </p>
        </div>
    </body>
    </html>
    ''')

@app.route(f'/{ADMIN_PATH}/logout')
def admin_logout():
    # 清理管理员会话记录
    session_id = session.get('admin_session_id')
    if session_id and session_id in admin_sessions:
        del admin_sessions[session_id]
        logger.info(f"管理员退出，会话ID: {session_id[:8]}...")
    
    session.clear()
    response = make_response(redirect(url_for('admin_login')))
    response.delete_cookie('session')
    return response

# 管理员API路由
@app.route(f'/{ADMIN_PATH}/api/system_stats')
@admin_required
def get_system_stats_api():
    stats = get_system_stats()
    return jsonify(stats)

@app.route(f'/{ADMIN_PATH}/api/users')
@admin_required
@rate_limit_required
def get_users_api():
    try:
        users = load_users()
        user_list = []
        for username, user_data in users.items():
            # 计算真实的在线状态：检查内存中的活跃会话
            user_sessions = [sid for sid, info in active_sessions.items() 
                           if info.get('username') == username]
            is_online = len(user_sessions) > 0
            
            user_info = {
                'username': username,
                'last_login_ip': user_data.get('last_login_ip', '未知'),
                'last_login_time': user_data.get('last_login_time', '从未登录'),
                'permissions': user_data.get('permissions', []),
                'report_quota': user_data.get('report_quota', 0),
                'used_reports': user_data.get('used_reports', 0),
                'remaining_reports': user_data.get('report_quota', 0) - user_data.get('used_reports', 0),
                'total_generated': user_data.get('used_reports', 0),
                'status': 'online' if is_online else 'offline',
                'active_sessions': len(user_sessions),
                'error_count': len(user_data.get('error_logs', []))
            }
            user_list.append(user_info)
        return jsonify(user_list)
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return jsonify({'error': '获取用户列表失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/user/<username>')
@admin_required
@rate_limit_required
def get_user_api(username):
    """获取单个用户的详细信息"""
    users = load_users()
    if username not in users:
        return jsonify({'success': False, 'message': '用户不存在'}), 404
    
    user_data = users[username]
    user_info = {
        'username': username,
        'password': user_data.get('password', ''),  # 编辑时需要密码信息
        'last_login_ip': user_data.get('last_login_ip', '未知'),
        'last_login_time': user_data.get('last_login_time', '从未登录'),
        'permissions': user_data.get('permissions', []),
        'report_quota': user_data.get('report_quota', 0),
        'used_reports': user_data.get('used_reports', 0),
        'remaining_reports': user_data.get('report_quota', 0) - user_data.get('used_reports', 0),
        'total_generated': user_data.get('used_reports', 0),
        'status': user_data.get('status', 'offline')
    }
    return jsonify(user_info)

@app.route(f'/{ADMIN_PATH}/api/add_user', methods=['POST'])
@admin_required
@rate_limit_required
def add_user_api():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    permissions = data.get('permissions', [])
    report_quota = int(data.get('report_quota', 0))
    
    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400
    
    users = load_users()
    if username in users:
        return jsonify({'success': False, 'message': '用户名已存在'}), 400
    
    users[username] = {
        'password': password,
        'permissions': permissions,
        'report_quota': report_quota,
        'used_reports': 0,
        'last_login_ip': '',
        'last_login_time': '',
        'status': 'offline',
        'error_logs': []
    }
    
    if save_users(users):
        return jsonify({'success': True, 'message': '用户添加成功'})
    else:
        return jsonify({'success': False, 'message': '保存用户数据失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/edit_user', methods=['POST'])
@admin_required
def edit_user_api():
    data = request.get_json()
    username = data.get('username')
    
    users = load_users()
    if username not in users:
        return jsonify({'success': False, 'message': '用户不存在'}), 404
    
    # 更新用户数据
    if data.get('password'):
        users[username]['password'] = data.get('password')
    if 'permissions' in data:
        users[username]['permissions'] = data.get('permissions', [])
    if 'report_quota' in data:
        users[username]['report_quota'] = int(data.get('report_quota', 0))
    
    if save_users(users):
        return jsonify({'success': True, 'message': '用户信息更新成功'})
    else:
        return jsonify({'success': False, 'message': '保存用户数据失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/delete_user', methods=['POST'])
@admin_required
@rate_limit_required
def delete_user_api():
    data = request.get_json()
    username = data.get('username')
    
    if not username:
        return jsonify({'success': False, 'message': '用户名不能为空'}), 400
    
    users = load_users()
    if username not in users:
        return jsonify({'success': False, 'message': '用户不存在'}), 404
    
    # 删除用户前，清理该用户的所有会话
    sessions_to_remove = []
    for session_id, session_info in active_sessions.items():
        if session_info['username'] == username:
            sessions_to_remove.append(session_id)
    
    for session_id in sessions_to_remove:
        del active_sessions[session_id]
    
    del users[username]
    
    if save_users(users):
        logger.info(f"用户 {username} 已被删除，同时清理了 {len(sessions_to_remove)} 个活跃会话")
        return jsonify({'success': True, 'message': '用户删除成功'})
    else:
        return jsonify({'success': False, 'message': '保存用户数据失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/clear_user_reports', methods=['POST'])
@admin_required
@rate_limit_required
def clear_user_reports_api():
    try:
        data = request.get_json()
        username = data.get('username')
        
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'}), 400
        
        users = load_users()
        if username not in users:
            return jsonify({'success': False, 'message': '用户不存在'}), 404
        
        # 记录清除前的数量用于日志
        old_used_reports = users[username].get('used_reports', 0)
        
        # 清除已使用报告数量
        users[username]['used_reports'] = 0
        
        if save_users(users):
            logger.info(f"管理员清除用户 {username} 的已使用报告：从 {old_used_reports} 重置为 0")
            return jsonify({'success': True, 'message': f'用户 {username} 的已使用报告已清除（原:{old_used_reports}→现:0）'})
        else:
            return jsonify({'success': False, 'message': '保存用户数据失败'}), 500
    
    except Exception as e:
        logger.error(f"清除用户报告失败: {str(e)}")
        return jsonify({'success': False, 'message': '清除用户报告失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/clear_login_attempts', methods=['POST'])
@admin_required
def clear_login_attempts_api():
    """清除所有IP的登录尝试记录"""
    try:
        global login_attempts
        cleared_count = len(login_attempts)
        login_attempts.clear()
        
        logger.info(f"管理员清除了所有登录尝试记录，共清除 {cleared_count} 个IP的记录")
        return jsonify({'success': True, 'message': f'已清除所有登录限制，共影响 {cleared_count} 个IP'})
    
    except Exception as e:
        logger.error(f"清除登录尝试记录失败: {str(e)}")
        return jsonify({'success': False, 'message': '清除登录限制失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/error_logs')
@admin_required
def get_error_logs_api():
    users = load_users()
    all_logs = []
    log_type = request.args.get('type', 'all')  # all, security, user
    
    for username, user_data in users.items():
        user_logs = user_data.get('error_logs', [])
        for log in user_logs:
            log_entry = {
                'time': log.get('time', ''),
                'username': username,
                'operation': log.get('operation', ''),
                'error': log.get('error', ''),
                'ip': log.get('ip', ''),
                'type': 'user'  # 默认为用户日志
            }
            
            # 根据操作类型判断日志分类
            operation = log.get('operation', '').lower()
            if any(keyword in operation for keyword in ['登录', '会话', '权限', '认证', '授权', '强制下线', '超时', '密码']):
                log_entry['type'] = 'security'
            elif any(keyword in operation for keyword in ['报告生成', '文件上传', '数据处理', '导出', '解析', '下载']):
                log_entry['type'] = 'user'
            else:
                log_entry['type'] = 'system'
            
            all_logs.append(log_entry)
    
    # 根据请求的类型过滤日志
    if log_type == 'security':
        all_logs = [log for log in all_logs if log['type'] == 'security']
    elif log_type == 'user':
        all_logs = [log for log in all_logs if log['type'] == 'user']
    elif log_type == 'system':
        all_logs = [log for log in all_logs if log['type'] == 'system']
    
    # 按时间倒序排列
    all_logs.sort(key=lambda x: x['time'], reverse=True)
    return jsonify(all_logs[:100])  # 只返回最近100条

@app.route(f'/{ADMIN_PATH}/api/backup', methods=['POST'])
@admin_required
@rate_limit_required
def create_backup_api():
    try:
        backup_file = backup_users_data()
        if backup_file:
            return jsonify({'success': True, 'message': '备份创建成功', 'filename': os.path.basename(backup_file)})
        else:
            return jsonify({'success': False, 'message': '备份创建失败'}), 500
    except Exception as e:
        logger.error(f"创建备份失败: {e}")
        return jsonify({'success': False, 'message': '备份创建失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/backups')
@admin_required
def list_backups_api():
    try:
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            return jsonify([])
        
        backups = []
        for filename in os.listdir(backup_dir):
            if filename.startswith('users_backup_'):
                file_path = os.path.join(backup_dir, filename)
                stat = os.stat(file_path)
                backups.append({
                    'filename': filename,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 按创建时间倒序排列
        backups.sort(key=lambda x: x['created'], reverse=True)
        return jsonify(backups)
    except Exception as e:
        logger.error(f"获取备份列表失败: {e}")
        return jsonify([])

@app.route(f'/{ADMIN_PATH}/api/restore', methods=['POST'])
@admin_required
@rate_limit_required
def restore_backup_api():
    try:
        data = request.get_json()
        filename = data.get('filename')
        
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'}), 400
        
        if restore_users_data(filename):
            return jsonify({'success': True, 'message': '数据恢复成功'})
        else:
            return jsonify({'success': False, 'message': '数据恢复失败'}), 500
    except Exception as e:
        logger.error(f"恢复数据失败: {e}")
        return jsonify({'success': False, 'message': '数据恢复失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/security_stats')
@admin_required
def get_security_stats_api():
    """获取安全监控统计"""
    try:
        current_time = datetime.now()
        
        # 计算活跃会话数
        active_sessions_count = len(active_sessions)
        
        # 计算最近15分钟的失败登录次数
        failed_logins_count = 0
        fifteen_min_ago = current_time - timedelta(minutes=15)
        for attempts in login_attempts.values():
            failed_logins_count += len([t for t in attempts if t > fifteen_min_ago])
        
        # 计算被封锁的IP数量
        blocked_ips_count = 0
        for ip, attempts in login_attempts.items():
            if len(attempts) >= MAX_LOGIN_ATTEMPTS:
                blocked_ips_count += 1
        
        # 计算最近1分钟的API请求数
        api_requests_count = 0
        one_min_ago = current_time - timedelta(minutes=1)
        for requests in rate_limits.values():
            api_requests_count += len([t for t in requests if t > one_min_ago])
        
        return jsonify({
            'active_sessions': active_sessions_count,
            'failed_logins': failed_logins_count,
            'blocked_ips': blocked_ips_count,
            'api_requests': api_requests_count
        })
    except Exception as e:
        logger.error(f"获取安全统计失败: {e}")
        return jsonify({'success': False, 'message': '获取安全统计失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/active_sessions')
@admin_required
def get_active_sessions_api():
    """获取活跃会话列表"""
    try:
        sessions_list = []
        
        # 添加普通用户会话
        for session_id, session_info in active_sessions.items():
            sessions_list.append({
                'session_id': session_id[:8] + '...',  # 只显示前8位
                'username': session_info['username'],
                'ip': session_info['ip'],
                'last_activity': session_info['last_activity'].strftime('%Y-%m-%d %H:%M:%S'),
                'type': '普通用户'
            })
        
        # 添加管理员会话
        for session_id, session_info in admin_sessions.items():
            sessions_list.append({
                'session_id': session_id[:8] + '...',  # 只显示前8位
                'username': session_info['username'],
                'ip': session_info['ip'],
                'last_activity': session_info['last_activity'].strftime('%Y-%m-%d %H:%M:%S'),
                'type': '管理员'
            })
        
        # 按最后活动时间排序
        sessions_list.sort(key=lambda x: x['last_activity'], reverse=True)
        return jsonify(sessions_list)
    except Exception as e:
        logger.error(f"获取活跃会话失败: {e}")
        return jsonify({'success': False, 'message': '获取活跃会话失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/force_logout', methods=['POST'])
@admin_required
@rate_limit_required
def force_logout_api():
    """强制下线用户"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        
        if not session_id:
            return jsonify({'success': False, 'message': '会话ID不能为空'}), 400
        
        # 查找完整的会话ID（处理截断的会话ID）
        full_session_id = None
        session_prefix = session_id.replace('...', '')
        username = None
        
        # 先在普通用户会话中查找
        for sid in active_sessions.keys():
            if sid.startswith(session_prefix) or sid == session_id:
                full_session_id = sid
                username = active_sessions[sid]['username']
                del active_sessions[full_session_id]
                
                # 更新用户状态为离线
                users = load_users()
                if username in users:
                    users[username]['status'] = 'offline'
                    save_users(users)
                break
        
        # 如果没找到，再在管理员会话中查找
        if not full_session_id:
            for sid in admin_sessions.keys():
                if sid.startswith(session_prefix) or sid == session_id:
                    full_session_id = sid
                    username = admin_sessions[sid]['username']
                    del admin_sessions[full_session_id]
                    break
        
        if full_session_id:
            logger.info(f"管理员强制下线用户: {username}，会话ID: {full_session_id[:8]}...")
            return jsonify({'success': True, 'message': f'用户 {username} 已被强制下线'})
        else:
            logger.warning(f"尝试强制下线不存在的会话: {session_id}")
            return jsonify({'success': False, 'message': '会话不存在或已过期'}), 404
            
    except Exception as e:
        logger.error(f"强制下线失败: {e}")
        return jsonify({'success': False, 'message': '强制下线失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/system_settings', methods=['GET', 'POST'])
@admin_required
def system_settings_api():
    """系统设置API"""
    settings_file = 'system_settings.json'
    
    if request.method == 'GET':
        # 获取系统设置
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            else:
                # 默认设置
                settings = {
                    'system_name': 'Rinwriter',
                    'version': '1.0.0',
                    'max_concurrent_users': 50,
                    'session_timeout': 30
                }
            return jsonify(settings)
        except Exception as e:
            logger.error(f"获取系统设置失败: {e}")
            return jsonify({'success': False, 'message': '获取系统设置失败'}), 500
    
    elif request.method == 'POST':
        # 保存系统设置
        try:
            data = request.get_json()
            
            # 验证数据
            if not isinstance(data.get('max_concurrent_users'), int) or data['max_concurrent_users'] <= 0:
                return jsonify({'success': False, 'message': '最大并发用户数必须是正整数'}), 400
            
            if not isinstance(data.get('session_timeout'), int) or data['session_timeout'] <= 0:
                return jsonify({'success': False, 'message': '会话超时时间必须是正整数'}), 400
            
            # 保存设置
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"系统设置已更新: {data}")
            return jsonify({'success': True, 'message': '系统设置保存成功'})
        except Exception as e:
            logger.error(f"保存系统设置失败: {e}")
            return jsonify({'success': False, 'message': '保存系统设置失败'}), 500

@app.route(f'/{ADMIN_PATH}/api/realtime_status')
@admin_required
def get_realtime_status_api():
    """获取实时状态信息"""
    try:
        current_time = datetime.now()
        
        # 系统统计（使用缓存）
        system_stats = get_system_stats()
        
        # 在线用户详情（优先使用内存中的会话数据）
        online_users = []
        try:
            # 基于活跃会话获取在线用户
            online_usernames = set()
            for session_id, session_info in active_sessions.items():
                username = session_info.get('username')
                if username:
                    online_usernames.add(username)
            
            # 补充用户详细信息
            if online_usernames:
                users = load_users()
                for username in online_usernames:
                    if username in users:
                        user_data = users[username]
                        online_users.append({
                            'username': username,
                            'last_login_time': user_data.get('last_login_time', ''),
                            'last_login_ip': user_data.get('last_login_ip', ''),
                            'used_reports': user_data.get('used_reports', 0),
                            'report_quota': user_data.get('report_quota', 0)
                        })
        except Exception as e:
            logger.warning(f"获取在线用户详情失败: {e}")
        
        # 最近错误日志 (最近5条，带异常处理)
        recent_errors = []
        try:
            users = load_users()
            all_errors = []
            for username, user_data in users.items():
                user_logs = user_data.get('error_logs', [])
                for log in user_logs[-10:]:  # 每个用户最多取10条
                    all_errors.append({
                        'time': log.get('time', ''),
                        'username': username,
                        'operation': log.get('operation', ''),
                        'error': log.get('error', '')[:50] + '...' if len(log.get('error', '')) > 50 else log.get('error', ''),
                        'timestamp': log.get('time', '1970-01-01 00:00:00')  # 用于排序
                    })
            
            # 按时间倒序排列
            all_errors.sort(key=lambda x: x['timestamp'], reverse=True)
            recent_errors = all_errors[:5]
        except Exception as e:
            logger.warning(f"获取错误日志失败: {e}")
        
        # 活跃会话详情（合并普通用户和管理员会话）
        active_sessions_list = []
        try:
            # 添加普通用户会话
            for session_id, session_info in active_sessions.items():
                try:
                    created_time = session_info.get('created', session_info.get('last_activity', current_time))
                    # 确保时间对象类型正确
                    if isinstance(created_time, str):
                        continue  # 跳过格式错误的时间
                    
                    active_sessions_list.append({
                        'session_id': session_id[:8] + '...',
                        'username': session_info.get('username', '未知'),
                        'ip': session_info.get('ip', '未知'),
                        'last_activity': session_info.get('last_activity', current_time).strftime('%H:%M:%S'),
                        'duration': str(current_time - created_time).split('.')[0],
                        'type': '普通用户'
                    })
                except Exception as e:
                    logger.debug(f"处理用户会话失败: {e}")
                    continue
            
            # 添加管理员会话
            for session_id, session_info in admin_sessions.items():
                try:
                    created_time = session_info.get('created', session_info.get('last_activity', current_time))
                    # 确保时间对象类型正确
                    if isinstance(created_time, str):
                        continue  # 跳过格式错误的时间
                    
                    active_sessions_list.append({
                        'session_id': session_id[:8] + '...',
                        'username': session_info.get('username', '未知'),
                        'ip': session_info.get('ip', '未知'),
                        'last_activity': session_info.get('last_activity', current_time).strftime('%H:%M:%S'),
                        'duration': str(current_time - created_time).split('.')[0],
                        'type': '管理员'
                    })
                except Exception as e:
                    logger.debug(f"处理管理员会话失败: {e}")
                    continue
        except Exception as e:
            logger.warning(f"获取活跃会话失败: {e}")
        
        return jsonify({
            'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'system_stats': system_stats,
            'online_users': online_users,
            'recent_errors': recent_errors,
            'active_sessions': active_sessions_list[:10],  # 最多显示10个会话
            'server_uptime': str(current_time - server_start_time).split('.')[0] if 'server_start_time' in globals() else '未知',
            'total_active_sessions': len(active_sessions) + len(admin_sessions)
        })
    except Exception as e:
        logger.error(f"获取实时状态失败: {e}")
        return jsonify({'success': False, 'message': '获取实时状态失败', 'error': str(e)}), 500

@app.route('/api/user_quota')
@login_required
def get_user_quota_api():
    """获取当前用户的额度信息"""
    try:
        username = session.get('username')
        quota_info = check_user_quota(username)
        return jsonify({
            'success': True,
            'quota_info': quota_info
        })
    except Exception as e:
        logger.error(f"获取用户额度失败: {e}")
        return jsonify({'success': False, 'message': '获取额度信息失败'}), 500

@app.route('/api/user_profile')
@login_required
def get_user_profile_api():
    """获取当前用户的完整信息"""
    try:
        username = session.get('username')
        users = load_users()
        
        if username not in users:
            return jsonify({'success': False, 'message': '用户不存在'}), 404
        
        user_data = users[username]
        user_info = get_user_info(username)
        
        return jsonify({
            'success': True,
            'profile': {
                'username': username,
                'password': user_data.get('password', ''),
                'total_quota': user_data.get('report_quota', 0),
                'used_reports': user_data.get('used_reports', 0),
                'remaining_quota': user_data.get('report_quota', 0) - user_data.get('used_reports', 0),
                'total_generated': user_data.get('total_generated', 0),
                'permissions': user_data.get('permissions', []),
                'last_login_time': user_data.get('last_login_time', '从未登录'),
                'last_login_ip': user_data.get('last_login_ip', '未知')
            }
        })
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return jsonify({'success': False, 'message': '获取用户信息失败'}), 500

# 修改原有登录函数以支持管理员登录
@app.route('/login', methods=['GET', 'POST'])
def login():
    # 清除任何可能存在的会话
    session.clear()

    # 处理登录请求
    if request.method == 'POST':
        # 检查登录尝试次数
        if not check_login_attempts(request.remote_addr):
            return jsonify({'error': f'登录尝试过多，请{LOGIN_BLOCK_TIME}分钟后重试'}), 429
        
        username = request.form.get('username')
        password = request.form.get('password')
        
        # 检查是否是管理员登录
        if username == app.config['ADMIN_USERNAME'] and password == app.config['ADMIN_PASSWORD']:
            admin_session_id = create_session_id()
            session['admin_logged_in'] = True
            session['admin_login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            session['admin_session_id'] = admin_session_id
            session.permanent = False
            
            # 创建管理员会话记录
            create_session_record('admin', admin_session_id, is_admin=True)
            return redirect(url_for('admin_dashboard'))
        
        # 检查普通用户登录
        users = load_users()
        if username in users and users[username]['password'] == password:
            # 创建新的会话ID
            new_session_id = create_session_id()
            
            # 更新用户登录信息
            users[username]['last_login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            users[username]['last_login_ip'] = request.remote_addr
            users[username]['status'] = 'online'
            save_users(users)
            
            # 设置会话
            session['logged_in'] = True
            session['username'] = username
            session['session_id'] = new_session_id
            session['login_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            session.permanent = False
            
            # 创建会话记录（会自动清理旧会话）
            create_session_record(username, new_session_id)
            logger.info(f"用户 {username} 成功登录，会话ID: {new_session_id[:8]}...")
            return redirect(url_for('index'))
        else:
            # 记录失败的登录尝试
            record_login_attempt(request.remote_addr)
            
            # 获取当前失败次数信息
            attempts_info = get_login_attempts_count(request.remote_addr)
            
            # 登录失败，记录错误日志
            if username:
                log_user_error(username, '登录', '用户名或密码错误', request.remote_addr)
            
            # 传递失败次数信息到登录页面
            return redirect(url_for('login', 
                                  error=1,
                                  attempts=attempts_info['current_attempts'],
                                  remaining=attempts_info['remaining_attempts']))

    # 显示登录页面
    login_html = open('login.html', encoding='utf-8').read()
    return render_template_string(login_html)

@app.before_request
def check_session_activity():
    """检查会话活动和超时"""
    # 跳过静态资源请求
    if request.endpoint == 'static':
        return
    
    # 跳过管理员相关路径的会话检查，避免干扰管理员操作
    if request.path and (request.path.startswith(f'/{ADMIN_PATH}/') or 
                        request.path.startswith(f'/{ADMIN_PATH}/api/')):
        return
    
    # 每30秒清理一次过期会话，避免频繁操作
    current_time = time.time()
    if not hasattr(check_session_activity, 'last_cleanup') or current_time - check_session_activity.last_cleanup > 30:
        try:
            check_session_timeout()
            check_session_activity.last_cleanup = current_time
        except Exception as e:
            logger.error(f"清理会话失败: {e}")
    
    # 对于普通用户的会话验证
    if 'logged_in' in session:
        session_id = session.get('session_id')
        username = session.get('username')
        
        # 检查会话是否存在于活跃会话列表中
        if session_id not in active_sessions:
            # 会话已被强制下线或过期，立即清除会话并重定向
            if request.endpoint not in ['login', 'admin_login']:
                logger.info(f"用户 {username} 的会话 {session_id[:8] if session_id else 'None'}... 不存在，强制重定向到登录页")
                session.clear()
                return redirect(url_for('login'))
        else:
            # 会话有效，减少更新频率（避免过度频繁的数据写入）
            try:
                last_activity = active_sessions[session_id].get('last_activity', datetime.min)
                if current_time - last_activity.timestamp() > 300:  # 5分钟更新一次
                    update_session_activity(username, session_id)
            except Exception as e:
                logger.error(f"更新会话活动失败: {e}")
    
    # 管理员会话不在此处检查，由 admin_required 装饰器处理

# 修改用户验证装饰器
@app.route('/')
@login_required
def index():
    # 获取当前用户信息
    username = session.get('username')
    user_info = get_user_info(username) if username else None
    
    # 如果用户不存在（被删除），清除会话并重定向到登录页
    if not user_info:
        session.clear()
        flash('用户账户已被删除，请重新登录', 'error')
        return redirect(url_for('login'))
    
    # 读取HTML模板
    with open('upload.html', encoding='utf-8') as f:
        html_content = f.read()
    
    # 在HTML中注入用户权限信息
    script_injection = f"""
    <script>
        window.userInfo = {{
            username: '{user_info["username"]}',
            permissions: {json.dumps(user_info["permissions"])},
            quotaInfo: {json.dumps(user_info["quota_info"])},
            hasSinglePermission: {str(user_info["has_single_permission"]).lower()},
            hasBatchPermission: {str(user_info["has_batch_permission"]).lower()},
            hasAchievementPermission: {str(user_info["has_achievement_permission"]).lower()}
        }};
    </script>
    """
    # 在</head>标签前插入脚本
    html_content = html_content.replace('</head>', script_injection + '</head>')
    
    return render_template_string(html_content)

# 启动清理线程
def start_cleanup_thread():
    import threading
    def cleanup_task():
        global backup_counter
        while True:
            try:
                # 清理过期会话
                check_session_timeout()
                
                # 每60分钟执行一次备份
                backup_counter += 1
                if backup_counter >= 60:  # 60分钟
                    backup_users_data()
                    backup_counter = 0
                
                # 清理过期的登录尝试和API限制记录
                current_time = datetime.now()
                block_time_ago = current_time - timedelta(minutes=LOGIN_BLOCK_TIME)
                minute_ago = current_time - timedelta(minutes=1)
                
                # 清理登录尝试记录
                for ip in list(login_attempts.keys()):
                    login_attempts[ip] = [t for t in login_attempts[ip] if t > block_time_ago]
                    if not login_attempts[ip]:
                        del login_attempts[ip]
                
                # 清理API限制记录
                for ip in list(rate_limits.keys()):
                    rate_limits[ip] = [t for t in rate_limits[ip] if t > minute_ago]
                    if not rate_limits[ip]:
                        del rate_limits[ip]
                
            except Exception as e:
                logger.error(f"清理任务失败: {e}")
            
            time.sleep(60)  # 每分钟执行一次
    
    # 启动后台清理线程
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("后台清理任务已启动，自动备份间隔：1小时")

def ask_hot_reload():
    """询问用户是否开启热载功能（5秒超时自动选择生产模式）"""
    import select
    import sys
    import threading
    
    print("\n" + "="*60)
    print("🔥 热载配置")
    print("="*60)
    print("热载（Hot Reload）功能可以在代码修改后自动重启服务器")
    print("⚠️  注意：开启热载会稍微影响性能，适合开发环境")
    print("📝 生产环境建议关闭热载功能")
    print("-"*60)
    
    def timeout_input(prompt, timeout=5):
        """带超时的输入函数"""
        result = [None]
        
        def input_thread():
            try:
                result[0] = input(prompt)
            except (EOFError, KeyboardInterrupt):
                result[0] = "interrupted"
        
        # 启动输入线程
        thread = threading.Thread(target=input_thread)
        thread.daemon = True
        thread.start()
        
        # 等待超时或输入完成
        thread.join(timeout)
        
        if thread.is_alive():
            # 超时了，返回None
            print(f"\n⏰ {timeout}秒内无输入，自动选择生产模式")
            return None
        else:
            # 有输入
            return result[0]
    
    while True:
        try:
            choice = timeout_input("是否开启热载功能？(y/n) [默认: n, 5秒后自动选择]: ", timeout=5)
            
            if choice is None:  # 超时
                print("✅ 已自动选择：关闭热载（生产模式）")
                return False
            elif choice == "interrupted":  # 用户中断
                print("\n❌ 用户中断，默认关闭热载")
                return False
            
            choice = choice.strip().lower()
            
            if choice == '' or choice == 'n' or choice == 'no':
                print("✅ 已选择：关闭热载（生产模式）")
                return False
            elif choice == 'y' or choice == 'yes':
                print("🔥 已选择：开启热载（开发模式）")
                print("💡 提示：修改代码后将自动重启服务器")
                return True
            else:
                print("❌ 请输入 y 或 n")
                continue  # 重新询问，但不重新计时
        except Exception as e:
            print(f"\n❌ 输入处理出错：{e}，默认关闭热载")
            return False

if __name__ == '__main__':
    # 询问是否开启热载
    enable_hot_reload = ask_hot_reload()
    
    # 启动清理线程
    start_cleanup_thread()
    
    # 显示启动信息
    display_startup_info()
    
    # 启动服务器
    try:
        if enable_hot_reload:
            print("\n🔥 服务器启动中（热载模式）...")
            socketio.run(app, host='::', port=9967, debug=True, allow_unsafe_werkzeug=True, use_reloader=True)
        else:
            print("\n🚀 服务器启动中（生产模式）...")
            socketio.run(app, host='::', port=9967, debug=False, allow_unsafe_werkzeug=True)
    except KeyboardInterrupt:
        print("\n" + "="*60)
        print("🛑 服务器已停止")
        print("="*60)