<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rinwriter - 登录</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Noto+Sans+SC:wght@400;500&display=swap" rel="stylesheet">
    <style>
        /* From Uiverse.io by g<PERSON><PERSON><PERSON><PERSON><PERSON> */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 添加动态背景效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.15) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }

        /* 添加网格背景 */
        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 30s linear infinite;
            opacity: 0.4;
        }

        @keyframes backgroundShift {
            0%, 100% {
                transform: translateX(0) translateY(0);
            }
            25% {
                transform: translateX(-20px) translateY(-10px);
            }
            50% {
                transform: translateX(20px) translateY(20px);
            }
            75% {
                transform: translateX(-10px) translateY(10px);
            }
        }

        @keyframes gridMove {
            0% {
                transform: translate(0, 0);
            }
            100% {
                transform: translate(50px, 50px);
            }
        }

        /* 添加浮动粒子效果 */
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            pointer-events: none;
            animation: float 15s infinite linear;
        }

        .particle:nth-child(1) {
            width: 4px;
            height: 4px;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            width: 6px;
            height: 6px;
            left: 20%;
            animation-delay: 2s;
        }

        .particle:nth-child(3) {
            width: 3px;
            height: 3px;
            left: 30%;
            animation-delay: 4s;
        }

        .particle:nth-child(4) {
            width: 5px;
            height: 5px;
            left: 40%;
            animation-delay: 6s;
        }

        .particle:nth-child(5) {
            width: 4px;
            height: 4px;
            left: 50%;
            animation-delay: 8s;
        }

        .particle:nth-child(6) {
            width: 6px;
            height: 6px;
            left: 60%;
            animation-delay: 10s;
        }

        .particle:nth-child(7) {
            width: 3px;
            height: 3px;
            left: 70%;
            animation-delay: 12s;
        }

        .particle:nth-child(8) {
            width: 5px;
            height: 5px;
            left: 80%;
            animation-delay: 14s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .login-box {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 350px;
            padding: 35px;
            transform: translate(-50%, -50%);
            background: rgba(15, 15, 25, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-sizing: border-box;
            box-shadow:
                0 25px 45px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 0 50px rgba(3, 244, 15, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 10;
        }

        .login-box h2 {
            margin: 0 0 30px;
            padding: 0;
            color: #fff;
            text-align: center;
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 700;
        }

        .login-box .user-box {
            position: relative;
        }

        .login-box .user-box input {
            width: 100%;
            padding: 10px 0;
            font-size: 16px;
            color: #fff !important;
            margin-bottom: 30px;
            border: none;
            border-bottom: 1px solid #fff;
            outline: none;
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: #fff !important;
        }

        .login-box .user-box input:focus {
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
        }

        .login-box .user-box input:valid {
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
        }

        .login-box .user-box input:-webkit-autofill,
        .login-box .user-box input:-webkit-autofill:hover,
        .login-box .user-box input:-webkit-autofill:focus,
        .login-box .user-box input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: #fff !important;
            background: transparent !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        .login-box .user-box label {
            position: absolute;
            top: 0;
            left: 0;
            padding: 10px 0;
            font-size: 16px;
            color: #fff;
            pointer-events: none;
            transition: .5s;
        }

        .login-box .user-box input:focus ~ label,
        .login-box .user-box input:valid ~ label {
            top: -20px;
            left: 0;
            color: #bdb8b8;
            font-size: 12px;
        }

        .login-box form button {
            position: relative;
            display: block;
            padding: 10px 20px;
            background: transparent;
            color: #ffffff;
            font-size: 16px;
            text-decoration: none;
            text-transform: uppercase;
            overflow: hidden;
            transition: .5s;
            margin: 40px auto 0;
            letter-spacing: 4px;
            border: none;
            cursor: pointer;
            width: fit-content;
        }

        .login-box button:hover {
            background: #03f40f;
            color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 5px #03f40f,
                        0 0 25px #03f40f,
                        0 0 50px #03f40f,
                        0 0 100px #03f40f;
        }

        .login-box button span {
            position: absolute;
            display: block;
        }

        @keyframes btn-anim1 {
            0% {
                left: -100%;
            }
            50%,100% {
                left: 100%;
            }
        }

        .login-box button span:nth-child(1) {
            bottom: 2px;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #03f40f);
            animation: btn-anim1 2s linear infinite;
        }

        @keyframes btn-anim2 {
            0% {
                top: -100%;
            }
            50%,100% {
                top: 100%;
            }
        }

        .login-box button span:nth-child(2) {
            right: 0;
            top: -100%;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, transparent, #03f40f);
            animation: btn-anim2 2s linear infinite;
            animation-delay: .5s;
        }

        @keyframes btn-anim3 {
            0% {
                right: -100%;
            }
            50%,100% {
                right: 100%;
            }
        }

        .login-box button span:nth-child(3) {
            bottom: 0;
            right: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(270deg, transparent, #03f40f);
            animation: btn-anim3 2s linear infinite;
            animation-delay: 1s;
        }

        @keyframes btn-anim4 {
            0% {
                bottom: -100%;
            }
            50%,100% {
                bottom: 100%;
            }
        }

        .login-box button span:nth-child(4) {
            left: 0;
            bottom: -100%;
            width: 2px;
            height: 100%;
            background: linear-gradient(360deg, transparent, #03f40f);
            animation: btn-anim4 2s linear infinite;
            animation-delay: 1.5s;
        }

        .error-message {
            color: #fff;
            background-color: rgba(239, 68, 68, 0.7);
            border-radius: 4px;
            padding: 10px 12px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
            position: relative;
            text-align: center;
        }

        .error-message.show {
            display: block;
        }

        .error-message::before {
            content: '⚠️';
            margin-right: 8px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8rem;
        }

    </style>
</head>
<body>
    <!-- 浮动粒子效果 -->
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>

    <div class="login-box">
        <h2>Rinwriter</h2>

        <div class="error-message" id="error-message">
            用户名或密码错误，请重试。
        </div>

        <form id="login-form" action="/login" method="post">
            <div class="user-box">
                <input type="text" id="username" name="username" required>
                <label>用户名</label>
            </div>
            <div class="user-box">
                <input type="password" id="password" name="password" required>
                <label>密码</label>
            </div>
            <button type="submit">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                登录
            </button>
        </form>

        <div class="footer">
            © 2025 Rinwriter - by 联 微信：116383932 | 版本 1.0
        </div>
    </div>

    <script>
        // 检查URL参数是否有错误信息
        document.addEventListener('DOMContentLoaded', function() {
            // 快速检查是否有错误参数
            if (window.location.search.includes('error')) {
                const errorMessage = document.getElementById('error-message');
                errorMessage.classList.add('show');

                // 3秒后自动隐藏错误信息
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            }
        });
    </script>
</body>
</html>


