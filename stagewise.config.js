// Stagewise 配置文件 - 为 Rinwriter 项目定制
export default {
  // 项目基本信息
  name: "Rinwriter",
  description: "研发报告生成器前端界面",
  
  // 开发服务器配置
  devServer: {
    port: 9967,
    host: "localhost"
  },
  
  // 工具栏配置
  toolbar: {
    theme: "dark", // 匹配您的深色主题偏好
    position: "bottom-right",
    size: "medium",
    
    // 自定义样式以匹配您的绿色主题
    customStyles: {
      "--stagewise-primary": "#10b981", // 您偏好的绿色
      "--stagewise-background": "rgba(17, 24, 39, 0.95)", // 深色背景
      "--stagewise-border": "rgba(16, 185, 129, 0.3)", // 绿色边框
      "--stagewise-text": "#f3f4f6", // 亮色文字
      "--stagewise-shadow": "0 4px 20px rgba(16, 185, 129, 0.2)" // 绿色阴影
    }
  },
  
  // 插件配置
  plugins: [
    {
      name: "design-system",
      config: {
        // 您的设计系统偏好
        colorScheme: "dark",
        primaryColor: "#10b981", // 绿色
        accentColor: "#059669", // 深绿色
        backgroundColor: "#111827", // 深色背景
        glassEffect: true, // 启用磨砂效果
        shadows: "green" // 使用绿色阴影而非黑色
      }
    },
    {
      name: "component-inspector",
      config: {
        // 组件检查器配置
        highlightColor: "#10b981",
        showBoundingBox: true,
        showComponentName: true
      }
    }
  ],
  
  // 文件监听配置
  watch: {
    files: [
      "*.html",
      "*.css",
      "*.js",
      "static/**/*"
    ],
    ignore: [
      "node_modules/**",
      "*.pyc",
      "__pycache__/**",
      "uploads/**",
      "backups/**"
    ]
  },
  
  // AI 助手配置
  ai: {
    // 针对您的项目特点的提示词
    systemPrompt: `
      你正在帮助改进 Rinwriter - 一个研发报告生成器的前端界面。
      
      设计偏好：
      - 深色背景主题
      - 绿色配色方案（主色：#10b981）
      - 高透明度和磨砂玻璃效果
      - 3D立体效果（特别是登录界面）
      - 简洁的按钮设计，避免过多动态效果
      - 绿色阴影而非黑色阴影
      - 较弱的阴影效果
      
      技术栈：
      - Flask 后端
      - 原生 HTML/CSS/JavaScript
      - Socket.IO 用于实时通信
      - 响应式设计
      
      请确保所有修改都符合这些设计偏好和技术约束。
    `,
    
    // 常用的设计模式
    patterns: [
      {
        name: "绿色按钮",
        description: "符合设计规范的绿色按钮样式",
        code: `
          background: linear-gradient(135deg, #10b981, #059669);
          border: 1px solid rgba(16, 185, 129, 0.3);
          box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
          backdrop-filter: blur(10px);
        `
      },
      {
        name: "磨砂卡片",
        description: "带有磨砂效果的卡片容器",
        code: `
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
        `
      }
    ]
  }
};
