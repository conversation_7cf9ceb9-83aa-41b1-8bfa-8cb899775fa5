# Rinwriter 服务器重启功能说明

## 🔧 功能概述

已为您的Rinwriter管理系统添加了以下功能：

1. **取消自动刷新** - 移除了所有自动刷新功能，减少服务器负载
2. **服务器重启按钮** - 在管理员系统设置页面添加了重启服务器按钮

## 📋 安装步骤

### 1. 设置systemd服务

将提供的 `rinwriter.service` 文件复制到系统服务目录：

```bash
# 编辑服务文件，修改路径信息
sudo nano rinwriter.service

# 复制服务文件到系统目录
sudo cp rinwriter.service /etc/systemd/system/

# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务开机自启
sudo systemctl enable rinwriter

# 启动服务
sudo systemctl start rinwriter

# 检查服务状态
sudo systemctl status rinwriter
```

### 2. 修改服务文件配置

编辑 `rinwriter.service` 文件中的以下内容：

```ini
# 修改为您的实际路径
WorkingDirectory=/path/to/your/rinwriter          # Rinwriter项目目录
Environment=PATH=/path/to/your/venv/bin           # Python虚拟环境路径
ExecStart=/path/to/your/venv/bin/python app.py   # 启动命令
User=www-data                                     # 运行用户（可改为您的用户名）
```

### 3. 设置sudo权限

为了让应用能够重启服务，需要给运行用户添加sudo权限：

```bash
# 编辑sudoers文件
sudo visudo

# 添加以下行（将 www-data 替换为您的实际用户）
www-data ALL=(ALL) NOPASSWD: /bin/systemctl restart rinwriter
www-data ALL=(ALL) NOPASSWD: /bin/systemctl restart rinwriter.service
```

### 4. 修改服务名称（可选）

如果您想使用不同的服务名称，需要修改 `app.py` 中的服务名称：

```python
# 在 restart_server_api 函数中找到这行：
service_name = 'rinwriter'  # 修改为您的实际服务名称
```

## 🚀 使用方法

1. 登录管理员界面
2. 进入"系统设置"页面
3. 点击"重启服务器"按钮
4. 确认重启操作
5. 系统将显示重启进度和倒计时
6. 重启完成后页面会自动刷新

## ⚠️ 重要说明

### 安全特性
- 双重确认机制，防止误操作
- 管理员权限验证
- 详细的操作日志记录
- 优雅的错误处理

### 重启流程
- 系统尝试多种重启方式
- 2秒延迟确保响应发送
- 60秒倒计时等待重启完成
- 自动检测服务器状态并刷新页面

### 故障处理
如果重启失败，系统会依次尝试：
1. `sudo systemctl restart rinwriter`
2. `systemctl restart rinwriter`
3. `sudo systemctl restart rinwriter.service`
4. `systemctl restart rinwriter.service`
5. `sudo pkill -f python.*app.py`
6. `pkill -f python.*app.py`

## 📊 已移除的功能

为了减少服务器负载，已移除以下自动刷新功能：

- ❌ 统计数据每5秒自动刷新
- ❌ 实时状态每10秒自动刷新  
- ❌ 安全监控每10秒自动刷新

现在所有数据都需要手动点击"刷新"按钮更新。

## 🔍 测试重启功能

```bash
# 检查服务状态
sudo systemctl status rinwriter

# 手动测试重启
sudo systemctl restart rinwriter

# 查看服务日志
sudo journalctl -u rinwriter -f
```

## 💡 常见问题

**Q: 重启按钮没有反应？**
A: 检查服务名称和sudo权限设置是否正确

**Q: 重启后无法访问？**
A: 检查服务是否正常启动：`sudo systemctl status rinwriter`

**Q: 权限不足错误？**
A: 确保sudoers文件配置正确，并重新登录

**Q: 想要恢复自动刷新？**
A: 可以在admin.html中重新添加setInterval函数

## 📝 日志记录

所有管理员操作都会记录在 `admin_logs.json` 文件中，包括：
- 重启操作时间
- 管理员会话ID
- 操作详情
- IP地址

---

*配置完成后，您就可以在管理界面中安全地重启服务器了！* 