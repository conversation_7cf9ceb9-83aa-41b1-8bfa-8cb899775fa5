#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试应用成效字数限制修改
验证从110字调整到300字的修改是否正确
"""

import re
import os

def test_word_limit_changes():
    """测试字数限制修改"""
    print("🔍 检查应用成效字数限制修改...")
    
    # 读取app.py文件
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有110字的限制
    old_pattern = r'110字以内'
    old_matches = re.findall(old_pattern, content)
    
    # 检查新的300字限制
    new_pattern = r'300字以内'
    new_matches = re.findall(new_pattern, content)
    
    # 检查表格高度调整
    height_pattern = r'table\.rows\[0\]\.height = Cm\((\d+\.?\d*)\)'
    height_matches = re.findall(height_pattern, content)
    
    print(f"📊 检查结果:")
    print(f"   - 旧的110字限制残留: {len(old_matches)} 处")
    print(f"   - 新的300字限制: {len(new_matches)} 处")
    print(f"   - 表格高度设置: {height_matches}")
    
    # 详细检查提示词内容
    prompt_pattern = r'请根据以下摘要内容，撰写一段严格控制在(\d+)字以内的描述'
    prompt_matches = re.findall(prompt_pattern, content)
    
    print(f"   - 提示词中的字数限制: {prompt_matches}")
    
    # 验证结果
    success = True
    if old_matches:
        print(f"❌ 发现 {len(old_matches)} 处旧的110字限制未修改")
        success = False
    
    if len(new_matches) < 2:
        print(f"❌ 新的300字限制应该有2处，实际找到 {len(new_matches)} 处")
        success = False
    
    if not height_matches or float(height_matches[0]) < 6.0:
        print(f"❌ 表格高度可能不足以容纳300字内容")
        success = False
    
    if success:
        print("✅ 所有修改都已正确应用！")
    else:
        print("⚠️  发现一些问题需要修复")
    
    return success

def generate_test_prompt():
    """生成测试用的AI提示词"""
    tech_name = "智能温控系统"
    abstract_content = """
    本发明公开了一种智能温控系统，包括温度传感器、控制器和执行器。
    该系统通过先进的算法实现精确温度控制，具有节能、高效、智能化的特点。
    系统可以根据环境变化自动调节，提供舒适的室内环境。
    """
    
    prompt = f"""请根据以下摘要内容，撰写一段严格控制在300字以内的描述，必须以'通过{tech_name}的应用，达到了'开头，后接具体效果：
摘要内容：{abstract_content}
描述需突出技术应用效果，可以详细阐述技术优势、应用场景、实际效果和价值意义，语言简洁流畅，适合正式报告。"""
    
    print("\n📝 测试用AI提示词:")
    print("=" * 60)
    print(prompt)
    print("=" * 60)
    
    # 模拟可能的AI回复（300字以内）
    sample_response = """通过智能温控系统的应用，达到了精确控制室内温度±0.5℃的卓越效果。该系统相比传统温控设备节能效率提升30%，响应速度提高50%，大幅改善了用户的居住舒适度。系统采用先进的自适应算法，能够根据室外环境变化、人员活动情况和用户习惯自动调节温度设定，实现了真正的智能化管理。在实际应用中，该技术不仅降低了能源消耗成本，还减少了碳排放，为绿色建筑和智能家居领域提供了重要的技术支撑。通过大规模部署，预计可为用户节省20-35%的空调电费，同时提升室内环境质量，具有显著的经济效益和社会价值。该系统的成功应用为智能建筑行业树立了新的技术标杆。"""
    
    print(f"\n📄 示例回复 (字数: {len(sample_response)}):")
    print(sample_response)
    
    if len(sample_response) <= 300:
        print("✅ 示例回复符合300字限制")
    else:
        print(f"❌ 示例回复超出300字限制 ({len(sample_response)}字)")

def main():
    """主函数"""
    print("🚀 应用成效字数限制修改测试")
    print("=" * 50)
    
    # 测试修改
    test_result = test_word_limit_changes()
    
    # 生成测试提示词
    generate_test_prompt()
    
    print("\n" + "=" * 50)
    if test_result:
        print("🎉 修改验证通过！应用成效字数限制已成功调整为300字")
    else:
        print("⚠️  请检查并修复发现的问题")

if __name__ == "__main__":
    main()
