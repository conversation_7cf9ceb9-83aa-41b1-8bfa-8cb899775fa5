<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rinwriter - 后台管理</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* 🎨 精致专业配色系统 */
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-300: #7dd3fc;
            --primary-400: #38bdf8;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;
            --primary-900: #0c4a6e;
            
            --success-50: #f0fdf4;
            --success-100: #dcfce7;
            --success-200: #bbf7d0;
            --success-300: #86efac;
            --success-400: #4ade80;
            --success-500: #22c55e;
            --success-600: #16a34a;
            --success-700: #15803d;
            --success-800: #166534;
            --success-900: #14532d;
            
            --warning-50: #fffbeb;
            --warning-100: #fef3c7;
            --warning-200: #fde68a;
            --warning-300: #fcd34d;
            --warning-400: #fbbf24;
            --warning-500: #f59e0b;
            --warning-600: #d97706;
            --warning-700: #b45309;
            --warning-800: #92400e;
            --warning-900: #78350f;
            
            --danger-50: #fef2f2;
            --danger-100: #fee2e2;
            --danger-200: #fecaca;
            --danger-300: #fca5a5;
            --danger-400: #f87171;
            --danger-500: #ef4444;
            --danger-600: #dc2626;
            --danger-700: #b91c1c;
            --danger-800: #991b1b;
            --danger-900: #7f1d1d;
            
            /* 中性色系 - 深色主题 */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* 背景系统 */
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-surface: rgba(30, 41, 59, 0.8);
            --bg-surface-hover: rgba(51, 65, 85, 0.6);
            --bg-glass: rgba(30, 41, 59, 0.7);
            --bg-glass-strong: rgba(30, 41, 59, 0.9);
            
            /* 文字颜色 */
            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --text-tertiary: #cbd5e1;
            --text-muted: #94a3b8;
            --text-accent: var(--primary-400);
            
            /* 边框颜色 */
            --border-primary: rgba(226, 232, 240, 0.1);
            --border-secondary: rgba(226, 232, 240, 0.15);
            --border-accent: rgba(56, 189, 248, 0.3);
            
            /* 阴影系统 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* 圆角系统 */
            --radius-sm: 0.375rem;
            --radius: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
            
            /* 间距系统 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
            overflow-x: hidden;
        }
        
        /* 微妙的背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 25% 25%, rgba(56, 189, 248, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 - 精简设计 */
        .sidebar {
            width: 280px;
            background: rgba(30, 41, 59, 0.15);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(226, 232, 240, 0.04);
            padding: var(--space-6) 0;
            position: relative;
        }

        .logo {
            padding: 0 var(--space-6) var(--space-8);
            text-align: center;
            border-bottom: 1px solid var(--border-primary);
            margin-bottom: var(--space-6);
        }

        .logo h1 {
            color: var(--text-primary);
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: var(--text-tertiary);
            font-weight: 500;
        }

        .nav-menu {
            list-style: none;
            padding: 0 var(--space-4);
        }

        .nav-item {
            margin-bottom: var(--space-2);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .nav-link:hover {
            background: var(--bg-surface-hover);
            color: var(--text-primary);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: rgba(56, 189, 248, 0.1);
            color: var(--primary-400);
            border-left: 3px solid var(--primary-400);
            border-radius: 0 var(--radius-md) var(--radius-md) 0;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
            opacity: 0.8;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: var(--space-8);
            overflow-y: auto;
            background: rgba(15, 23, 42, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            background: rgba(30, 41, 59, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(226, 232, 240, 0.04);
            border-radius: var(--radius-xl);
            padding: var(--space-5) var(--space-6);
        }

        .page-title {
            color: var(--text-primary);
            font-size: 1.875rem;
            font-weight: 700;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .user-info span {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-3);
            background: none;
            border: none;
            border-radius: var(--radius);
        }

        .logout-btn {
            background: transparent;
            border: 1px solid var(--danger-500);
            color: var(--danger-500);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .logout-btn:hover {
            background: var(--danger-500);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* 卡片系统 */
        .card {
            background: rgba(30, 41, 59, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(226, 232, 240, 0.05);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            transition: all 0.2s ease;
        }

        .card:hover {
            border-color: rgba(226, 232, 240, 0.08);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .card-title {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .card-title i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(100, 116, 139, 0.15);
            border-radius: var(--radius);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .card-subtitle {
            color: var(--text-primary);
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .card-subtitle i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(100, 116, 139, 0.15);
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        /* 统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .stat-card {
            background: rgba(30, 41, 59, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(226, 232, 240, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            text-align: center;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            border-color: rgba(226, 232, 240, 0.08);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: var(--text-secondary);
            font-size: 1.25rem;
            background: rgba(100, 116, 139, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 监控网格 - 重新设计 */
        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }

        .monitoring-card {
            background: rgba(30, 41, 59, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(226, 232, 240, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-5);
            transition: all 0.2s ease;
        }

        .monitoring-card:hover {
            border-color: rgba(226, 232, 240, 0.08);
            box-shadow: var(--shadow-lg);
        }

        .monitoring-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .monitoring-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .monitoring-icon.system { background: rgba(100, 116, 139, 0.15); }
        .monitoring-icon.users { background: rgba(100, 116, 139, 0.15); }
        .monitoring-icon.errors { background: rgba(100, 116, 139, 0.15); }
        .monitoring-icon.sessions { background: rgba(100, 116, 139, 0.15); }

        .monitoring-title {
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
        }

        .monitoring-content {
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--border-primary);
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-value {
            color: var(--primary-400);
            font-weight: 600;
            font-feature-settings: 'tnum';
        }

        .monitoring-scrollable {
            max-height: 120px;
            overflow-y: auto;
            padding-right: var(--space-2);
        }

        .monitoring-scrollable::-webkit-scrollbar {
            width: 4px;
        }

        .monitoring-scrollable::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 2px;
        }

        .monitoring-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-500);
            border-radius: 2px;
        }

        /* 信息横幅 */
        .info-banner {
            background: rgba(30, 41, 59, 0.15);
            border: 1px solid rgba(226, 232, 240, 0.04);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-top: var(--space-6);
        }

        .info-banner-content {
            color: var(--text-tertiary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .info-banner-icon {
            color: var(--primary-400);
        }

        /* 备份设置优化 */
        .backup-setting-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
        }

        .backup-info-card {
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-5);
            text-align: center;
            transition: all 0.2s ease;
        }

        .backup-info-card:hover {
            border-color: var(--primary-400);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .backup-info-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-3);
            color: white;
            font-size: 1rem;
        }

        .backup-info-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-400);
            margin-bottom: var(--space-2);
        }

        .backup-info-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            overflow: hidden;
            margin-top: var(--space-3);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-500), var(--success-500));
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* 表格系统 */
        .table-container {
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: var(--space-4);
            text-align: left;
            border-bottom: 1px solid var(--border-primary);
        }

        .table th {
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background: var(--bg-surface);
        }

        /* 按钮系统 */
        .btn {
            padding: var(--space-2) var(--space-4);
            border: 1px solid transparent;
            border-radius: var(--radius);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            background: transparent;
        }

        .btn-primary {
            background: var(--primary-500);
            color: white;
            border-color: var(--primary-500);
        }

        .btn-primary:hover {
            background: var(--primary-600);
            border-color: var(--primary-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-500);
            color: white;
            border-color: var(--success-500);
        }

        .btn-success:hover {
            background: var(--success-600);
            border-color: var(--success-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: var(--warning-500);
            color: white;
            border-color: var(--warning-500);
        }

        .btn-warning:hover {
            background: var(--warning-600);
            border-color: var(--warning-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: var(--danger-500);
            color: white;
            border-color: var(--danger-500);
        }

        .btn-danger:hover {
            background: var(--danger-600);
            border-color: var(--danger-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-sm {
            padding: var(--space-1) var(--space-3);
            font-size: 0.75rem;
        }

        /* 表单系统 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }

        .form-group {
            margin-bottom: var(--space-5);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            color: var(--text-primary);
            font-weight: 500;
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: var(--space-3);
            border: 1px solid var(--border-primary);
            background: var(--bg-surface);
            border-radius: var(--radius);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.1);
            background: var(--bg-glass);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: var(--space-2);
        }

        .status-online {
            background: var(--success-500);
            box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
        }

        .status-offline {
            background: var(--gray-500);
        }

        /* 徽章系统 */
        .badge {
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: var(--space-1);
        }

        .badge-success {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-400);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .badge-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-400);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        /* 模态框系统 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            width: 90%;
            max-width: 600px;
            background: var(--bg-glass-strong);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
        }

        .modal-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .close-btn {
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: var(--danger-500);
            border-color: var(--danger-500);
            color: white;
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            background: var(--bg-glass-strong);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            box-shadow: var(--shadow-xl);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            min-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 3px solid var(--success-500);
        }

        .notification.error {
            border-left: 3px solid var(--danger-500);
        }

        .notification.warning {
            border-left: 3px solid var(--warning-500);
        }

        /* 页面切换 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 🚀 移动端深度适配 */
        
        /* 平板设备优化 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 260px;
            }
            
            .main-content {
                padding: var(--space-6);
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: var(--space-4);
            }

            .monitoring-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
            
            .card {
                padding: var(--space-5);
            }
            
            .btn {
                padding: var(--space-3) var(--space-4);
                font-size: 0.9rem;
            }
        }

        /* 移动端核心适配 */
        @media (max-width: 768px) {
            :root {
                /* 移动端专用间距 */
                --mobile-space-1: 0.25rem;
                --mobile-space-2: 0.5rem;
                --mobile-space-3: 0.75rem;
                --mobile-space-4: 1rem;
                --mobile-space-6: 1.5rem;
                --mobile-space-8: 2rem;
            }
            
            .admin-container {
                flex-direction: column;
                min-height: 100vh;
            }

            /* 移动端侧边栏 - 顶部导航栏 */
            .sidebar {
                width: 100%;
                padding: var(--mobile-space-3) 0;
                position: sticky;
                top: 0;
                z-index: 100;
                backdrop-filter: blur(25px);
                border-right: none;
                border-bottom: 1px solid var(--border-primary);
            }
            
            .logo {
                padding: 0 var(--mobile-space-4) var(--mobile-space-4);
                margin-bottom: var(--mobile-space-3);
                border-bottom: 1px solid var(--border-primary);
            }
            
            .logo h1 {
                font-size: 1.5rem;
                margin-bottom: var(--mobile-space-1);
            }
            
            .logo-subtitle {
                font-size: 0.8rem;
            }

            /* 横向滚动导航 */
            .nav-menu {
                display: flex;
                overflow-x: auto;
                padding: 0 var(--mobile-space-4);
                gap: var(--mobile-space-2);
                scrollbar-width: none;
                -ms-overflow-style: none;
            }
            
            .nav-menu::-webkit-scrollbar {
                display: none;
            }

            .nav-item {
                flex-shrink: 0;
                margin-bottom: 0;
            }

            .nav-link {
                white-space: nowrap;
                padding: var(--mobile-space-3) var(--mobile-space-4);
                min-height: 44px; /* iOS最小触控区域 */
                border-radius: var(--radius-lg);
                font-size: 0.875rem;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 100px;
            }
            
            .nav-link.active {
                border-left: none;
                border-radius: var(--radius-lg);
                border: 2px solid var(--primary-400);
            }

            /* 主内容区移动端优化 */
            .main-content {
                padding: var(--mobile-space-4);
                background: transparent;
            }

            /* 头部移动端适配 */
            .header {
                flex-direction: column;
                gap: var(--mobile-space-4);
                text-align: center;
                padding: var(--mobile-space-4);
                margin-bottom: var(--mobile-space-6);
            }
            
            .page-title {
                font-size: 1.5rem;
                line-height: 1.3;
            }
            
            .user-info {
                flex-direction: column;
                gap: var(--mobile-space-3);
                font-size: 0.8rem;
            }
            
            .user-info span {
                justify-content: center;
                padding: var(--mobile-space-2) var(--mobile-space-3);
                min-height: 40px;
            }
            
            .logout-btn {
                padding: var(--mobile-space-3) var(--mobile-space-6);
                min-height: 44px;
                font-size: 0.9rem;
                justify-content: center;
                border-radius: var(--radius-lg);
            }

            /* 卡片移动端优化 */
            .card {
                padding: var(--mobile-space-4);
                margin-bottom: var(--mobile-space-4);
                border-radius: var(--radius-lg);
            }
            
            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--mobile-space-3);
                margin-bottom: var(--mobile-space-4);
                padding-bottom: var(--mobile-space-3);
            }
            
            .card-title {
                font-size: 1.125rem;
                gap: var(--mobile-space-2);
            }
            
            .card-title i {
                width: 20px;
                height: 20px;
                font-size: 0.75rem;
            }

            /* 统计卡片移动端单列布局 */
            .stats-grid {
                grid-template-columns: 1fr;
                gap: var(--mobile-space-4);
                margin-bottom: var(--mobile-space-6);
            }
            
            .stat-card {
                padding: var(--mobile-space-5);
                min-height: 140px;
            }
            
            .stat-icon {
                width: 40px;
                height: 40px;
                margin-bottom: var(--mobile-space-3);
            }
            
            .stat-value {
                font-size: 1.75rem;
                margin-bottom: var(--mobile-space-2);
            }
            
            .stat-label {
                font-size: 0.875rem;
            }

            /* 监控网格移动端优化 */
            .monitoring-grid {
                grid-template-columns: 1fr;
                gap: var(--mobile-space-4);
            }
            
            .monitoring-card {
                padding: var(--mobile-space-4);
            }
            
            .monitoring-header {
                gap: var(--mobile-space-2);
                margin-bottom: var(--mobile-space-3);
            }
            
            .monitoring-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }
            
            .monitoring-title {
                font-size: 0.95rem;
            }
            
            .monitoring-scrollable {
                max-height: 100px;
                font-size: 0.85rem;
            }

            /* 表格移动端处理 */
            .table-container {
                overflow-x: auto;
                border-radius: var(--radius-lg);
                -webkit-overflow-scrolling: touch;
            }
            
            .table {
                min-width: 600px; /* 确保表格内容不被压缩 */
            }
            
            .table th,
            .table td {
                padding: var(--mobile-space-3);
                font-size: 0.8rem;
                white-space: nowrap;
            }
            
            .table th {
                position: sticky;
                top: 0;
                background: var(--bg-secondary);
                z-index: 10;
            }

            /* 按钮移动端优化 */
            .btn {
                padding: var(--mobile-space-3) var(--mobile-space-5);
                font-size: 0.875rem;
                min-height: 44px;
                border-radius: var(--radius-lg);
                gap: var(--mobile-space-2);
            }
            
            .btn-sm {
                padding: var(--mobile-space-2) var(--mobile-space-4);
                font-size: 0.8rem;
                min-height: 40px;
            }
            
            /* 按钮组移动端堆叠 */
            .card-header .btn,
            .card-header .btn-group {
                width: 100%;
                justify-content: center;
            }

            /* 表单移动端优化 */
            .form-grid {
                grid-template-columns: 1fr;
                gap: var(--mobile-space-4);
            }
            
            .form-group {
                margin-bottom: var(--mobile-space-4);
            }
            
            .form-label {
                font-size: 0.9rem;
                margin-bottom: var(--mobile-space-2);
            }
            
            .form-input {
                padding: var(--mobile-space-4);
                font-size: 1rem; /* 防止iOS缩放 */
                min-height: 44px;
                border-radius: var(--radius-lg);
            }

            /* 模态框移动端适配 */
            .modal-content {
                width: 95%;
                margin: var(--mobile-space-4);
                max-height: 90vh;
                border-radius: var(--radius-lg);
            }
            
            .modal-header {
                padding: var(--mobile-space-4);
                flex-direction: column;
                gap: var(--mobile-space-2);
                text-align: center;
            }
            
            .modal-title {
                font-size: 1.125rem;
            }
            
            .close-btn {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
                position: absolute;
                top: var(--mobile-space-3);
                right: var(--mobile-space-3);
            }
            
            .modal-body {
                padding: var(--mobile-space-4);
                max-height: calc(90vh - 120px);
            }

            /* 备份设置移动端优化 */
            .backup-setting-grid {
                grid-template-columns: 1fr;
                gap: var(--mobile-space-3);
            }
            
            .backup-info-card {
                padding: var(--mobile-space-4);
                min-height: 120px;
            }
            
            .backup-info-icon {
                width: 36px;
                height: 36px;
                margin-bottom: var(--mobile-space-2);
            }
            
            .backup-info-value {
                font-size: 1.125rem;
            }
            
            .backup-info-label {
                font-size: 0.8rem;
            }

            /* 信息横幅移动端优化 */
            .info-banner {
                padding: var(--mobile-space-3);
                margin-top: var(--mobile-space-4);
                border-radius: var(--radius-lg);
            }
            
            .info-banner-content {
                font-size: 0.8rem;
                gap: var(--mobile-space-2);
                flex-direction: column;
                text-align: center;
            }

            /* 通知移动端适配 */
            .notification {
                top: var(--mobile-space-3);
                right: var(--mobile-space-3);
                left: var(--mobile-space-3);
                width: auto;
                min-width: auto;
                border-radius: var(--radius-lg);
                padding: var(--mobile-space-3);
                font-size: 0.875rem;
            }

            /* 状态指示器移动端优化 */
            .status-indicator {
                width: 10px;
                height: 10px;
                margin-right: var(--mobile-space-2);
            }
            
            /* 徽章移动端优化 */
            .badge {
                padding: var(--mobile-space-1) var(--mobile-space-2);
                font-size: 0.7rem;
                margin-right: var(--mobile-space-1);
                margin-bottom: var(--mobile-space-1);
            }

            /* 进度条移动端优化 */
            .progress-bar {
                height: 8px;
                margin-top: var(--mobile-space-2);
            }
        }

        /* 小屏移动设备进一步优化 */
        @media (max-width: 480px) {
            .main-content {
                padding: var(--mobile-space-3);
            }
            
            .card {
                padding: var(--mobile-space-3);
                margin-bottom: var(--mobile-space-3);
            }
            
            .header {
                padding: var(--mobile-space-3);
                margin-bottom: var(--mobile-space-4);
            }
            
            .page-title {
                font-size: 1.25rem;
            }
            
            .stats-grid {
                gap: var(--mobile-space-3);
            }
            
            .stat-card {
                padding: var(--mobile-space-4);
                min-height: 120px;
            }
            
            .stat-value {
                font-size: 1.5rem;
            }
            
            .monitoring-card {
                padding: var(--mobile-space-3);
            }
            
            .table th,
            .table td {
                padding: var(--mobile-space-2);
                font-size: 0.75rem;
            }
            
            .btn {
                padding: var(--mobile-space-2) var(--mobile-space-4);
                font-size: 0.8rem;
                min-height: 40px;
            }
        }

        /* 横屏移动设备优化 */
        @media (max-width: 768px) and (orientation: landscape) {
            .header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                text-align: left;
            }
            
            .user-info {
                flex-direction: row;
                gap: var(--mobile-space-2);
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .monitoring-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 触控设备特殊优化 */
        @media (hover: none) and (pointer: coarse) {
            .nav-link,
            .btn,
            .close-btn,
            .logout-btn {
                min-height: 44px; /* iOS推荐最小触控区域 */
            }
            
            .table th,
            .table td {
                min-height: 40px;
            }
            
            /* 移除悬停效果，避免触控设备粘滞 */
            .nav-link:hover,
            .btn:hover,
            .card:hover,
            .stat-card:hover {
                transform: none;
            }
            
            /* 保留点击反馈 */
            .nav-link:active,
            .btn:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
        }

        /* 特殊样式：动态数据高亮 */
        #cpuUsage, #memoryUsage, #onlineUsers, #totalReports,
        #load1, #load5, #load15, #currentTime, #lastUpdateTime, #serverUptime {
            color: var(--primary-400) !important;
            font-weight: 600 !important;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-primary);
            border-radius: 50%;
            border-top-color: var(--primary-500);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 增强可读性 */
        strong {
            color: var(--text-primary) !important;
            font-weight: 600;
        }

        /* 细节优化 */
        .card-header,
        .modal-header {
            border-bottom-color: var(--border-primary) !important;
        }

        /* 确保内容层级正确 */
        .stats-grid > div,
        .card > div:not(.card-header) {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h1>Rinwriter</h1>
                <div class="logo-subtitle">智能管理系统</div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        仪表板
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="users">
                        <i class="fas fa-users"></i>
                        用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="add-user">
                        <i class="fas fa-user-plus"></i>
                        添加用户
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="logs">
                        <i class="fas fa-file-alt"></i>
                        系统日志
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="backup">
                        <i class="fas fa-database"></i>
                        备份管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="security">
                        <i class="fas fa-shield-alt"></i>
                        安全监控
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        系统设置
                    </a>
                </li>
            </ul>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 头部 -->
            <div class="header">
                <h1 class="page-title" id="pageTitle">仪表板</h1>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> 管理员</span>
                    <span><i class="fas fa-clock"></i> <span id="currentTime"></span></span>
                    <a href="#" class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        退出
                    </a>
                </div>
            </div>

            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="stat-value" id="cpuUsage">45%</div>
                        <div class="stat-label">CPU 使用率</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-memory"></i>
                        </div>
                        <div class="stat-value" id="memoryUsage">62%</div>
                        <div class="stat-label">内存使用率</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 62%"></div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value" id="onlineUsers">8</div>
                        <div class="stat-label">在线用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-value" id="totalReports">0</div>
                        <div class="stat-label">总生成报告</div>
                    </div>
                </div>

                <!-- 实时监控 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-line"></i>
                            实时监控
                            <span id="lastUpdateTime" style="font-size: 0.8rem; color: var(--text-muted); margin-left: 1rem;"></span>
                        </div>
                        <button class="btn btn-primary btn-sm" onclick="refreshRealtimeStatus()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    
                    <!-- 重新设计的监控网格 -->
                    <div class="monitoring-grid">
                        <div class="monitoring-card">
                            <div class="monitoring-header">
                                <div class="monitoring-icon system">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                                <div class="monitoring-title">系统负载</div>
                            </div>
                            <div class="monitoring-content">
                                <div class="data-item">
                                    <span>1分钟</span>
                                    <span class="data-value" id="load1">0.45</span>
                                </div>
                                <div class="data-item">
                                    <span>5分钟</span>
                                    <span class="data-value" id="load5">0.38</span>
                                </div>
                                <div class="data-item">
                                    <span>15分钟</span>
                                    <span class="data-value" id="load15">0.42</span>
                                </div>
                            </div>
                        </div>

                        <div class="monitoring-card">
                            <div class="monitoring-header">
                                <div class="monitoring-icon users">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="monitoring-title">在线用户详情</div>
                            </div>
                            <div class="monitoring-content">
                                <div class="monitoring-scrollable" id="onlineUserDetails">
                                    加载中...
                                </div>
                            </div>
                        </div>

                        <div class="monitoring-card">
                            <div class="monitoring-header">
                                <div class="monitoring-icon errors">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="monitoring-title">最近错误</div>
                            </div>
                            <div class="monitoring-content">
                                <div class="monitoring-scrollable" id="recentErrors">
                                    加载中...
                                </div>
                            </div>
                        </div>

                        <div class="monitoring-card">
                            <div class="monitoring-header">
                                <div class="monitoring-icon sessions">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="monitoring-title">活跃会话</div>
                            </div>
                            <div class="monitoring-content">
                                <div class="monitoring-scrollable" id="activeSessionsDetails">
                                    加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 服务器信息横幅 -->
                    <div class="info-banner">
                        <div class="info-banner-content">
                            <i class="fas fa-info-circle info-banner-icon"></i>
                            <span>服务器运行时间: <span id="serverUptime">计算中...</span> | 自动刷新间隔: 10秒</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户管理页面 -->
            <div id="users" class="page">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-users"></i>
                            用户列表
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>最后登录IP</th>
                                    <th>最后登录时间</th>
                                    <th>权限</th>
                                    <th>报告额度</th>
                                    <th>已生成报告</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <tr>
                                    <td>rinch@</td>
                                    <td>*************</td>
                                    <td>2025-01-03 14:30</td>
                                    <td>
                                        <span class="badge badge-success">单项</span>
                                        <span class="badge badge-success">批量</span>
                                        <span class="badge badge-success">成果</span>
                                    </td>
                                    <td>
                                        <div>总计: 1000</div>
                                        <div>已用: 234</div>
                                        <div>剩余: 766</div>
                                    </td>
                                    <td>
                                        <span class="status-indicator status-online"></span>
                                        在线
                                    </td>
                                    <td>
                                        <button class="btn btn-primary btn-sm" onclick="editUser('rinch@')" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-warning btn-sm" onclick="viewLogs('rinch@')" title="查看日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteUser('rinch@')" title="删除用户">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>user001</td>
                                    <td>*************</td>
                                    <td>2025-01-03 10:15</td>
                                    <td>
                                        <span class="badge badge-success">单项</span>
                                        <span class="badge badge-warning">批量</span>
                                    </td>
                                    <td>
                                        <div>总计: 500</div>
                                        <div>已用: 87</div>
                                        <div>剩余: 413</div>
                                    </td>
                                    <td>
                                        <span class="status-indicator status-offline"></span>
                                        离线
                                    </td>
                                    <td>
                                        <button class="btn btn-primary btn-sm" onclick="editUser('user001')" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-warning btn-sm" onclick="viewLogs('user001')" title="查看日志">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteUser('user001')" title="删除用户">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 添加用户页面 -->
            <div id="add-user" class="page">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-user-plus"></i>
                            添加新用户
                        </div>
                    </div>
                    <form id="addUserForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" id="username" placeholder="输入用户名" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-input" id="password" placeholder="输入密码" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认密码</label>
                                <input type="password" class="form-input" id="confirmPassword" placeholder="确认密码" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">报告额度</label>
                                <input type="number" class="form-input" id="reportQuota" placeholder="设置报告生成数量" min="0" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">用户权限</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="permSingle" value="single">
                                    <label for="permSingle">单项报告生成</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="permBatch" value="batch">
                                    <label for="permBatch">批量报告生成</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="permAchievement" value="achievement">
                                    <label for="permAchievement">成果材料处理</label>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus"></i>
                            添加用户
                        </button>
                    </form>
                </div>
            </div>

            <!-- 系统日志页面 -->
            <div id="logs" class="page">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-file-alt"></i>
                            系统日志
                        </div>
                        <div class="log-tabs">
                            <button class="log-tab active" data-type="all">全部日志</button>
                            <button class="log-tab" data-type="security">安全日志</button>
                            <button class="log-tab" data-type="user">用户日志</button>
                            <button class="log-tab" data-type="system">系统日志</button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>错误信息</th>
                                    <th>IP地址</th>
                                    <th>类型</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                                <tr>
                                    <td>2025-01-03 14:25</td>
                                    <td>user001</td>
                                    <td>批量报告生成</td>
                                    <td>文件上传失败：文件格式不正确</td>
                                    <td>*************</td>
                                </tr>
                                <tr>
                                    <td>2025-01-03 13:15</td>
                                    <td>rinch@</td>
                                    <td>单项报告生成</td>
                                    <td>API调用超时</td>
                                    <td>*************</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 备份管理页面 -->
            <div id="backup" class="page">
                <!-- 备份设置信息 -->
                <div class="card" style="margin-bottom: 1.5rem;">
                    <div class="card-header">
                        <div class="card-subtitle">
                            <i class="fas fa-cog"></i>
                            自动备份设置
                        </div>
                    </div>
                    <div style="padding: 1rem;">
                        <div class="backup-setting-grid">
                            <div class="backup-info-card">
                                <div class="backup-info-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="backup-info-value">1小时</div>
                                <div class="backup-info-label">自动备份间隔</div>
                            </div>
                            <div class="backup-info-card">
                                <div class="backup-info-icon">
                                    <i class="fas fa-archive"></i>
                                </div>
                                <div class="backup-info-value">10个</div>
                                <div class="backup-info-label">保留备份数量</div>
                            </div>
                            <div class="backup-info-card">
                                <div class="backup-info-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="backup-info-value">启用</div>
                                <div class="backup-info-label">自动备份状态</div>
                            </div>
                        </div>
                        <div class="info-banner">
                            <div class="info-banner-content">
                                <i class="fas fa-info-circle info-banner-icon"></i>
                                <span>系统将每小时自动创建一次数据备份，并保留最近10次备份记录。您也可以随时手动创建备份。</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 备份文件管理 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-database"></i>
                            备份文件管理
                        </div>
                        <div style="display: flex; gap: 1rem;">
                            <button class="btn btn-success btn-sm" onclick="createBackup()">
                                <i class="fas fa-plus"></i>
                                创建备份
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="loadBackups()">
                                <i class="fas fa-refresh"></i>
                                刷新列表
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>备份文件名</th>
                                    <th>文件大小</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="backupTableBody">
                                <!-- 备份文件列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 安全监控页面 -->
            <div id="security" class="page">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-shield-alt"></i>
                            安全监控
                        </div>
                    </div>
                    <div class="stats-grid" style="margin-bottom: 2rem;">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-value" id="activeSessions">0</div>
                            <div class="stat-label">活跃会话</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-value" id="failedLogins">0</div>
                            <div class="stat-label">失败登录</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div class="stat-value" id="blockedIPs">0</div>
                            <div class="stat-label">被封IP</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="stat-value" id="apiRequests">0</div>
                            <div class="stat-label">API请求/分钟</div>
                        </div>
                    </div>
                    
                    <!-- 会话管理 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-clock"></i>
                                活跃会话管理
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>会话ID</th>
                                        <th>IP地址</th>
                                        <th>最后活动</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="sessionTableBody">
                                    <!-- 会话列表 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div id="settings" class="page">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-cog"></i>
                            系统设置
                        </div>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">系统名称</label>
                            <input type="text" class="form-input" id="systemName" value="Rinwriter" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">版本号</label>
                            <input type="text" class="form-input" id="systemVersion" value="1.0.0" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">最大并发用户</label>
                            <input type="number" class="form-input" id="maxConcurrentUsers" value="50">
                        </div>
                        <div class="form-group">
                            <label class="form-label">会话超时时间(分钟)</label>
                            <input type="number" class="form-input" id="sessionTimeout" value="30">
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="saveSystemSettings()">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑用户</h3>
                <button class="close-btn" onclick="closeModal('editUserModal')">&times;</button>
            </div>
            <form id="editUserForm">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" id="editUsername" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">新密码 (留空表示不修改)</label>
                    <input type="password" class="form-input" id="editPassword" placeholder="新密码">
                </div>
                <div class="form-group">
                    <label class="form-label">报告额度</label>
                    <input type="number" class="form-input" id="editReportQuota" min="0">
                </div>
                <div class="form-group">
                    <label class="form-label">用户权限</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="editPermSingle" value="single">
                            <label for="editPermSingle">单项报告生成</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="editPermBatch" value="batch">
                            <label for="editPermBatch">批量报告生成</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="editPermAchievement" value="achievement">
                            <label for="editPermAchievement">成果材料处理</label>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    保存修改
                </button>
            </form>
        </div>
    </div>

    <script>
        // 获取管理路径
        const ADMIN_PATH = '{{ admin_path or "admin" }}';
        
        // 所有API请求的基础URL
        const API_BASE = `/${ADMIN_PATH}/api`;
        
        // 更新所有API调用以使用动态路径
        function updateApiUrls() {
            // 系统状态API
            window.SYSTEM_STATS_URL = `${API_BASE}/system_stats`;
            window.USERS_API_URL = `${API_BASE}/users`;
            window.ADD_USER_URL = `${API_BASE}/add_user`;
            window.EDIT_USER_URL = `${API_BASE}/edit_user`;
            window.DELETE_USER_URL = `${API_BASE}/delete_user`;
            window.ERROR_LOGS_URL = `${API_BASE}/error_logs`;
            window.BACKUP_URL = `${API_BASE}/backup`;
            window.BACKUPS_URL = `${API_BASE}/backups`;
            window.RESTORE_URL = `${API_BASE}/restore`;
            window.SECURITY_STATS_URL = `${API_BASE}/security_stats`;
            window.ACTIVE_SESSIONS_URL = `${API_BASE}/active_sessions`;
            window.FORCE_LOGOUT_URL = `${API_BASE}/force_logout`;
            window.SYSTEM_SETTINGS_URL = `${API_BASE}/system_settings`;
            window.REALTIME_STATUS_URL = `${API_BASE}/realtime_status`;
        }
        
        // 页面加载时初始化API URLs
        updateApiUrls();

        // 全局API错误处理
        function handleApiError(error, operation = '操作') {
            console.error(`${operation}失败:`, error);
            
            // 检查是否是权限错误
            if (error.status === 401) {
                alert('管理员会话已过期，请重新登录');
                window.location.href = `/${ADMIN_PATH}/login`;
                return;
            }
            
            // 检查是否是网络错误
            if (!navigator.onLine) {
                showErrorMessage('网络连接已断开，请检查网络连接');
                return;
            }
            
            // 其他错误
            if (error.response) {
                error.response.json().then(data => {
                    showErrorMessage(`${operation}失败: ${data.error || '未知错误'}`);
                }).catch(() => {
                    showErrorMessage(`${operation}失败: 服务器响应异常`);
                });
            } else {
                showErrorMessage(`${operation}失败: 请检查网络连接或稍后重试`);
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            // 创建错误提示元素
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(220, 38, 38, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 400px;
                font-size: 14px;
                backdrop-filter: blur(10px);
            `;
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(34, 197, 94, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 400px;
                font-size: 14px;
                backdrop-filter: blur(10px);
            `;
            successDiv.textContent = message;
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 2000);
        }

        // 改进的fetch包装器
        function apiCall(url, options = {}) {
            return fetch(url, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            }).then(response => {
                if (!response.ok) {
                    throw { status: response.status, response: response };
                }
                return response.json();
            });
        }

        // 页面切换功能
        function switchPage(pageId, title) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新页面标题
            document.getElementById('pageTitle').textContent = title;
            
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
        }

        // 导航点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = link.getAttribute('data-page');
                const titles = {
                    'dashboard': '仪表板',
                    'users': '用户管理',
                    'add-user': '添加用户',
                    'logs': '系统日志',
                    'backup': '备份管理',
                    'security': '安全监控',
                    'settings': '系统设置'
                };
                switchPage(pageId, titles[pageId]);
                
                // 根据页面加载相应数据
                if (pageId === 'users') {
                    loadUsers();
                } else if (pageId === 'logs') {
                    loadErrorLogs();
                    initLogTabs();
                } else if (pageId === 'dashboard') {
                    refreshRealtimeStatus();
                } else if (pageId === 'settings') {
                    loadSystemSettings();
                }
            });
        });

        // 页面加载完成后初始化数据
        document.addEventListener('DOMContentLoaded', function() {
            // 初始加载仪表板数据
            updateStats();
            
            // 如果当前是用户管理页面，加载用户列表
            if (document.getElementById('users').classList.contains('active')) {
                loadUsers();
            }
        });

        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            document.getElementById('currentTime').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 实时数据更新
        function updateStats() {
            apiCall(window.SYSTEM_STATS_URL)
                .then(data => {
                    // CPU使用率
                    document.getElementById('cpuUsage').textContent = data.cpu_usage.toFixed(1) + '%';
                    document.querySelector('.stat-card .progress-fill').style.width = data.cpu_usage + '%';
                    
                    // 内存使用率
                    document.getElementById('memoryUsage').textContent = data.memory_usage.toFixed(1) + '%';
                    document.querySelectorAll('.stat-card .progress-fill')[1].style.width = data.memory_usage + '%';
                    
                    // 在线用户数
                    document.getElementById('onlineUsers').textContent = data.online_users;
                    
                    // 总生成报告 - 使用后端返回的数据
                    document.getElementById('totalReports').textContent = data.total_reports || 0;
                    
                    // 系统负载
                    document.getElementById('load1').textContent = data.load_1;
                    document.getElementById('load5').textContent = data.load_5;
                    document.getElementById('load15').textContent = data.load_15;
                })
                .catch(error => {
                    // 静默处理系统状态获取失败，避免频繁弹出错误
                    console.warn('获取系统状态失败，使用后备数据:', error);
                    // 使用较为合理的后备数据
                    document.getElementById('cpuUsage').textContent = '--';
                    document.getElementById('memoryUsage').textContent = '--';
                    document.getElementById('onlineUsers').textContent = '--';
                    document.getElementById('totalReports').textContent = '--';
                    document.getElementById('load1').textContent = '--';
                    document.getElementById('load5').textContent = '--';
                    document.getElementById('load15').textContent = '--';
                });
        }

        // 获取总报告数函数已移除，直接使用后端API数据

        // 每5秒更新一次统计数据
        updateStats();
        setInterval(updateStats, 5000);

        // 加载用户列表
        function loadUsers() {
            apiCall(window.USERS_API_URL)
                .then(users => {
                    const tbody = document.getElementById('userTableBody');
                    tbody.innerHTML = '';
                    
                    if (!users || users.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #a0aec0;">暂无用户数据</td></tr>';
                        return;
                    }
                    
                    users.forEach(user => {
                        const row = document.createElement('tr');
                        const statusBadge = user.status === 'online' 
                            ? '<span class="badge badge-success">在线</span>' 
                            : '<span class="badge badge-secondary">离线</span>';
                        
                        const permissionBadges = (user.permissions || []).map(perm => {
                            const permNames = {
                                'single': '单项',
                                'batch': '批量',
                                'achievement': '成果'
                            };
                            return `<span class="badge badge-primary">${permNames[perm] || perm}</span>`;
                        }).join(' ');
                        
                        row.innerHTML = `
                            <td>${user.username || ''}</td>
                            <td>${user.last_login_ip || '未知'}</td>
                            <td>${user.last_login_time || '从未登录'}</td>
                            <td>${permissionBadges}</td>
                            <td>${user.report_quota || 0}</td>
                            <td>${user.used_reports || 0}</td>
                            <td>${user.total_generated || 0}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <button class="btn btn-primary btn-sm" onclick="editUser('${user.username}')" title="编辑用户">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="clearUserReports('${user.username}')" title="清除已使用报告">
                                    <i class="fas fa-refresh"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.username}')" title="删除用户">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    handleApiError(error, '加载用户列表');
                    const tbody = document.getElementById('userTableBody');
                    tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #f87171;">加载用户数据失败，请刷新重试</td></tr>';
                });
        }

        // 添加用户表单提交
        document.getElementById('addUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const reportQuota = document.getElementById('reportQuota').value;
            
            // 简单验证
            if (password !== confirmPassword) {
                alert('密码不一致！');
                return;
            }
            
            // 获取权限
            const permissions = [];
            if (document.getElementById('permSingle').checked) permissions.push('single');
            if (document.getElementById('permBatch').checked) permissions.push('batch');
            if (document.getElementById('permAchievement').checked) permissions.push('achievement');
            
            // 发送到后端API
            fetch(window.ADD_USER_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    password,
                    report_quota: parseInt(reportQuota),
                    permissions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('用户添加成功！');
                    this.reset();
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert('添加失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('添加用户失败:', error);
                alert('添加用户失败，请稍后重试');
            });
        });

        // 编辑用户
        function editUser(username) {
            // 从后端获取用户数据
            fetch(`${API_BASE}/user/${username}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('editUsername').value = username;
                    document.getElementById('editReportQuota').value = data.report_quota;
                    
                    // 设置权限复选框
                    document.getElementById('editPermSingle').checked = data.permissions.includes('single');
                    document.getElementById('editPermBatch').checked = data.permissions.includes('batch');
                    document.getElementById('editPermAchievement').checked = data.permissions.includes('achievement');
                    
                    document.getElementById('editUserModal').classList.add('show');
                })
                .catch(error => {
                    console.error('获取用户数据失败:', error);
                    alert('获取用户数据失败');
                });
        }

        // 查看用户日志
        function viewLogs(username) {
            switchPage('logs', '系统日志');
            loadErrorLogs(); // 加载错误日志
        }

        // 清除用户已使用报告
        function clearUserReports(username) {
            if (confirm(`确定要清除用户 "${username}" 的已使用报告数量吗？此操作将重置该用户的已使用报告为0。`)) {
                fetch(`${API_BASE}/clear_user_reports`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadUsers(); // 重新加载用户列表
                        updateStats(); // 更新统计数据
                    } else {
                        alert('清除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('清除用户报告失败:', error);
                    alert('清除用户报告失败，请稍后重试');
                });
            }
        }

        // 删除用户
        function deleteUser(username) {
            if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
                fetch(window.DELETE_USER_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadUsers(); // 重新加载用户列表
                    } else {
                        alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('删除用户失败:', error);
                    alert('删除用户失败，请稍后重试');
                });
            }
        }

        // 加载错误日志
        function loadErrorLogs() {
            fetch(window.ERROR_LOGS_URL)
                .then(response => response.json())
                .then(logs => {
                    const tbody = document.getElementById('logsTableBody');
                    tbody.innerHTML = '';
                    
                    if (logs.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #a0aec0;">暂无日志记录</td></tr>';
                        return;
                    }
                    
                    logs.forEach(log => {
                        const row = document.createElement('tr');
                        const typeColor = {
                            'security': '#ef4444',
                            'user': '#3b82f6',
                            'system': '#f59e0b'
                        };
                        const typeBadge = log.type ? 
                            `<span style="background: ${typeColor[log.type] || '#6b7280'}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem;">${log.type}</span>` : '';
                        
                        row.innerHTML = `
                            <td>${log.time}</td>
                            <td>${log.username}</td>
                            <td>${log.operation}</td>
                            <td>${log.error}</td>
                            <td>${log.ip}</td>
                            <td>${typeBadge}</td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('加载日志失败:', error);
                });
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击模态框外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('show');
                }
            });
        });

        // 编辑用户表单提交
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('editUsername').value;
            const password = document.getElementById('editPassword').value;
            const reportQuota = document.getElementById('editReportQuota').value;
            
            // 获取权限
            const permissions = [];
            if (document.getElementById('editPermSingle').checked) permissions.push('single');
            if (document.getElementById('editPermBatch').checked) permissions.push('batch');
            if (document.getElementById('editPermAchievement').checked) permissions.push('achievement');
            
            // 准备更新数据
            const updateData = {
                username,
                permissions,
                report_quota: parseInt(reportQuota)
            };
            
            // 只有密码不为空时才更新密码
            if (password.trim()) {
                updateData.password = password;
            }
            
            // 发送到后端API
            fetch(window.EDIT_USER_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('用户信息更新成功！');
                    closeModal('editUserModal');
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert('更新失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('更新用户失败:', error);
                alert('更新用户失败，请稍后重试');
            });
        });

        // === 备份管理功能 ===
        function createBackup() {
            if (confirm('确定要创建新的备份吗？')) {
                fetch(window.BACKUP_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('备份创建成功！');
                        loadBackups(); // 刷新备份列表
                    } else {
                        alert('创建备份失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('创建备份失败:', error);
                    alert('创建备份失败，请稍后重试');
                });
            }
        }

        function loadBackups() {
            fetch(window.BACKUPS_URL)
                .then(response => response.json())
                .then(backups => {
                    const tbody = document.getElementById('backupTableBody');
                    tbody.innerHTML = '';
                    
                    if (backups.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #a0aec0;">暂无备份文件</td></tr>';
                        return;
                    }
                    
                    backups.forEach(backup => {
                        const row = document.createElement('tr');
                        const fileSize = (backup.size / 1024).toFixed(2) + ' KB';
                        
                        row.innerHTML = `
                            <td>${backup.filename}</td>
                            <td>${fileSize}</td>
                            <td>${backup.created}</td>
                            <td>
                                <button class="btn btn-primary btn-sm" onclick="restoreBackup('${backup.filename}')" title="恢复备份">
                                    <i class="fas fa-undo"></i>
                                    恢复
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('加载备份列表失败:', error);
                });
        }

        function restoreBackup(filename) {
            if (confirm(`确定要恢复备份 "${filename}" 吗？这将覆盖当前的用户数据！`)) {
                fetch(window.RESTORE_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ filename })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('数据恢复成功！页面将自动刷新。');
                        location.reload();
                    } else {
                        alert('恢复失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('恢复备份失败:', error);
                    alert('恢复备份失败，请稍后重试');
                });
            }
        }

                // === 安全监控功能 ===
        function loadSecurityStats() {
            // 加载安全统计数据
            fetch(window.SECURITY_STATS_URL)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('activeSessions').textContent = data.active_sessions || 0;
                    document.getElementById('failedLogins').textContent = data.failed_logins || 0;
                    document.getElementById('blockedIPs').textContent = data.blocked_ips || 0;
                    document.getElementById('apiRequests').textContent = data.api_requests || 0;
                })
                .catch(error => {
                    console.error('加载安全统计失败:', error);
                    // 模拟数据作为后备
                    document.getElementById('activeSessions').textContent = Math.floor(Math.random() * 10);
                    document.getElementById('failedLogins').textContent = Math.floor(Math.random() * 20);
                    document.getElementById('blockedIPs').textContent = Math.floor(Math.random() * 5);
                    document.getElementById('apiRequests').textContent = Math.floor(Math.random() * 100);
                });
            
            // 加载活跃会话列表
            loadActiveSessions();
        }

        function loadActiveSessions() {
            fetch(window.ACTIVE_SESSIONS_URL)
                .then(response => response.json())
                .then(sessions => {
                    const tbody = document.getElementById('sessionTableBody');
                    tbody.innerHTML = '';
                    
                    if (sessions.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #a0aec0;">暂无活跃会话</td></tr>';
                        return;
                    }
                    
                    sessions.forEach(session => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${session.username}</td>
                            <td><code>${session.session_id}</code></td>
                            <td>${session.ip}</td>
                            <td>${session.last_activity}</td>
                            <td>
                                <button class="btn btn-danger btn-sm" onclick="forceLogout('${session.session_id}')" title="强制下线">
                                    <i class="fas fa-sign-out-alt"></i>
                                    下线
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('加载活跃会话失败:', error);
                });
        }

        function forceLogout(sessionId) {
            if (confirm('确定要强制下线该用户吗？')) {
                fetch(window.FORCE_LOGOUT_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ session_id: sessionId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadActiveSessions(); // 刷新会话列表
                        loadSecurityStats(); // 刷新统计数据
                    } else {
                        alert('强制下线失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('强制下线失败:', error);
                    alert('强制下线失败，请稍后重试');
                });
            }
        }

        // 扩展页面加载逻辑
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = link.getAttribute('data-page');
                const titles = {
                    'dashboard': '仪表板',
                    'users': '用户管理',
                    'add-user': '添加用户',
                    'logs': '系统日志',
                    'backup': '备份管理',
                    'security': '安全监控',
                    'settings': '系统设置'
                };
                switchPage(pageId, titles[pageId]);
                
                // 根据页面加载相应数据
                if (pageId === 'users') {
                    loadUsers();
                } else if (pageId === 'logs') {
                    loadErrorLogs();
                } else if (pageId === 'backup') {
                    loadBackups();
                } else if (pageId === 'security') {
                    loadSecurityStats();
                }
            });
        });

        // 定期更新安全监控数据
        setInterval(function() {
            if (document.getElementById('security').classList.contains('active')) {
                loadSecurityStats();
            }
        }, 10000); // 每10秒更新一次

        // 管理员退出功能
        function logout() {
            if (confirm('确定要退出管理员界面吗？')) {
                window.location.href = `/${ADMIN_PATH}/logout`;
            }
        }

        // === 新增功能实现 ===

        // 1. 日志分类功能
        function initLogTabs() {
            document.querySelectorAll('.log-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活跃状态
                    document.querySelectorAll('.log-tab').forEach(t => t.classList.remove('active'));
                    // 添加当前活跃状态
                    this.classList.add('active');
                    // 加载对应类型的日志
                    const logType = this.getAttribute('data-type');
                    loadErrorLogs(logType);
                });
            });
        }

        // 修改loadErrorLogs函数支持分类
        function loadErrorLogs(type = 'all') {
            const url = type === 'all' ? window.ERROR_LOGS_URL : `${window.ERROR_LOGS_URL}?type=${type}`;
            
            fetch(url)
                .then(response => response.json())
                .then(logs => {
                    const tbody = document.getElementById('logsTableBody');
                    tbody.innerHTML = '';
                    
                    if (logs.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #a0aec0;">暂无日志记录</td></tr>';
                        return;
                    }
                    
                    logs.forEach(log => {
                        const row = document.createElement('tr');
                        const typeColor = {
                            'security': '#ef4444',
                            'user': '#3b82f6',
                            'system': '#f59e0b'
                        };
                        const typeBadge = log.type ? 
                            `<span style="background: ${typeColor[log.type] || '#6b7280'}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem;">${log.type}</span>` : '';
                        
                        row.innerHTML = `
                            <td>${log.time}</td>
                            <td>${log.username}</td>
                            <td>${log.operation}</td>
                            <td>${log.error}</td>
                            <td>${log.ip}</td>
                            <td>${typeBadge}</td>
                        `;
                        tbody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('加载日志失败:', error);
                });
        }

        // 2. 系统设置功能
        function loadSystemSettings() {
            fetch(window.SYSTEM_SETTINGS_URL)
                .then(response => response.json())
                .then(settings => {
                    document.getElementById('systemName').value = settings.system_name || 'Rinwriter';
                    document.getElementById('systemVersion').value = settings.version || '1.0.0';
                    document.getElementById('maxConcurrentUsers').value = settings.max_concurrent_users || 50;
                    document.getElementById('sessionTimeout').value = settings.session_timeout || 30;
                })
                .catch(error => {
                    console.error('加载系统设置失败:', error);
                });
        }

        function saveSystemSettings() {
            const settings = {
                system_name: document.getElementById('systemName').value,
                version: document.getElementById('systemVersion').value,
                max_concurrent_users: parseInt(document.getElementById('maxConcurrentUsers').value),
                session_timeout: parseInt(document.getElementById('sessionTimeout').value)
            };

            fetch(window.SYSTEM_SETTINGS_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('系统设置保存成功！');
                } else {
                    alert('保存失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('保存系统设置失败:', error);
                alert('保存系统设置失败，请稍后重试');
            });
        }

        // 3. 实时状态监控功能
        function refreshRealtimeStatus() {
            // 显示加载状态
            const lastUpdateElement = document.getElementById('lastUpdateTime');
            const originalText = lastUpdateElement.textContent;
            lastUpdateElement.textContent = '正在更新...';
            lastUpdateElement.style.color = '#f59e0b';
            
            fetch(window.REALTIME_STATUS_URL)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // 更新时间戳
                    lastUpdateElement.textContent = `最后更新: ${data.timestamp}`;
                    lastUpdateElement.style.color = '#10b981'; // 成功绿色
                    
                    // 更新在线用户详情
                    const onlineUserDetails = document.getElementById('onlineUserDetails');
                    if (data.online_users && data.online_users.length === 0) {
                        onlineUserDetails.innerHTML = '<span style="color: #6b7280;">暂无在线用户</span>';
                    } else if (data.online_users) {
                        onlineUserDetails.innerHTML = data.online_users.map(user => 
                            `<div style="margin-bottom: 4px;">${user.username} <span style="color: #6b7280;">(${user.used_reports}/${user.report_quota})</span></div>`
                        ).join('');
                    }
                    
                    // 更新最近错误
                    const recentErrors = document.getElementById('recentErrors');
                    if (data.recent_errors && data.recent_errors.length === 0) {
                        recentErrors.innerHTML = '<span style="color: #6b7280;">暂无错误记录</span>';
                    } else if (data.recent_errors) {
                        recentErrors.innerHTML = data.recent_errors.map(error => 
                            `<div style="margin-bottom: 4px; font-size: 0.9em;">${error.time.split(' ')[1]} <strong>${error.username}</strong>: ${error.error}</div>`
                        ).join('');
                    }
                    
                    // 更新活跃会话详情
                    const activeSessionsDetails = document.getElementById('activeSessionsDetails');
                    if (data.active_sessions && data.active_sessions.length === 0) {
                        activeSessionsDetails.innerHTML = '<span style="color: #6b7280;">暂无活跃会话</span>';
                    } else if (data.active_sessions) {
                        activeSessionsDetails.innerHTML = data.active_sessions.map(session => 
                            `<div style="margin-bottom: 4px;">${session.username} <span style="color: #6b7280;">(${session.duration}) [${session.type || '普通用户'}]</span></div>`
                        ).join('');
                    }
                    
                    // 更新服务器运行时间
                    const serverUptimeElement = document.getElementById('serverUptime');
                    if (serverUptimeElement && data.server_uptime) {
                        serverUptimeElement.textContent = data.server_uptime;
                    }
                })
                .catch(error => {
                    console.error('获取实时状态失败:', error);
                    lastUpdateElement.textContent = `更新失败 (${new Date().toLocaleTimeString()})`;
                    lastUpdateElement.style.color = '#ef4444'; // 错误红色
                    
                    // 显示错误详情（可选）
                    const errorDetails = error.message || '网络连接失败';
                    console.warn('实时状态更新错误详情:', errorDetails);
                    
                    // 在控制台显示更多调试信息
                    if (error.name === 'TypeError') {
                        console.warn('可能的原因：网络连接中断或服务器无响应');
                    }
                });
        }

        // 自动刷新实时状态（每10秒）
        setInterval(function() {
            if (document.getElementById('dashboard').classList.contains('active')) {
                refreshRealtimeStatus();
            }
        }, 10000);

        // 🚀 移动端功能增强
        
        // 移动端导航栏滚动优化
        function initMobileNavigation() {
            const navMenu = document.querySelector('.nav-menu');
            const activeNavLink = document.querySelector('.nav-link.active');
            
            // 确保活跃项在可视区域
            if (activeNavLink && navMenu && window.innerWidth <= 768) {
                setTimeout(() => {
                    activeNavLink.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center'
                    });
                }, 100);
            }
        }

        // 移动端表格滚动提示
        function initTableScrollHints() {
            const tableContainers = document.querySelectorAll('.table-container');
            
            tableContainers.forEach(container => {
                const table = container.querySelector('.table');
                if (!table) return;
                
                // 检查是否需要滚动
                function checkScrollable() {
                    const needsScroll = table.scrollWidth > container.clientWidth;
                    if (needsScroll && window.innerWidth <= 768) {
                        // 添加滚动提示
                        if (!container.querySelector('.scroll-hint')) {
                            const hint = document.createElement('div');
                            hint.className = 'scroll-hint';
                            hint.style.cssText = `
                                position: absolute;
                                top: 50%;
                                right: 8px;
                                transform: translateY(-50%);
                                background: var(--primary-500);
                                color: white;
                                padding: 4px 8px;
                                border-radius: 12px;
                                font-size: 0.7rem;
                                pointer-events: none;
                                opacity: 0.8;
                                animation: fadeInOut 3s ease-in-out;
                            `;
                            hint.textContent = '可左右滑动';
                            container.style.position = 'relative';
                            container.appendChild(hint);
                            
                            // 3秒后自动移除提示
                            setTimeout(() => {
                                if (hint.parentNode) {
                                    hint.remove();
                                }
                            }, 3000);
                        }
                    }
                }
                
                // 监听滚动事件移除提示
                container.addEventListener('scroll', () => {
                    const hint = container.querySelector('.scroll-hint');
                    if (hint) {
                        hint.remove();
                    }
                });
                
                checkScrollable();
                window.addEventListener('resize', checkScrollable);
            });
        }

        // 移动端触摸反馈
        function initTouchFeedback() {
            const touchElements = document.querySelectorAll('.btn, .nav-link, .card, .stat-card');
            
            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.opacity = '0.8';
                }, { passive: true });
                
                element.addEventListener('touchend', function() {
                    this.style.opacity = '';
                }, { passive: true });
                
                element.addEventListener('touchcancel', function() {
                    this.style.opacity = '';
                }, { passive: true });
            });
        }

        // 移动端模态框滚动锁定
        function initModalScrollLock() {
            const modals = document.querySelectorAll('.modal');
            
            modals.forEach(modal => {
                modal.addEventListener('show', () => {
                    if (window.innerWidth <= 768) {
                        document.body.style.overflow = 'hidden';
                        document.body.style.position = 'fixed';
                        document.body.style.width = '100%';
                    }
                });
                
                modal.addEventListener('hide', () => {
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                });
            });
        }

        // 移动端键盘适配
        function initKeyboardAdaptation() {
            if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
                const inputs = document.querySelectorAll('input, textarea');
                
                inputs.forEach(input => {
                    input.addEventListener('focus', () => {
                        setTimeout(() => {
                            input.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }, 300);
                    });
                });
            }
        }

        // 移动端下拉刷新（仅在顶部）
        function initPullToRefresh() {
            if (window.innerWidth <= 768) {
                let startY = 0;
                let currentY = 0;
                let pullDistance = 0;
                let pulling = false;
                
                const mainContent = document.querySelector('.main-content');
                
                mainContent.addEventListener('touchstart', (e) => {
                    if (mainContent.scrollTop === 0) {
                        startY = e.touches[0].clientY;
                        pulling = true;
                    }
                }, { passive: true });
                
                mainContent.addEventListener('touchmove', (e) => {
                    if (!pulling) return;
                    
                    currentY = e.touches[0].clientY;
                    pullDistance = currentY - startY;
                    
                    if (pullDistance > 80 && mainContent.scrollTop === 0) {
                        // 显示刷新提示
                        if (!document.querySelector('.pull-refresh-hint')) {
                            const hint = document.createElement('div');
                            hint.className = 'pull-refresh-hint';
                            hint.style.cssText = `
                                position: fixed;
                                top: 80px;
                                left: 50%;
                                transform: translateX(-50%);
                                background: var(--primary-500);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                font-size: 0.8rem;
                                z-index: 1000;
                            `;
                            hint.textContent = '松开刷新';
                            document.body.appendChild(hint);
                        }
                    }
                }, { passive: true });
                
                mainContent.addEventListener('touchend', () => {
                    const hint = document.querySelector('.pull-refresh-hint');
                    if (hint) {
                        hint.remove();
                        if (pullDistance > 80) {
                            // 执行刷新
                            location.reload();
                        }
                    }
                    pulling = false;
                    pullDistance = 0;
                });
            }
        }

        // CSS动画定义
        const mobileStyles = document.createElement('style');
        mobileStyles.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translateY(-50%) scale(0.8); }
                20% { opacity: 1; transform: translateY(-50%) scale(1); }
                80% { opacity: 1; transform: translateY(-50%) scale(1); }
                100% { opacity: 0; transform: translateY(-50%) scale(0.8); }
            }
            
            /* 移动端专用滚动条 */
            @media (max-width: 768px) {
                .nav-menu::-webkit-scrollbar,
                .table-container::-webkit-scrollbar,
                .monitoring-scrollable::-webkit-scrollbar {
                    height: 2px;
                    width: 2px;
                }
                
                .nav-menu::-webkit-scrollbar-thumb,
                .table-container::-webkit-scrollbar-thumb,
                .monitoring-scrollable::-webkit-scrollbar-thumb {
                    background: var(--primary-400);
                    border-radius: 2px;
                }
                
                .nav-menu::-webkit-scrollbar-track,
                .table-container::-webkit-scrollbar-track,
                .monitoring-scrollable::-webkit-scrollbar-track {
                    background: transparent;
                }
            }
        `;
        document.head.appendChild(mobileStyles);

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果当前在仪表板页面，立即加载实时状态
            if (document.getElementById('dashboard').classList.contains('active')) {
                refreshRealtimeStatus();
            }
            
            // 初始化移动端功能
            if (window.innerWidth <= 768) {
                initMobileNavigation();
                initTableScrollHints();
                initTouchFeedback();
                initModalScrollLock();
                initKeyboardAdaptation();
                initPullToRefresh();
            }
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                initMobileNavigation();
                initTableScrollHints();
            }
        });
    </script>
</body>
</html> 