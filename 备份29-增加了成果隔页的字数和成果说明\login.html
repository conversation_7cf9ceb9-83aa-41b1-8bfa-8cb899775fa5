<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rinwriter - 登录</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* From Uiverse.io by g<PERSON><PERSON><PERSON><PERSON><PERSON> */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 添加动态背景效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.15) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }

        /* 添加网格背景 */
        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 30s linear infinite;
            opacity: 0.4;
        }

        @keyframes backgroundShift {
            0%, 100% {
                transform: translateX(0) translateY(0);
            }
            25% {
                transform: translateX(-20px) translateY(-10px);
            }
            50% {
                transform: translateX(20px) translateY(20px);
            }
            75% {
                transform: translateX(-10px) translateY(10px);
            }
        }

        @keyframes gridMove {
            0% {
                transform: translate(0, 0);
            }
            100% {
                transform: translate(50px, 50px);
            }
        }

        /* 添加浮动粒子效果 */
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            pointer-events: none;
            animation: float 15s infinite linear;
        }

        .particle:nth-child(1) {
            width: 4px;
            height: 4px;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            width: 6px;
            height: 6px;
            left: 20%;
            animation-delay: 2s;
        }

        .particle:nth-child(3) {
            width: 3px;
            height: 3px;
            left: 30%;
            animation-delay: 4s;
        }

        .particle:nth-child(4) {
            width: 5px;
            height: 5px;
            left: 40%;
            animation-delay: 6s;
        }

        .particle:nth-child(5) {
            width: 4px;
            height: 4px;
            left: 50%;
            animation-delay: 8s;
        }

        .particle:nth-child(6) {
            width: 6px;
            height: 6px;
            left: 60%;
            animation-delay: 10s;
        }

        .particle:nth-child(7) {
            width: 3px;
            height: 3px;
            left: 70%;
            animation-delay: 12s;
        }

        .particle:nth-child(8) {
            width: 5px;
            height: 5px;
            left: 80%;
            animation-delay: 14s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .login-box {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 350px;
            padding: 35px;
            transform: translate(-50%, -50%);
            background: rgba(15, 15, 25, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-sizing: border-box;
            box-shadow:
                0 25px 45px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 0 50px rgba(3, 244, 15, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 10;
        }

        .login-header {
            position: relative;
            margin-bottom: 30px;
        }

        .login-box h2 {
            margin: 0;
            padding: 0;
            color: #fff;
            text-align: center;
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 700;
        }

        .help-icon {
            position: absolute;
            top: 0;
            right: 0;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .help-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .help-icon:hover {
            background: rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .help-icon:hover::before {
            left: 100%;
        }

        .help-icon i {
            color: #fff;
            font-size: 16px;
            z-index: 1;
        }

        .login-box .user-box {
            position: relative;
        }

        .login-box .user-box input {
            width: 100%;
            padding: 10px 0;
            font-size: 16px;
            color: #fff !important;
            margin-bottom: 30px;
            border: none;
            border-bottom: 1px solid #fff;
            outline: none;
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: #fff !important;
        }

        .login-box .user-box input:focus {
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
        }

        .login-box .user-box input:valid {
            background: transparent !important;
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
        }

        .login-box .user-box input:-webkit-autofill,
        .login-box .user-box input:-webkit-autofill:hover,
        .login-box .user-box input:-webkit-autofill:focus,
        .login-box .user-box input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: #fff !important;
            background: transparent !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        .login-box .user-box label {
            position: absolute;
            top: 0;
            left: 0;
            padding: 10px 0;
            font-size: 16px;
            color: #fff;
            pointer-events: none;
            transition: .5s;
        }

        .login-box .user-box input:focus ~ label,
        .login-box .user-box input:valid ~ label {
            top: -20px;
            left: 0;
            color: #bdb8b8;
            font-size: 12px;
        }

        .login-box form button {
            position: relative;
            display: block;
            padding: 10px 20px;
            background: transparent;
            color: #ffffff;
            font-size: 16px;
            text-decoration: none;
            text-transform: uppercase;
            overflow: hidden;
            transition: .5s;
            margin: 40px auto 0;
            letter-spacing: 4px;
            border: none;
            cursor: pointer;
            width: fit-content;
        }

        .login-box button:hover {
            background: #03f40f;
            color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 5px #03f40f,
                        0 0 25px #03f40f,
                        0 0 50px #03f40f,
                        0 0 100px #03f40f;
        }

        .login-box button span {
            position: absolute;
            display: block;
        }

        @keyframes btn-anim1 {
            0% {
                left: -100%;
            }
            50%,100% {
                left: 100%;
            }
        }

        .login-box button span:nth-child(1) {
            bottom: 2px;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #03f40f);
            animation: btn-anim1 2s linear infinite;
        }

        @keyframes btn-anim2 {
            0% {
                top: -100%;
            }
            50%,100% {
                top: 100%;
            }
        }

        .login-box button span:nth-child(2) {
            right: 0;
            top: -100%;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, transparent, #03f40f);
            animation: btn-anim2 2s linear infinite;
            animation-delay: .5s;
        }

        @keyframes btn-anim3 {
            0% {
                right: -100%;
            }
            50%,100% {
                right: 100%;
            }
        }

        .login-box button span:nth-child(3) {
            bottom: 0;
            right: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(270deg, transparent, #03f40f);
            animation: btn-anim3 2s linear infinite;
            animation-delay: 1s;
        }

        @keyframes btn-anim4 {
            0% {
                bottom: -100%;
            }
            50%,100% {
                bottom: 100%;
            }
        }

        .login-box button span:nth-child(4) {
            left: 0;
            bottom: -100%;
            width: 2px;
            height: 100%;
            background: linear-gradient(360deg, transparent, #03f40f);
            animation: btn-anim4 2s linear infinite;
            animation-delay: 1.5s;
        }

        .error-message {
            color: #fff;
            background-color: rgba(239, 68, 68, 0.7);
            border-radius: 4px;
            padding: 10px 12px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
            position: relative;
            text-align: center;
        }

        .error-message.show {
            display: block;
        }

        .error-message::before {
            content: '⚠️';
            margin-right: 8px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8rem;
        }

        /* 现代透明模态框设计 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            animation: fadeIn 0.3s ease-out;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-content {
            width: 90%;
            max-width: 680px;
            max-height: 80vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            overflow: hidden;
            animation: slideUp 0.4s ease-out;
        }

        .modal-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .modal-title i {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.25rem;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .close-btn i {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .modal-body {
            padding: 1.5rem 2rem 2rem;
            max-height: calc(80vh - 100px);
            overflow-y: auto;
        }

        .module {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .module:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .module-header {
            padding: 1rem 1.25rem 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .module-title {
            font-size: 1rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .module-title i {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .module-body {
            padding: 1.25rem;
        }

        .section {
            margin-bottom: 1.25rem;
        }

        .section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 0.85rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 0.5rem 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .file-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .file-tag {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 6px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .file-tag.required {
            background: rgba(34, 197, 94, 0.15);
            border-color: rgba(34, 197, 94, 0.3);
            color: rgba(34, 197, 94, 1);
        }

        .file-tag.optional {
            background: rgba(249, 115, 22, 0.15);
            border-color: rgba(249, 115, 22, 0.3);
            color: rgba(249, 115, 22, 1);
        }

        .info-text {
            font-size: 0.8rem;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .output-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .output-list li {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0.25rem;
            padding-left: 1rem;
            position: relative;
        }

        .output-list li:before {
            content: '•';
            color: rgba(255, 255, 255, 0.5);
            position: absolute;
            left: 0;
        }

        /* 自定义滚动条 */
        .modal-body::-webkit-scrollbar {
            width: 4px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-height: 90vh;
            }

            .modal-header {
                padding: 1rem;
            }

            .modal-title {
                font-size: 1.2rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .file-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动粒子效果 -->
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>

    <div class="login-box">
        <div class="login-header">
            <h2>Rinwriter</h2>
            <div class="help-icon" onclick="showModal()">
                <i class="fas fa-question"></i>
            </div>
        </div>

        <div class="error-message" id="error-message">
            <div id="error-text">用户名或密码错误，请重试。</div>
            <div id="attempts-info" style="font-size: 0.8rem; margin-top: 5px; opacity: 0.8;"></div>
        </div>

        <form id="login-form" action="/login" method="post">
            <div class="user-box">
                <input type="text" id="username" name="username" required>
                <label>用户名</label>
            </div>
            <div class="user-box">
                <input type="password" id="password" name="password" required>
                <label>密码</label>
            </div>
            <button type="submit">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                登录
            </button>
        </form>

        <div class="footer">
            © 2025 Rinwriter - by 联 微信：116383932 | 版本 1.0
        </div>
    </div>

    <!-- 使用说明模态框 -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-book-open"></i>
                    使用必看
                </div>
                <div class="close-btn" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </div>
            </div>

            <div class="modal-body">
                <!-- 单个报告生成 -->
                <div class="module">
                    <div class="module-header">
                        <div class="module-title">
                            <i class="fas fa-file-alt"></i>
                            单个报告生成
                        </div>
                    </div>
                    <div class="module-body">
                        <div class="section">
                            <div class="section-title">需要文件</div>
                            <div class="file-tags">
                                <div class="file-tag required">技术文件 (必需)</div>
                                <div class="file-tag required">人员信息表 (必需)</div>
                                <div class="file-tag optional">设备信息表 (可选)</div>
                                <div class="file-tag optional">RDPS表 (可选)</div>
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">填写信息</div>
                            <p class="info-text">公司名称、项目名称、起止日期、RD序号为必填项目编号和验收编号为可选</p>
                        </div>

                        <div class="section">
                            <div class="section-title">生成文档</div>
                            <ul class="output-list">
                                <li>研发项目计划书.docx</li>
                                <li>项目验收报告.docx</li>
                                <li>RD_PS统计表.xlsx</li>
                                <li>项目立项通知.docx</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 批量报告生成 -->
                <div class="module">
                    <div class="module-header">
                        <div class="module-title">
                            <i class="fas fa-layer-group"></i>
                            批量报告生成
                        </div>
                    </div>
                    <div class="module-body">
                        <div class="section">
                            <div class="section-title">操作步骤</div>
                            <p class="info-text">1. 点击"解析RDPS表"上传Excel格式的RDPS表<br>
                            2. 批量上传人员信息表和对应技术文件<br>
                            3. 点击开始批量生成，系统自动处理所有项目</p>
                        </div>

                        <div class="section">
                            <div class="section-title">实时进度</div>
                            <p class="info-text">系统会实时显示处理进度和状态信息</p>
                        </div>
                    </div>
                </div>

                <!-- 成果转化处理 -->
                <div class="module">
                    <div class="module-header">
                        <div class="module-title">
                            <i class="fas fa-trophy"></i>
                            成果转化处理
                        </div>
                    </div>
                    <div class="module-body">
                        <div class="section">
                            <div class="section-title">需要文件</div>
                            <div class="file-tags">
                                <div class="file-tag required">研发诊断表 (必需)</div>
                                <div class="file-tag optional">技术文件ZIP (可选)</div>
                                <div class="file-tag optional">专利证书ZIP (可选)</div>
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">生成文档</div>
                            <ul class="output-list">
                                <li>成果材料统计表.xlsx</li>
                                <li>成果材料汇总.docx</li>
                                <li>成果材料分页.pdf</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 使用技巧 -->
                <div class="module">
                    <div class="module-header">
                        <div class="module-title">
                            <i class="fas fa-lightbulb"></i>
                            使用技巧
                        </div>
                    </div>
                    <div class="module-body">
                        <p class="info-text">• 局域网环境下自动保存到本地，外网环境自动ZIP下载<br>
                        • 系统会实时显示处理进度和状态信息<br>
                        • 批量处理建议在网络稳定时使用，大项目可分批处理</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示模态框
        function showModal() {
            const modal = document.getElementById('helpModal');
            modal.classList.add('show');
        }

        // 隐藏模态框
        function hideModal() {
            const modal = document.getElementById('helpModal');
            modal.classList.remove('show');
        }

        // 点击模态框外部关闭
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideModal();
            }
        });

        // 检查URL参数是否有错误信息
        document.addEventListener('DOMContentLoaded', function() {
            // 快速检查是否有错误参数
            if (window.location.search.includes('error')) {
                const errorMessage = document.getElementById('error-message');
                const errorText = document.getElementById('error-text');
                const attemptsInfo = document.getElementById('attempts-info');
                
                // 解析URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const attempts = urlParams.get('attempts');
                const remaining = urlParams.get('remaining');
                
                // 设置错误信息
                if (attempts !== null && remaining !== null) {
                    const attemptsNum = parseInt(attempts);
                    const remainingNum = parseInt(remaining);
                    
                    if (remainingNum > 0) {
                        errorText.textContent = '用户名或密码错误，请重试。';
                        attemptsInfo.textContent = `已失败 ${attemptsNum} 次，还有 ${remainingNum} 次尝试机会（最多5次）`;
                        
                        // 根据剩余次数改变颜色
                        if (remainingNum <= 2) {
                            attemptsInfo.style.color = '#ff6b6b';
                            attemptsInfo.style.fontWeight = 'bold';
                        } else if (remainingNum <= 3) {
                            attemptsInfo.style.color = '#ffb347';
                        }
                    } else {
                        errorText.textContent = '登录失败次数过多，请15分钟后重试。';
                        attemptsInfo.textContent = '为了账户安全，暂时禁止登录';
                        attemptsInfo.style.color = '#ff6b6b';
                        attemptsInfo.style.fontWeight = 'bold';
                    }
                } else {
                    errorText.textContent = '用户名或密码错误，请重试。';
                    attemptsInfo.textContent = '';
                }
                
                errorMessage.classList.add('show');

                // 5秒后自动隐藏错误信息（延长到5秒让用户看清楚）
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 5000);
            }
        });
    </script>
</body>
</html>


